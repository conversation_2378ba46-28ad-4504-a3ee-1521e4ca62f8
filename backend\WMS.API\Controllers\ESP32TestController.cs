using Microsoft.AspNetCore.Mvc;
using WMS.API.DTOs.ESP32;
using WMS.API.Models;
using WMS.API.Services;

namespace WMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ESP32TestController : ControllerBase
    {
        private readonly IESP32CommunicationService _communicationService;
        private readonly ILogger<ESP32TestController> _logger;

        public ESP32TestController(
            IESP32CommunicationService communicationService,
            ILogger<ESP32TestController> logger
        )
        {
            _communicationService = communicationService;
            _logger = logger;
        }

        [HttpPost("test-connection")]
        public async Task<ActionResult<object>> TestConnection(
            [FromBody] TestConnectionRequest request
        )
        {
            var controller = new ESP32Controller
            {
                Name = "Test Controller",
                IpAddress = request.IpAddress,
                Port = request.Port,
            };

            var isOnline = await _communicationService.TestConnectionAsync(controller);

            return Ok(
                new
                {
                    IpAddress = request.IpAddress,
                    Port = request.Port,
                    IsOnline = isOnline,
                    Message = isOnline ? "连接成功" : "连接失败",
                    Timestamp = DateTime.UtcNow,
                }
            );
        }

        [HttpPost("get-device-info")]
        public async Task<ActionResult<object>> GetDeviceInfo(
            [FromBody] TestConnectionRequest request
        )
        {
            var controller = new ESP32Controller
            {
                Name = "Test Controller",
                IpAddress = request.IpAddress,
                Port = request.Port,
            };

            var deviceInfo = await _communicationService.GetDeviceInfoAsync(controller);

            if (deviceInfo == null)
            {
                return BadRequest(new { Message = "无法获取设备信息，请检查网络连接" });
            }

            return Ok(deviceInfo);
        }

        [HttpPost("set-light")]
        public async Task<ActionResult<object>> SetLight([FromBody] SetLightTestRequest request)
        {
            var controller = new ESP32Controller
            {
                Name = "Test Controller",
                IpAddress = request.IpAddress,
                Port = request.Port,
            };

            var success = await _communicationService.SetLightAsync(
                controller,
                0, // 默认使用通道0
                request.StartPosition,
                request.EndPosition,
                request.Color,
                request.Brightness
            );

            return Ok(
                new
                {
                    Success = success,
                    Message = success ? "灯光控制成功" : "灯光控制失败",
                    Parameters = new
                    {
                        request.IpAddress,
                        request.Port,
                        request.StartPosition,
                        request.EndPosition,
                        request.Color,
                        request.Brightness,
                    },
                }
            );
        }

        [HttpPost("turn-off-all")]
        public async Task<ActionResult<object>> TurnOffAll([FromBody] TurnOffAllRequest request)
        {
            var controller = new ESP32Controller
            {
                Name = "Test Controller",
                IpAddress = request.IpAddress,
                Port = request.Port,
            };

            var success = await _communicationService.TurnOffAllLightsAsync(
                controller,
                request.Channel
            );

            return Ok(
                new
                {
                    Success = success,
                    Message = success ? "关闭所有灯光成功" : "关闭所有灯光失败",
                    Parameters = new
                    {
                        request.IpAddress,
                        request.Port,
                        request.Channel,
                    },
                }
            );
        }

        [HttpPost("set-all-lights")]
        public async Task<ActionResult<object>> SetAllLights([FromBody] SetAllLightsRequest request)
        {
            var controller = new ESP32Controller
            {
                Name = "Test Controller",
                IpAddress = request.IpAddress,
                Port = request.Port,
            };

            var success = await _communicationService.SetAllLightsAsync(
                controller,
                request.Channel,
                request.Color,
                request.Brightness
            );

            return Ok(
                new
                {
                    Success = success,
                    Message = success ? "设置所有灯光成功" : "设置所有灯光失败",
                    Parameters = new
                    {
                        request.IpAddress,
                        request.Port,
                        request.Channel,
                        request.Color,
                        request.Brightness,
                    },
                }
            );
        }

        [HttpPost("control-big-led")]
        public async Task<ActionResult<object>> ControlBigLed(
            [FromBody] ControlBigLedTestRequest request
        )
        {
            var controller = new ESP32Controller
            {
                Name = "Test Controller",
                IpAddress = request.IpAddress,
                Port = request.Port,
            };

            var success = await _communicationService.ControlBigLedAsync(
                controller,
                request.IndicatorIndex,
                request.TurnOn
            );

            return Ok(
                new
                {
                    Success = success,
                    Message = success
                        ? $"大指示灯控制成功 - 指示灯 {request.IndicatorIndex} 已{(request.TurnOn ? "开启" : "关闭")}"
                        : "大指示灯控制失败",
                    Parameters = new
                    {
                        request.IpAddress,
                        request.Port,
                        request.IndicatorIndex,
                        request.TurnOn,
                    },
                }
            );
        }
    }

    public class TestConnectionRequest
    {
        public string IpAddress { get; set; } = "*************";
        public int Port { get; set; } = 80;
    }

    public class SetLightTestRequest : TestConnectionRequest
    {
        public int StartPosition { get; set; } = 0;
        public int EndPosition { get; set; } = 9;
        public string Color { get; set; } = "red";
        public int Brightness { get; set; } = 255;
    }

    public class TurnOffAllRequest : TestConnectionRequest
    {
        public int Channel { get; set; } = 0;
    }

    public class SetAllLightsRequest : TestConnectionRequest
    {
        public int Channel { get; set; } = 0;
        public string Color { get; set; } = "white";
        public int Brightness { get; set; } = 255;
    }

    public class ControlBigLedTestRequest : TestConnectionRequest
    {
        public int IndicatorIndex { get; set; } = 0;
        public bool TurnOn { get; set; } = true;
    }
}
