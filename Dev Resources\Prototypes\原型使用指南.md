# WMS-VGL 高保真原型使用指南

## 概述

本文档为 WMS-VGL (Visual Guided Logistics) 智能仓储管理系统的高保真原型使用指南。该原型是一个完整的可交互HTML应用，模拟了真实系统的所有核心功能和用户界面。

⚠️ **2024年1月20日更新**: 已修复所有页面访问问题，现在所有侧边栏菜单都可以正常点击进入对应界面！

## 系统架构

### 四级层级结构
```
🏭 Warehouse (仓库)
└── 🏢 Zone (区域) 
    └── 📚 Shelf (货架)
        └── 📦 StorageLocation (储位)
```

### 核心模块
- **仓库管理**: 四级层级管理（仓库→区域→货架→储位）
- **库存管理**: 实时库存查询、状态跟踪
- **作业管理**: 入库、出库、移库、拣货、盘点作业
- **硬件管理**: ESP32设备管理和LED控制

## 使用说明

### 1. 登录系统

#### 快速体验
1. 打开 `WMS-VGL-完整原型系统.html` 文件
2. 点击 **"快速登录"** 按钮（推荐）
3. 或者手动输入：
   - 邮箱：`<EMAIL>`
   - 密码：`Admin@123456`

#### 演示账号
- **超级管理员**: `<EMAIL>` / `Admin@123456`
- **仓库经理**: `<EMAIL>` / `Manager@123456`
- **操作员**: `<EMAIL>` / `Operator@123456`

### 2. 系统导航

#### 主导航栏
- **首页**: 系统概览和快速操作入口
- **仓库管理**: 四级层级结构管理
- **库存管理**: 库存查询和快速作业
- **作业管理**: 入库、出库、移库等作业操作
- **硬件管理**: ESP32设备和LED控制
- **用户管理**: 用户账号和权限管理

#### 侧边栏菜单
每个模块都有对应的侧边栏子菜单，**所有菜单项都可以点击**：

**仓库管理模块：**
- ✅ 仓库列表：查看和管理所有仓库
- ✅ 区域管理：管理仓库内的功能区域
- ✅ 货架管理：管理区域内的物理货架
- ✅ 储位管理：管理最细粒度的储存单位

**库存管理模块：**
- ✅ 库存查询：实时查看库存状态
- ✅ 库存统计：库存分类统计和分析
- ✅ 库存预警：库存异常预警和处理

**作业管理模块：**
- ✅ 入库作业：PDA优化的入库操作界面
- ✅ 出库作业：PDA优化的出库操作界面
- ✅ 移库作业：库内移库操作界面
- ✅ 拣货作业：完整的拣货工作流程
- ✅ 盘点作业：库存盘点操作界面

**硬件管理模块：**
- ✅ ESP32设备：设备列表和管理
- ✅ 设备监控：实时监控设备状态和性能
- ✅ LED控制：集中控制LED灯带测试

**用户管理模块：**
- ✅ 用户管理：用户账号管理
- ✅ 角色权限：角色和权限分配
- ✅ 操作日志：系统操作记录和审计

### 3. 仓库管理模块

#### 3.1 仓库列表
- **功能**: 查看所有仓库信息
- **操作**: 新增、编辑、删除仓库
- **导航**: 点击 "查看区域" 进入下级管理

#### 3.2 区域管理
- **功能**: 管理仓库内的功能区域
- **特点**: 支持不同区域类型（常温、冷藏、冷冻、危险品）
- **导航**: 从仓库列表点击进入，可继续向下查看货架

#### 3.3 货架管理
- **功能**: 管理区域内的物理货架
- **信息**: 货架规格、承重、储位数量
- **导航**: 从区域管理点击进入，可继续查看储位

#### 3.4 储位管理
- **功能**: 管理最细粒度的储存单位
- **特点**: 绑定ESP32设备和LED位置
- **操作**: 点亮储位功能测试

### 4. 库存管理模块

#### 4.1 库存查询
- **实时数据**: 显示当前库存状态
- **搜索功能**: 支持条码扫描和手动输入
- **状态管理**: 合格、待检、冻结状态

#### 4.2 快速操作
- **快速入库**: 一键跳转入库作业
- **快速出库**: 一键跳转出库作业  
- **库内移库**: 一键跳转移库作业

#### 4.3 条码扫描
- **模拟扫码**: 点击 "扫码" 按钮模拟扫描
- **自动识别**: 系统自动识别物料信息
- **支持类型**: 物料码、储位码、LPN码

### 5. 作业管理模块

#### 5.1 入库作业
1. **扫描物料**: 使用扫码功能或手动输入物料编码
2. **填写信息**: 入库数量、批次号、有效期等
3. **获取推荐**: 点击 "获取储位推荐" 查看推荐储位
4. **选择储位**: 从推荐列表中选择合适储位
5. **LED引导**: 系统自动点亮目标储位LED

#### 5.2 出库作业
1. **扫描物料**: 输入要出库的物料编码
2. **确认数量**: 输入出库数量
3. **扫描储位**: 扫描储位码确认位置
4. **执行出库**: 系统点亮储位LED并更新库存

#### 5.3 移库作业
1. **扫描源储位**: 输入当前储位编码
2. **扫描目标储位**: 输入目标储位编码
3. **确认信息**: 系统自动识别物料信息
4. **执行移库**: 同时点亮源储位和目标储位LED

### 6. 硬件管理模块

#### 6.1 设备列表
- **设备状态**: 显示所有ESP32设备在线状态
- **连接测试**: 测试设备网络连通性
- **设备信息**: IP地址、端口、绑定储位

#### 6.2 LED控制测试
1. **选择设备**: 从下拉列表选择ESP32设备
2. **设置参数**: 
   - 通道号 (0-3)
   - 起始和结束位置
   - 亮度级别 (0-31)
   - 持续时间
3. **选择颜色**: 8种预设颜色可选
4. **实时预览**: 预览区域显示LED效果
5. **发送指令**: 向真实设备发送控制命令

#### 6.3 LED操作
- **发送控制指令**: 发送当前配置的LED控制命令
- **测试所有LED**: 测试设备所有LED通道
- **关闭所有LED**: 关闭设备所有LED

### 7. 数据模拟说明

#### 7.1 假数据设置
原型使用以下模拟数据：

**仓库数据**:
- WH001: 主仓库 (北京)
- WH002: 分拣中心 (上海)  
- WH003: 生产仓库 (广州)

**区域数据**:
- A区域: 常温区 (15-25°C)
- B区域: 冷藏区 (2-8°C)
- C区域: 冷冻区 (-18°C)

**物料数据**:
- MAT001: 电子元器件A
- MAT002: 原材料B
- MAT003: 成品C

**设备数据**:
- ESP32-001: *************
- ESP32-002: 192.168.1.101
- ESP32-003: 192.168.1.102

#### 7.2 条码扫描模拟
点击 "扫码" 按钮会随机生成以下类型的条码：
- 物料编码: MAT001, MAT002, MAT003
- 储位编码: A01-001, A01-002, A01-003
- LPN编码: LPN001234

### 8. 功能演示流程

#### 8.1 完整入库流程演示
1. 登录系统 → 作业管理 → 入库作业
2. 扫描物料码 (例: MAT001)
3. 填写入库数量: 100
4. 点击 "获取储位推荐"
5. 选择推荐储位 A01-005
6. 系统模拟点亮储位LED
7. 确认入库完成

#### 8.2 层级管理演示
1. 仓库管理 → 仓库列表
2. 点击 "查看区域" → 进入区域管理
3. 点击 "查看货架" → 进入货架管理  
4. 点击 "查看储位" → 进入储位管理
5. 点击 "点亮储位" → 测试LED控制

#### 8.3 LED控制演示
1. 硬件管理 → ESP32设备管理
2. 选择设备: ESP32-001
3. 设置参数: 通道0, 位置0-10, 红色, 亮度20
4. 查看预览效果
5. 点击 "发送控制指令"

### 9. 界面特点

#### 9.1 Ant Design风格
- 使用Ant Design色彩规范
- 统一的组件样式和交互
- 现代化的视觉设计

#### 9.2 响应式设计
- 支持PC和移动端访问
- 自适应不同屏幕尺寸
- 优化的触摸交互

#### 9.3 用户体验
- 直观的面包屑导航
- 实时的状态反馈
- 流畅的页面切换动画

### 10. 技术实现

#### 10.1 纯前端实现
- 单一HTML文件包含所有功能
- 无需服务器环境
- 可直接在浏览器中运行

#### 10.2 模拟交互
- 完整的用户交互流程
- 真实的数据展示效果
- 模拟的后端响应

#### 10.3 扩展性
- 清晰的代码结构
- 易于修改和扩展
- 为实际开发提供参考

### 11. 注意事项

#### 11.1 数据持久化
- 原型数据仅在内存中存储
- 刷新页面会重置所有数据
- 不支持真实的数据库操作

#### 11.2 硬件集成
- LED控制为模拟操作
- 不会发送真实的硬件指令
- 需要在实际开发中对接ESP32设备

#### 11.3 浏览器兼容
- 建议使用现代浏览器 (Chrome, Firefox, Safari)
- 支持HTML5和ES6语法
- 确保JavaScript已启用

### 12. 后续开发参考

#### 12.1 前端开发
- 使用Vue 3 + Ant Design Vue重构
- 参考原型的组件结构和交互设计
- 实现更完善的状态管理

#### 12.2 后端开发
- 基于.NET 9 Web API实现
- 使用PostgreSQL数据库
- 集成JWT认证和权限管理

#### 12.3 硬件集成
- ESP32设备HTTP通信协议
- WS2812 LED控制算法
- 设备健康监控机制

---

**联系信息**
- 项目: WMS-VGL 智能仓储管理系统
- 版本: 高保真原型 v1.0
- 更新: 2024年1月

本原型为WMS-VGL系统的完整功能演示，为实际开发提供详细的界面和交互参考。