﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace WMS.API.Migrations
{
    /// <inheritdoc />
    public partial class AddBarcodeParsingAndConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BarcodeRuleCategories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CategoryCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CategoryName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BarcodeRuleCategories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BarcodeValidationRules",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RuleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RuleType = table.Column<int>(type: "integer", nullable: false),
                    Configuration = table.Column<string>(type: "jsonb", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    ClientId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BarcodeValidationRules", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ConfigurationConflictLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ClientId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ConflictType = table.Column<int>(type: "integer", nullable: false),
                    TargetScope = table.Column<string>(type: "text", nullable: true),
                    ConflictingConfigs = table.Column<string>(type: "jsonb", nullable: true),
                    ResolutionStrategy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ResolvedValue = table.Column<string>(type: "jsonb", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    ResolvedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConfigurationConflictLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "HierarchicalConfigurations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ClientId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ConfigLevel = table.Column<int>(type: "integer", nullable: false),
                    TargetType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    TargetId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ConfigKey = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ConfigValue = table.Column<string>(type: "jsonb", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    EffectiveTo = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HierarchicalConfigurations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UniqueCodeRegistries",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UniqueCode = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    MaterialId = table.Column<int>(type: "integer", nullable: false),
                    StorageLocationId = table.Column<int>(type: "integer", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    RegistrationDate = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    LastOperationDate = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UniqueCodeRegistries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UniqueCodeRegistries_Materials_MaterialId",
                        column: x => x.MaterialId,
                        principalTable: "Materials",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UniqueCodeRegistries_StorageLocations_StorageLocationId",
                        column: x => x.StorageLocationId,
                        principalTable: "StorageLocations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "BarcodeParsingRules",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CategoryId = table.Column<int>(type: "integer", nullable: false),
                    RuleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RegexPattern = table.Column<string>(type: "text", nullable: false),
                    FieldMappings = table.Column<string>(type: "jsonb", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    ClientId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BarcodeParsingRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BarcodeParsingRules_BarcodeRuleCategories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "BarcodeRuleCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ConfigurationApplicationLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    OperationId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ClientId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ZoneCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    ShelfCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    LocationCode = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    MaterialCategory = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ConfigKey = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AppliedConfigLevel = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    AppliedConfigId = table.Column<int>(type: "integer", nullable: true),
                    FinalConfigValue = table.Column<string>(type: "jsonb", nullable: false),
                    ConfigResolutionPath = table.Column<string>(type: "text", nullable: true),
                    AppliedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConfigurationApplicationLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ConfigurationApplicationLogs_HierarchicalConfigurations_App~",
                        column: x => x.AppliedConfigId,
                        principalTable: "HierarchicalConfigurations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "BarcodeParsingLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    OriginalBarcode = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ParsedResult = table.Column<string>(type: "jsonb", nullable: true),
                    MatchedRuleId = table.Column<int>(type: "integer", nullable: true),
                    IsSuccess = table.Column<bool>(type: "boolean", nullable: false),
                    ErrorMessage = table.Column<string>(type: "text", nullable: true),
                    ParsedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UserId = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    ClientId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    OperationType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    StorageLocationCode = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ProcessingTimeMs = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BarcodeParsingLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BarcodeParsingLogs_BarcodeParsingRules_MatchedRuleId",
                        column: x => x.MatchedRuleId,
                        principalTable: "BarcodeParsingRules",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeParsingLogs_ClientId",
                table: "BarcodeParsingLogs",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeParsingLogs_IsSuccess",
                table: "BarcodeParsingLogs",
                column: "IsSuccess");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeParsingLogs_MatchedRuleId",
                table: "BarcodeParsingLogs",
                column: "MatchedRuleId");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeParsingLogs_ParsedAt",
                table: "BarcodeParsingLogs",
                column: "ParsedAt");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeParsingLogs_UserId",
                table: "BarcodeParsingLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeParsingRules_CategoryId",
                table: "BarcodeParsingRules",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeParsingRules_ClientId",
                table: "BarcodeParsingRules",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeParsingRules_Priority",
                table: "BarcodeParsingRules",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeRuleCategories_CategoryCode",
                table: "BarcodeRuleCategories",
                column: "CategoryCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeValidationRules_ClientId",
                table: "BarcodeValidationRules",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_BarcodeValidationRules_RuleType",
                table: "BarcodeValidationRules",
                column: "RuleType");

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationApplicationLogs_AppliedAt",
                table: "ConfigurationApplicationLogs",
                column: "AppliedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationApplicationLogs_AppliedConfigId",
                table: "ConfigurationApplicationLogs",
                column: "AppliedConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationApplicationLogs_OperationId",
                table: "ConfigurationApplicationLogs",
                column: "OperationId");

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationConflictLogs_ClientId",
                table: "ConfigurationConflictLogs",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationConflictLogs_ConflictType",
                table: "ConfigurationConflictLogs",
                column: "ConflictType");

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationConflictLogs_CreatedAt",
                table: "ConfigurationConflictLogs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_HierarchicalConfigurations_ClientId_ConfigLevel",
                table: "HierarchicalConfigurations",
                columns: new[] { "ClientId", "ConfigLevel" });

            migrationBuilder.CreateIndex(
                name: "IX_HierarchicalConfigurations_ConfigKey",
                table: "HierarchicalConfigurations",
                column: "ConfigKey");

            migrationBuilder.CreateIndex(
                name: "IX_HierarchicalConfigurations_Priority",
                table: "HierarchicalConfigurations",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_HierarchicalConfigurations_TargetType_TargetId",
                table: "HierarchicalConfigurations",
                columns: new[] { "TargetType", "TargetId" });

            migrationBuilder.CreateIndex(
                name: "IX_UniqueCodeRegistries_MaterialId",
                table: "UniqueCodeRegistries",
                column: "MaterialId");

            migrationBuilder.CreateIndex(
                name: "IX_UniqueCodeRegistries_Status",
                table: "UniqueCodeRegistries",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_UniqueCodeRegistries_StorageLocationId",
                table: "UniqueCodeRegistries",
                column: "StorageLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_UniqueCodeRegistries_UniqueCode",
                table: "UniqueCodeRegistries",
                column: "UniqueCode",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BarcodeParsingLogs");

            migrationBuilder.DropTable(
                name: "BarcodeValidationRules");

            migrationBuilder.DropTable(
                name: "ConfigurationApplicationLogs");

            migrationBuilder.DropTable(
                name: "ConfigurationConflictLogs");

            migrationBuilder.DropTable(
                name: "UniqueCodeRegistries");

            migrationBuilder.DropTable(
                name: "BarcodeParsingRules");

            migrationBuilder.DropTable(
                name: "HierarchicalConfigurations");

            migrationBuilder.DropTable(
                name: "BarcodeRuleCategories");
        }
    }
}
