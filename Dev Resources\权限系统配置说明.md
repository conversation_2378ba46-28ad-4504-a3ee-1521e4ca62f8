# WMS-VGL 权限系统配置说明

## 概述

WMS-VGL系统已集成ASP.NET Core Identity框架，实现基于角色的权限控制（RBAC），为仓库管理系统提供多层级的安全访问控制。

## 角色体系

### 系统角色

| 角色 | 说明 | 权限范围 |
|------|------|----------|
| **SuperAdmin** | 超级管理员 | 系统最高权限，包括用户管理、系统配置等 |
| **Admin** | 管理员 | 用户管理、系统配置（除超级管理员权限外） |
| **WarehouseManager** | 仓库管理员 | 仓库运营管理，包括储位、库存、设备管理 |
| **Operator** | 操作员 | 基础仓库操作权限 |
| **ReadOnly** | 只读用户 | 只能查看数据，无法修改 |

### 权限策略

| 策略名称 | 适用角色 | 功能模块 |
|----------|----------|----------|
| **SuperAdminPolicy** | SuperAdmin | 系统最高权限 |
| **AdminPolicy** | SuperAdmin, Admin | 管理员级别功能 |
| **WarehousePolicy** | SuperAdmin, Admin, WarehouseManager | 仓库管理功能 |
| **OperatorPolicy** | SuperAdmin, Admin, WarehouseManager, Operator | 基础操作功能 |
| **ReadOnlyPolicy** | 所有角色 | 数据查看功能 |
| **ESP32ManagementPolicy** | SuperAdmin, Admin, WarehouseManager | ESP32设备管理 |
| **StorageLocationManagementPolicy** | SuperAdmin, Admin, WarehouseManager | 储位管理 |
| **InventoryManagementPolicy** | SuperAdmin, Admin, WarehouseManager, Operator | 库存管理 |
| **UserManagementPolicy** | SuperAdmin, Admin | 用户管理 |
| **SystemConfigPolicy** | SuperAdmin | 系统配置 |
| **DataImportExportPolicy** | SuperAdmin, Admin, WarehouseManager | 数据导入导出 |

## 默认配置

### 超级管理员账户

系统会自动创建默认超级管理员账户：

- **邮箱**: <EMAIL>
- **密码**: Admin@123456
- **角色**: SuperAdmin

> ⚠️ **重要提醒**: 请在首次登录后立即修改默认密码！

### 配置文件设置

可在 `appsettings.json` 中自定义默认超级管理员信息：

```json
{
  "DefaultSuperAdmin": {
    "Email": "<EMAIL>",
    "Password": "Admin@123456",
    "FullName": "系统管理员"
  }
}
```

## API接口

### 认证接口

#### 1. 用户登录
```http
POST /api/Auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin@123456",
  "rememberMe": false
}
```

#### 2. 用户注销
```http
POST /api/Auth/logout
Authorization: Bearer <token>
```

#### 3. 获取当前用户信息
```http
GET /api/Auth/profile
Authorization: Bearer <token>
```

#### 4. 修改密码
```http
POST /api/Auth/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "Admin@123456",
  "newPassword": "NewPassword@123",
  "confirmPassword": "NewPassword@123"
}
```

### 用户管理接口 (需要管理员权限)

#### 1. 获取用户列表
```http
GET /api/Users
Authorization: Bearer <token>
```

#### 2. 创建用户
```http
POST /api/Users
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "fullName": "用户姓名",
  "employeeId": "EMP001",
  "department": "仓储部",
  "position": "仓库管理员",
  "password": "Password@123",
  "roles": ["WarehouseManager"],
  "remarks": "备注信息"
}
```

#### 3. 更新用户信息
```http
PUT /api/Users/<USER>
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "fullName": "更新后姓名",
  "employeeId": "EMP001",
  "department": "仓储部",
  "position": "高级仓库管理员",
  "isActive": true,
  "roles": ["WarehouseManager", "Operator"],
  "remarks": "更新后的备注"
}
```

#### 4. 重置用户密码
```http
POST /api/Users/<USER>/reset-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "newPassword": "NewPassword@123"
}
```

#### 5. 删除用户 (仅超级管理员)
```http
DELETE /api/Users/<USER>
Authorization: Bearer <token>
```

#### 6. 获取系统角色列表
```http
GET /api/Users/<USER>
Authorization: Bearer <token>
```

## 控制器权限配置

### ESP32控制器管理
- **基础权限**: ESP32ManagementPolicy (仓库管理员及以上)
- **查看权限**: ReadOnlyPolicy (所有用户)

### 储位管理
- **管理权限**: StorageLocationManagementPolicy (仓库管理员及以上)
- **查看权限**: ReadOnlyPolicy (所有用户)

### 库存管理
- **管理权限**: InventoryManagementPolicy (操作员及以上)
- **查看权限**: ReadOnlyPolicy (所有用户)

## 密码策略

系统采用以下密码策略：

- **最小长度**: 8个字符
- **必须包含**: 大写字母、小写字母、数字、特殊字符
- **唯一字符数**: 至少1个

## 账户锁定策略

- **失败次数**: 5次
- **锁定时间**: 5分钟
- **适用范围**: 所有新用户

## 部署注意事项

### 1. 数据库迁移

在首次部署时，需要执行以下命令：

```bash
# 生成Identity迁移
dotnet ef migrations add AddIdentityTables

# 应用迁移
dotnet ef database update
```

### 2. 种子数据初始化

系统启动时会自动：
- 创建所有系统角色
- 创建默认超级管理员账户
- 检查并修复角色分配

### 3. 环境变量配置

可通过环境变量覆盖默认配置：

```bash
DefaultSuperAdmin__Email=<EMAIL>
DefaultSuperAdmin__Password=CustomPassword@123
DefaultSuperAdmin__FullName=自定义管理员
```

## 安全建议

### 1. 生产环境配置
- 立即修改默认超级管理员密码
- 使用强密码策略
- 定期审查用户权限
- 监控异常登录行为

### 2. 权限分配原则
- 遵循最小权限原则
- 按岗位职责分配角色
- 定期审查和调整权限
- 及时停用离职员工账户

### 3. 数据保护
- 定期备份用户数据
- 监控敏感操作日志
- 实施访问审计

## 常见问题

### Q: 忘记管理员密码怎么办？
A: 可以通过数据库直接重置，或使用备用超级管理员账户。

### Q: 如何批量导入用户？
A: 目前需要通过API逐个创建，后续版本将支持批量导入。

### Q: 如何自定义权限策略？
A: 修改 `Program.cs` 中的授权策略配置，并重新部署应用。

### Q: 用户角色可以动态修改吗？
A: 是的，管理员可以通过用户管理接口动态修改用户角色。

## 技术架构

### 使用技术
- **框架**: ASP.NET Core Identity
- **数据库**: PostgreSQL
- **ORM**: Entity Framework Core
- **认证**: Cookie Authentication (可扩展为JWT)

### 数据表结构
- AspNetUsers: 用户表
- AspNetRoles: 角色表
- AspNetUserRoles: 用户角色关联表
- AspNetUserClaims: 用户声明表
- AspNetRoleClaims: 角色声明表
- AspNetUserLogins: 用户登录表
- AspNetUserTokens: 用户令牌表

## 更新日志

### v1.0.0 (当前版本)
- ✅ 实现基于角色的权限控制
- ✅ 用户管理功能
- ✅ 认证登录功能
- ✅ 密码策略配置
- ✅ 种子数据初始化
- ✅ ESP32控制器权限控制

### 计划功能
- [ ] JWT Token认证支持
- [ ] 用户批量导入
- [ ] 操作日志审计
- [ ] 权限变更历史
- [ ] 单点登录(SSO)支持