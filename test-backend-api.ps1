# PowerShell脚本：测试后端API是否正在运行
Write-Host "=== WMS-VGL 后端API测试 ===" -ForegroundColor Cyan

# 测试API基础连接
Write-Host "`n1. 测试API基础连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/health" -Method GET -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ API服务正在运行 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ API服务未运行或无法访问: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请确保后端服务已启动在端口8080" -ForegroundColor Yellow
}

# 测试仓库API端点
Write-Host "`n2. 测试仓库API端点..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/warehouses" -Method GET -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ 仓库API端点可访问 (状态码: $($response.StatusCode))" -ForegroundColor Green
    
    # 尝试解析响应内容
    $content = $response.Content | ConvertFrom-Json
    Write-Host "📦 返回的仓库数量: $($content.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 仓库API端点访问失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 检查是否是404错误
    if ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "💡 可能的原因：API路由配置问题" -ForegroundColor Yellow
    }
}

# 测试CORS配置
Write-Host "`n3. 测试CORS配置..." -ForegroundColor Yellow
try {
    $headers = @{
        'Origin' = 'http://localhost:3002'
        'Access-Control-Request-Method' = 'POST'
        'Access-Control-Request-Headers' = 'Content-Type'
    }
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/warehouses" -Method OPTIONS -Headers $headers -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ CORS预检请求成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ CORS配置可能有问题: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试创建仓库API
Write-Host "`n4. 测试创建仓库API..." -ForegroundColor Yellow
$testWarehouse = @{
    code = "TEST$(Get-Date -Format 'HHmmss')"
    name = "PowerShell测试仓库"
    description = "通过PowerShell脚本创建的测试仓库"
    warehouseType = "distribution"
    status = "Active"
    address = "测试地址123号"
    contactPerson = "测试联系人"
    contactPhone = "13800138000"
    totalArea = 1000.0
    totalVolume = 3000.0
    configuration = "{}"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/warehouses" -Method POST -Body $testWarehouse -ContentType "application/json" -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ 创建仓库API测试成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
    
    $createdWarehouse = $response.Content | ConvertFrom-Json
    Write-Host "📦 创建的仓库ID: $($createdWarehouse.id)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 创建仓库API测试失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 显示详细错误信息
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "📊 HTTP状态码: $statusCode" -ForegroundColor Yellow
        
        if ($statusCode -eq 400) {
            Write-Host "💡 可能的原因：请求数据格式错误或验证失败" -ForegroundColor Yellow
        } elseif ($statusCode -eq 500) {
            Write-Host "💡 可能的原因：服务器内部错误，检查后端日志" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Cyan
Write-Host "如果所有测试都失败，请检查：" -ForegroundColor Yellow
Write-Host "1. 后端服务是否已启动" -ForegroundColor White
Write-Host "2. 端口8080是否被占用" -ForegroundColor White
Write-Host "3. 防火墙设置是否阻止了连接" -ForegroundColor White
Write-Host "4. 数据库连接是否正常" -ForegroundColor White
