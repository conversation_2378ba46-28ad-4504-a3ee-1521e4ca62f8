using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    public enum InboundOrderStatus
    {
        Pending = 0, // 待处理
        InProgress = 1, // 进行中
        Completed = 2, // 已完成
        Cancelled = 3, // 已取消
    }

    public class InboundOrder
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string OrderNumber { get; set; } = string.Empty;

        [StringLength(100)]
        public string? SupplierName { get; set; }

        [StringLength(50)]
        public string? SupplierCode { get; set; }

        public InboundOrderStatus Status { get; set; } = InboundOrderStatus.Pending;

        public DateTime? ExpectedDate { get; set; }
        public DateTime? ActualDate { get; set; }

        [StringLength(500)]
        public string? Remarks { get; set; }

        [StringLength(100)]
        public string? CreatedBy { get; set; }

        [StringLength(100)]
        public string? ProcessedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // 导航属性
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<InboundOrderItem>? Items { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<InventoryTransaction>? Transactions { get; set; }
    }

    public class InboundOrderItem
    {
        public int Id { get; set; }

        public int InboundOrderId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual InboundOrder? InboundOrder { get; set; }

        public int MaterialId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual Material? Material { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal ExpectedQuantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? ActualQuantity { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? LpnCode { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(200)]
        public string? Remarks { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
