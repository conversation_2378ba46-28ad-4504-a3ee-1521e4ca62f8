using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Constants;
using WMS.API.Data;
using WMS.API.Models;
using WMS.API.Services;

namespace WMS.API.Controllers
{
    /// <summary>
    /// ESP32控制器管理接口
    /// 提供ESP32硬件控制器的增删改查及设备通信功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
    public class ESP32ControllersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IESP32CommunicationService _communicationService;
        private readonly IESP32HealthCheckService _healthCheckService;
        private readonly ILogger<ESP32ControllersController> _logger;

        public ESP32ControllersController(
            ApplicationDbContext context,
            IESP32CommunicationService communicationService,
            IESP32HealthCheckService healthCheckService,
            ILogger<ESP32ControllersController> logger
        )
        {
            _context = context;
            _communicationService = communicationService;
            _healthCheckService = healthCheckService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有ESP32控制器列表
        /// </summary>
        /// <returns>控制器列表，包含关联的储位信息</returns>
        [HttpGet]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<ActionResult<IEnumerable<ESP32Controller>>> GetControllers()
        {
            return await _context.ESP32Controllers.Include(c => c.StorageLocations).ToListAsync();
        }

        /// <summary>
        /// 根据ID获取指定ESP32控制器信息
        /// </summary>
        /// <param name="id">控制器ID</param>
        /// <returns>控制器详细信息，包含关联的储位信息</returns>
        [HttpGet("{id}")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<ActionResult<ESP32Controller>> GetController(int id)
        {
            var controller = await _context
                .ESP32Controllers.Include(c => c.StorageLocations)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (controller == null)
            {
                return NotFound();
            }

            return controller;
        }

        /// <summary>
        /// 创建新的ESP32控制器
        /// </summary>
        /// <param name="controller">控制器信息</param>
        /// <returns>创建成功的控制器信息</returns>
        [HttpPost]
        public async Task<ActionResult<ESP32Controller>> CreateController(
            ESP32Controller controller
        )
        {
            // 检查是否存在相同IP的控制器
            var existingController = await _context.ESP32Controllers.FirstOrDefaultAsync(c =>
                c.IpAddress == controller.IpAddress
            );

            if (existingController != null)
            {
                // 如果IP地址已存在，更新现有控制器信息而不是创建新的
                existingController.Name = controller.Name;
                existingController.Port = controller.Port;
                existingController.Description = controller.Description;
                existingController.MdnsName = controller.MdnsName;
                existingController.IsOnline = controller.IsOnline;
                existingController.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(existingController);
            }

            controller.CreatedAt = DateTime.UtcNow;
            controller.UpdatedAt = DateTime.UtcNow;

            _context.ESP32Controllers.Add(controller);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetController), new { id = controller.Id }, controller);
        }

        /// <summary>
        /// 更新ESP32控制器信息
        /// </summary>
        /// <param name="id">控制器ID</param>
        /// <param name="controller">更新的控制器数据</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateController(int id, ESP32Controller controller)
        {
            if (id != controller.Id)
            {
                return BadRequest();
            }

            var existingController = await _context.ESP32Controllers.FindAsync(id);
            if (existingController == null)
            {
                return NotFound();
            }

            existingController.Name = controller.Name;
            existingController.IpAddress = controller.IpAddress;
            existingController.Port = controller.Port;
            existingController.Description = controller.Description;
            existingController.MdnsName = controller.MdnsName;
            existingController.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ControllerExists(id))
                {
                    return NotFound();
                }
                throw;
            }

            return NoContent();
        }

        /// <summary>
        /// 删除ESP32控制器
        /// </summary>
        /// <param name="id">控制器ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteController(int id)
        {
            var controller = await _context.ESP32Controllers.FindAsync(id);
            if (controller == null)
            {
                return NotFound();
            }

            _context.ESP32Controllers.Remove(controller);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// 测试ESP32控制器连接状态
        /// </summary>
        /// <param name="id">控制器ID</param>
        /// <returns>连接测试结果</returns>
        [HttpPost("{id}/test-connection")]
        public async Task<ActionResult<object>> TestConnection(int id)
        {
            var controller = await _context.ESP32Controllers.FindAsync(id);
            if (controller == null)
            {
                return NotFound();
            }

            var isOnline = await _communicationService.TestConnectionAsync(controller);

            // 更新状态
            controller.IsOnline = isOnline;
            controller.LastHeartbeat = DateTime.UtcNow;
            controller.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            return Ok(
                new
                {
                    Id = controller.Id,
                    Name = controller.Name,
                    IsOnline = isOnline,
                    LastHeartbeat = controller.LastHeartbeat,
                    Message = isOnline ? "Connection successful" : "Connection failed",
                }
            );
        }

        /// <summary>
        /// 获取ESP32设备信息
        /// </summary>
        /// <param name="id">控制器ID</param>
        /// <returns>设备详细信息</returns>
        [HttpPost("{id}/device-info")]
        public async Task<ActionResult<object>> GetDeviceInfo(int id)
        {
            var controller = await _context.ESP32Controllers.FindAsync(id);
            if (controller == null)
            {
                return NotFound();
            }

            var deviceInfo = await _communicationService.GetDeviceInfoAsync(controller);
            if (deviceInfo == null)
            {
                return BadRequest(new { Message = "Failed to get device information" });
            }

            return Ok(deviceInfo);
        }

        /// <summary>
        /// 对所有ESP32控制器进行健康检查
        /// </summary>
        /// <returns>所有控制器的健康状态统计</returns>
        [HttpPost("health-check")]
        public async Task<ActionResult<object>> HealthCheckAll()
        {
            await _healthCheckService.CheckAllControllersHealthAsync();
            var healthStatus = await _healthCheckService.GetControllersHealthStatusAsync();

            return Ok(
                new
                {
                    TotalControllers = healthStatus.Count,
                    OnlineControllers = healthStatus.Count(h => h.Value),
                    OfflineControllers = healthStatus.Count(h => !h.Value),
                    HealthStatus = healthStatus,
                }
            );
        }

        /// <summary>
        /// 控制ESP32的LED灯条显示
        /// </summary>
        /// <param name="id">控制器ID</param>
        /// <param name="request">灯光控制参数</param>
        /// <returns>控制结果</returns>
        [HttpPost("{id}/set-light")]
        public async Task<ActionResult<object>> SetLight(int id, [FromBody] SetLightRequest request)
        {
            var controller = await _context.ESP32Controllers.FindAsync(id);
            if (controller == null)
            {
                return NotFound();
            }

            var success = await _communicationService.SetLightAsync(
                controller,
                request.Channel,
                request.StartPosition,
                request.EndPosition,
                request.Color,
                request.Brightness
            );

            return Ok(
                new
                {
                    Success = success,
                    Message = success ? "Light control successful" : "Light control failed",
                }
            );
        }

        /// <summary>
        /// 关闭ESP32控制器的LED灯条
        /// </summary>
        /// <param name="id">控制器ID</param>
        /// <param name="request">关灯请求参数</param>
        /// <returns>关灯结果</returns>
        [HttpPost("{id}/turn-off-lights")]
        public async Task<ActionResult<object>> TurnOffLights(
            int id,
            [FromBody] TurnOffLightsRequest request
        )
        {
            var controller = await _context.ESP32Controllers.FindAsync(id);
            if (controller == null)
            {
                return NotFound();
            }

            var success = await _communicationService.TurnOffAllLightsAsync(
                controller,
                request.Channel
            );

            return Ok(
                new
                {
                    Success = success,
                    Message = success
                        ? "Lights turned off successfully"
                        : "Failed to turn off lights",
                }
            );
        }

        /// <summary>
        /// 控制ESP32控制器的大指示灯（硬件层接口）
        /// </summary>
        /// <param name="id">控制器ID</param>
        /// <param name="request">大指示灯控制参数</param>
        /// <returns>控制结果</returns>
        [HttpPost("{id}/control-big-led")]
        public async Task<ActionResult<object>> ControlBigLed(
            int id,
            [FromBody] ControlBigLedHardwareRequest request
        )
        {
            var controller = await _context.ESP32Controllers.FindAsync(id);
            if (controller == null)
            {
                return NotFound();
            }

            var success = await _communicationService.ControlBigLedAsync(
                controller,
                request.IndicatorIndex,
                request.TurnOn
            );

            return Ok(
                new
                {
                    Success = success,
                    Message = success
                        ? $"Big LED control successful - Controller {id}, Indicator {request.IndicatorIndex} turned {(request.TurnOn ? "on" : "off")}"
                        : "Big LED control failed",
                }
            );
        }

        /// <summary>
        /// 通过货架号控制大指示灯（业务层接口）
        /// </summary>
        /// <param name="request">货架大指示灯控制参数</param>
        /// <returns>控制结果</returns>
        [HttpPost("control-shelf-indicator")]
        public async Task<ActionResult<object>> ControlShelfIndicator(
            [FromBody] ControlShelfIndicatorRequest request
        )
        {
            // 根据货架号找到对应的控制器
            var storageLocation = await _context
                .StorageLocations.Include(sl => sl.ESP32Controller)
                .FirstOrDefaultAsync(sl => sl.ShelfCode == request.ShelfNumber);

            if (storageLocation?.ESP32Controller == null)
            {
                return NotFound(
                    new { Message = $"No controller found for shelf {request.ShelfNumber}" }
                );
            }

            var success = await _communicationService.ControlBigLedAsync(
                storageLocation.ESP32Controller,
                request.IndicatorIndex,
                request.TurnOn
            );

            return Ok(
                new
                {
                    Success = success,
                    ShelfNumber = request.ShelfNumber,
                    ControllerId = storageLocation.ESP32Controller.Id,
                    ControllerName = storageLocation.ESP32Controller.Name,
                    Message = success
                        ? $"Shelf indicator control successful - Shelf {request.ShelfNumber}, Indicator {request.IndicatorIndex} turned {(request.TurnOn ? "on" : "off")}"
                        : "Shelf indicator control failed",
                }
            );
        }

        private bool ControllerExists(int id)
        {
            return _context.ESP32Controllers.Any(e => e.Id == id);
        }
    }

    /// <summary>
    /// LED灯控制请求参数
    /// </summary>
    public class SetLightRequest
    {
        /// <summary>
        /// LED通道号（0-3）
        /// </summary>
        public int Channel { get; set; } = 0;

        /// <summary>
        /// LED起始位置
        /// </summary>
        public int StartPosition { get; set; }

        /// <summary>
        /// LED结束位置
        /// </summary>
        public int EndPosition { get; set; }

        /// <summary>
        /// LED颜色（如：red, green, blue等）
        /// </summary>
        public string Color { get; set; } = "red";

        /// <summary>
        /// LED亮度（0-255）
        /// </summary>
        public int Brightness { get; set; } = 255;
    }

    /// <summary>
    /// 关闭LED灯请求参数
    /// </summary>
    public class TurnOffLightsRequest
    {
        /// <summary>
        /// LED通道号（0-3）
        /// </summary>
        public int Channel { get; set; } = 0;
    }

    /// <summary>
    /// 硬件层：ESP32控制器大指示灯控制请求参数
    /// </summary>
    public class ControlBigLedHardwareRequest
    {
        /// <summary>
        /// 指示灯索引（Y轴位置）
        /// </summary>
        public int IndicatorIndex { get; set; }

        /// <summary>
        /// 开关状态（true为开启，false为关闭）
        /// </summary>
        public bool TurnOn { get; set; }
    }

    /// <summary>
    /// 业务层：货架大指示灯控制请求参数
    /// </summary>
    public class ControlShelfIndicatorRequest
    {
        /// <summary>
        /// 货架编号
        /// </summary>
        public string ShelfNumber { get; set; } = string.Empty;

        /// <summary>
        /// 指示灯索引（Y轴位置）
        /// </summary>
        public int IndicatorIndex { get; set; }

        /// <summary>
        /// 开关状态（true为开启，false为关闭）
        /// </summary>
        public bool TurnOn { get; set; }
    }
}
