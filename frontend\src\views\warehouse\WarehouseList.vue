<template>
  <div class="warehouse-list">
    <div class="page-header">
      <h1 class="page-title">仓库管理</h1>
      <p class="page-desc">管理仓库基础信息，配置全局策略和层级结构</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-card class="stat-card">
        <div class="stat-number">3</div>
        <div class="stat-label">总仓库数</div>
      </a-card>
      <a-card class="stat-card">
        <div class="stat-number">12</div>
        <div class="stat-label">总区域数</div>
      </a-card>
      <a-card class="stat-card">
        <div class="stat-number">156</div>
        <div class="stat-label">总货架数</div>
      </a-card>
      <a-card class="stat-card">
        <div class="stat-number">2,340</div>
        <div class="stat-label">总储位数</div>
      </a-card>
    </div>

    <!-- 仓库列表 -->
    <a-card class="content-card">
      <template #title>
        <span class="card-title">仓库列表</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新增仓库
          </a-button>
          <a-button>
            <ExportOutlined />
            导出数据
          </a-button>
        </a-space>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="仓库编码">
                <a-input
                  v-model:value="searchForm.code"
                  placeholder="输入仓库编码"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="仓库名称">
                <a-input
                  v-model:value="searchForm.name"
                  placeholder="输入仓库名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="仓库类型">
                <a-select
                  v-model:value="searchForm.type"
                  placeholder="选择仓库类型"
                >
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="distribution"
                    >配送中心</a-select-option
                  >
                  <a-select-option value="storage">存储仓库</a-select-option>
                  <a-select-option value="production">生产仓库</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="状态">
                <a-select
                  v-model:value="searchForm.status"
                  placeholder="选择状态"
                >
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="active">启用</a-select-option>
                  <a-select-option value="inactive">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" class="text-right">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <SearchOutlined />
                  搜索
                </a-button>
                <a-button @click="handleReset">重置</a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="warehouses"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.warehouseType)">
              {{ getTypeLabel(record.warehouseType) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusLabel(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'contact'">
            {{ record.contactPerson }}
          </template>
          <template v-else-if="column.key === 'phone'">
            {{ record.contactPhone }}
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewZones(record)">
                查看区域
              </a-button>
              <a-button type="link" size="small" @click="editWarehouse(record)">
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="deleteWarehouse(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑仓库模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑仓库' : '新增仓库'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      width="600px"
    >
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="仓库编码" name="code">
              <a-input v-model:value="form.code" placeholder="请输入仓库编码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="仓库名称" name="name">
              <a-input v-model:value="form.name" placeholder="请输入仓库名称" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="form.description"
            placeholder="请输入仓库描述"
            :rows="2"
          />
        </a-form-item>
        <a-form-item label="地址" name="address">
          <a-input v-model:value="form.address" placeholder="请输入仓库地址" />
        </a-form-item>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="仓库类型" name="type">
              <a-select v-model:value="form.type" placeholder="请选择仓库类型">
                <a-select-option value="distribution">配送中心</a-select-option>
                <a-select-option value="storage">存储仓库</a-select-option>
                <a-select-option value="production">生产仓库</a-select-option>
                <a-select-option value="cold_chain">冷链仓</a-select-option>
                <a-select-option value="hazmat">危险品仓</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="form.status" placeholder="请选择状态">
                <a-select-option value="active">运营中</a-select-option>
                <a-select-option value="maintenance">维护中</a-select-option>
                <a-select-option value="inactive">已停用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="总面积(㎡)" name="totalArea">
              <a-input-number
                v-model:value="form.totalArea"
                placeholder="请输入总面积"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="总容量(m³)" name="totalVolume">
              <a-input-number
                v-model:value="form.totalVolume"
                placeholder="请输入总容量"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="联系人" name="contact">
              <a-input v-model:value="form.contact" placeholder="请输入联系人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系电话" name="phone">
              <a-input v-model:value="form.phone" placeholder="请输入联系电话" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { message, Modal } from "ant-design-vue";
import {
  PlusOutlined,
  ExportOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import { warehouseApi } from "@/api/warehouse";
import type { Warehouse } from "@/types";

const router = useRouter();

// 响应式数据
const loading = ref(false);
const modalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 搜索表单
const searchForm = reactive({
  code: "",
  name: "",
  type: "",
  status: "",
});

// 表单数据
const form = reactive({
  id: "",
  code: "",
  name: "",
  description: "",
  address: "",
  type: "",
  status: "active",
  totalArea: null as number | null,
  totalVolume: null as number | null,
  contact: "",
  phone: "",
});

// 表单验证规则
const rules = {
  code: [{ required: true, message: "请输入仓库编码" }],
  name: [{ required: true, message: "请输入仓库名称" }],
  type: [{ required: true, message: "请选择仓库类型" }],
  status: [{ required: true, message: "请选择状态" }],
  address: [{ required: true, message: "请输入地址" }],
  contact: [{ required: true, message: "请输入联系人" }],
};

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`,
});

// 表格列配置
const columns = [
  {
    title: "仓库编码",
    dataIndex: "code",
    key: "code",
    width: 120,
  },
  {
    title: "仓库名称",
    dataIndex: "name",
    key: "name",
    width: 150,
  },
  {
    title: "仓库类型",
    dataIndex: "warehouseType",
    key: "type",
    width: 120,
  },
  {
    title: "面积(㎡)",
    dataIndex: "totalArea",
    key: "totalArea",
    width: 100,
  },
  {
    title: "容量(m³)",
    dataIndex: "totalVolume",
    key: "totalVolume",
    width: 100,
  },
  {
    title: "联系人",
    dataIndex: "contactPerson",
    key: "contact",
    width: 100,
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    width: 80,
  },
  {
    title: "操作",
    key: "actions",
    width: 200,
    fixed: "right",
  },
];

// 仓库数据
const warehouses = ref<Warehouse[]>([]);

// 模拟数据（当API不可用时使用）
const mockWarehouses: Warehouse[] = [
  {
    id: 1,
    code: "WH001",
    name: "主仓库",
    description: "主要配送中心，负责华北地区的物流配送",
    address: "北京市朝阳区xx路xx号",
    warehouseType: "distribution",
    status: "Active",
    totalArea: 5000.00,
    totalVolume: 15000.00,
    contactPerson: "张三",
    contactPhone: "13800138000",
    configuration: "{}",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 2,
    code: "WH002",
    name: "分拣中心",
    description: "自动化分拣中心，提供高效的货物分拣服务",
    address: "上海市浦东新区xx路xx号",
    warehouseType: "storage",
    status: "Active",
    totalArea: 3000.00,
    totalVolume: 9000.00,
    contactPerson: "李四",
    contactPhone: "13800138001",
    configuration: "{}",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 3,
    code: "WH003",
    name: "生产仓库",
    description: "生产原料和成品存储仓库",
    address: "广州市天河区xx路xx号",
    warehouseType: "production",
    status: "Maintenance",
    totalArea: 2500.00,
    totalVolume: 7500.00,
    contactPerson: "王五",
    contactPhone: "13800138002",
    configuration: "{}",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// 工具方法
const getTypeColor = (type: string) => {
  const colors = {
    distribution: "blue",
    storage: "green",
    production: "orange",
    cold_chain: "cyan",
    hazmat: "red",
  };
  return colors[type as keyof typeof colors] || "default";
};

const getTypeLabel = (type: string) => {
  const labels = {
    distribution: "配送中心",
    storage: "存储仓库",
    production: "生产仓库",
    cold_chain: "冷链仓",
    hazmat: "危险品仓",
  };
  return labels[type as keyof typeof labels] || type;
};

const getStatusColor = (status: string) => {
  const colors = {
    Active: "green",
    Maintenance: "orange",
    Inactive: "red",
  };
  return colors[status as keyof typeof colors] || "default";
};

const getStatusLabel = (status: string) => {
  const labels = {
    Active: "运营中",
    Maintenance: "维护中",
    Inactive: "已停用",
  };
  return labels[status as keyof typeof labels] || status;
};

// 数据加载方法
const loadWarehouses = async () => {
  loading.value = true;
  try {
    console.log("🚀 开始加载仓库数据...");
    const response = await warehouseApi.getWarehouses();
    console.log("✅ 仓库数据加载成功:", response);
    warehouses.value = response;
    pagination.total = response.length;
    message.success(`成功加载 ${response.length} 个仓库`);
  } catch (error: any) {
    console.error("❌ API调用失败，详细错误信息:", {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        baseURL: error.config?.baseURL,
        headers: error.config?.headers
      }
    });

    // 使用模拟数据
    warehouses.value = mockWarehouses;
    pagination.total = mockWarehouses.length;

    if (error.response?.status === 400) {
      message.error(`API请求错误 (400): ${error.response?.data?.message || '请求参数有误'}`);
    } else if (error.response?.status === 404) {
      message.error("API接口未找到 (404)，请检查后端服务");
    } else if (error.response?.status === 500) {
      message.error("服务器内部错误 (500)，请联系管理员");
    } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
      message.warning("网络连接失败，使用模拟数据");
    } else {
      message.warning("后端服务未启动，当前显示模拟数据");
    }
  } finally {
    loading.value = false;
  }
};

// 事件处理方法
const showAddModal = () => {
  isEdit.value = false;
  resetForm();
  modalVisible.value = true;
};

const handleSearch = async () => {
  loading.value = true;
  try {
    // 重新加载数据（在实际项目中，这里应该传递搜索参数给API）
    await loadWarehouses();

    // 在本地进行过滤（模拟搜索）
    let filteredWarehouses = [...warehouses.value];

    if (searchForm.code) {
      filteredWarehouses = filteredWarehouses.filter((w) =>
        w.code.toLowerCase().includes(searchForm.code.toLowerCase())
      );
    }

    if (searchForm.name) {
      filteredWarehouses = filteredWarehouses.filter((w) =>
        w.name.toLowerCase().includes(searchForm.name.toLowerCase())
      );
    }

    if (searchForm.type) {
      filteredWarehouses = filteredWarehouses.filter(
        (w) => w.warehouseType === searchForm.type
      );
    }

    if (searchForm.status) {
      const statusFilter =
        searchForm.status === "active" ? "Active" : "Inactive";
      filteredWarehouses = filteredWarehouses.filter(
        (w) => w.status === statusFilter
      );
    }

    warehouses.value = filteredWarehouses;
    pagination.total = filteredWarehouses.length;
    pagination.current = 1; // 重置到第一页

    message.success(`找到 ${filteredWarehouses.length} 条记录`);
  } catch (error) {
    console.error("搜索失败:", error);
    message.error("搜索失败，请重试");
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  Object.assign(searchForm, {
    code: "",
    name: "",
    type: "",
    status: "",
  });
  // 重新加载所有数据
  loadWarehouses();
};

const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // 重新加载数据
};

const viewZones = (record: any) => {
  router.push(`/warehouse/zones?warehouseId=${record.id}`);
};

const editWarehouse = (record: Warehouse) => {
  isEdit.value = true;
  // 转换数据格式以匹配表单
  Object.assign(form, {
    id: record.id.toString(),
    code: record.code,
    name: record.name,
    description: record.description || "",
    address: record.address || "",
    type: record.warehouseType || "",
    status: record.status === "Active" ? "active" :
            record.status === "Maintenance" ? "maintenance" : "inactive",
    totalArea: record.totalArea || null,
    totalVolume: record.totalVolume || null,
    contact: record.contactPerson || "",
    phone: record.contactPhone || "",
  });
  modalVisible.value = true;
};

const deleteWarehouse = (record: Warehouse) => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除仓库 "${record.name}" 吗？`,
    okText: "确定",
    cancelText: "取消",
    onOk: async () => {
      try {
        loading.value = true;

        try {
          // 调用API删除
          await warehouseApi.deleteWarehouse(record.id);
          message.success("仓库删除成功");
        } catch (apiError) {
          console.warn("API调用失败，使用模拟删除:", apiError);
          message.success("仓库删除成功（模拟操作）");
          message.warning("后端服务未启动，数据仅从本地移除");
        }

        // 从本地数据中移除
        const index = warehouses.value.findIndex((w) => w.id === record.id);
        if (index !== -1) {
          warehouses.value.splice(index, 1);
          pagination.total = warehouses.value.length;
        }
      } catch (error) {
        console.error("删除失败:", error);
        message.error("删除失败，请重试");
      } finally {
        loading.value = false;
      }
    },
  });
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    // 准备提交数据，转换为API期望的格式
    const submitData: Partial<Warehouse> = {
      code: form.code,
      name: form.name,
      description: form.description || undefined,
      address: form.address || undefined,
      warehouseType: form.type || undefined,
      status: form.status === "active" ? "Active" :
              form.status === "maintenance" ? "Maintenance" : "Inactive",
      totalArea: form.totalArea || undefined,
      totalVolume: form.totalVolume || undefined,
      contactPerson: form.contact || undefined,
      contactPhone: form.phone || undefined,
      configuration: "{}",
    };

    try {
      if (isEdit.value && form.id) {
        console.log("🔄 开始更新仓库，ID:", form.id, "数据:", submitData);
        // 编辑仓库
        const response = await warehouseApi.updateWarehouse(
          Number(form.id),
          submitData
        );
        console.log("✅ 仓库更新成功，响应:", response);
        // 更新本地数据
        const index = warehouses.value.findIndex(
          (w) => w.id === Number(form.id)
        );
        if (index !== -1) {
          warehouses.value[index] = response;
        }
        message.success("仓库更新成功");
      } else {
        console.log("🆕 开始创建仓库，提交数据:", submitData);
        // 新增仓库
        const response = await warehouseApi.createWarehouse(submitData);
        console.log("✅ 仓库创建成功，响应:", response);
        // 添加到本地数据
        warehouses.value.unshift(response);
        pagination.total = warehouses.value.length;
        message.success("仓库创建成功");
      }
    } catch (apiError: any) {
      console.error("❌ API调用失败，详细错误信息:", {
        message: apiError.message,
        status: apiError.response?.status,
        statusText: apiError.response?.statusText,
        data: apiError.response?.data,
        config: {
          url: apiError.config?.url,
          method: apiError.config?.method,
          data: apiError.config?.data
        }
      });

      // 根据错误类型显示不同的错误信息
      if (apiError.response?.status === 400) {
        const errorData = apiError.response?.data;
        if (errorData?.errors) {
          // 处理验证错误
          const errorMessages = Object.values(errorData.errors).flat();
          message.error(`请求参数错误: ${errorMessages.join(', ')}`);
        } else {
          message.error(`请求参数错误: ${errorData?.message || '数据格式不正确'}`);
        }
        return; // 不执行模拟操作
      } else if (apiError.response?.status === 409) {
        message.error("仓库编码已存在，请使用其他编码");
        return;
      } else {
        console.warn("API不可用，使用模拟操作:", apiError.message);
      }

      // API失败时的模拟操作
      if (isEdit.value && form.id) {
        // 模拟编辑
        const index = warehouses.value.findIndex(
          (w) => w.id === Number(form.id)
        );
        if (index !== -1) {
          warehouses.value[index] = {
            ...warehouses.value[index],
            ...submitData,
            id: Number(form.id),
            updatedAt: new Date().toISOString(),
          };
        }
        message.success("仓库更新成功（模拟操作）");
      } else {
        // 模拟新增
        const newWarehouse: Warehouse = {
          id: Date.now(), // 使用时间戳作为临时ID
          ...submitData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        } as Warehouse;

        warehouses.value.unshift(newWarehouse);
        pagination.total = warehouses.value.length;
        message.success("仓库创建成功（模拟操作）");
      }

      message.warning("后端服务未启动，数据仅保存在本地");
    }

    modalVisible.value = false;
    resetForm();
  } catch (error) {
    console.error("表单验证失败:", error);
    message.error("请检查表单输入");
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(form, {
    id: "",
    code: "",
    name: "",
    description: "",
    address: "",
    type: "",
    status: "active",
    totalArea: null,
    totalVolume: null,
    contact: "",
    phone: "",
  });
  formRef.value?.resetFields();
};

// 生命周期
onMounted(() => {
  // 加载仓库数据
  loadWarehouses();
});
</script>

<style scoped lang="less">
.warehouse-list {
  .page-header {
    background: white;
    padding: 20px 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);

    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    .page-desc {
      color: #8c8c8c;
      margin: 0;
    }
  }
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;

  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 8px;
  }

  .stat-label {
    color: #8c8c8c;
    font-size: 14px;
  }
}

.content-card {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }
}

.search-form {
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;

  .ant-form-item {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .search-form {
    .ant-col {
      margin-bottom: 16px;
    }
  }
}
</style>
