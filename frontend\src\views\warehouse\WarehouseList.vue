<template>
  <div class="warehouse-list">
    <div class="page-header">
      <h1 class="page-title">仓库管理</h1>
      <p class="page-desc">管理仓库基础信息，配置全局策略和层级结构</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-card class="stat-card">
        <div class="stat-number">3</div>
        <div class="stat-label">总仓库数</div>
      </a-card>
      <a-card class="stat-card">
        <div class="stat-number">12</div>
        <div class="stat-label">总区域数</div>
      </a-card>
      <a-card class="stat-card">
        <div class="stat-number">156</div>
        <div class="stat-label">总货架数</div>
      </a-card>
      <a-card class="stat-card">
        <div class="stat-number">2,340</div>
        <div class="stat-label">总储位数</div>
      </a-card>
    </div>

    <!-- 仓库列表 -->
    <a-card class="content-card">
      <template #title>
        <span class="card-title">仓库列表</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新增仓库
          </a-button>
          <a-button>
            <ExportOutlined />
            导出数据
          </a-button>
        </a-space>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="仓库编码">
                <a-input
                  v-model:value="searchForm.code"
                  placeholder="输入仓库编码"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="仓库名称">
                <a-input
                  v-model:value="searchForm.name"
                  placeholder="输入仓库名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="仓库类型">
                <a-select
                  v-model:value="searchForm.type"
                  placeholder="选择仓库类型"
                >
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="distribution"
                    >配送中心</a-select-option
                  >
                  <a-select-option value="storage">存储仓库</a-select-option>
                  <a-select-option value="production">生产仓库</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="状态">
                <a-select
                  v-model:value="searchForm.status"
                  placeholder="选择状态"
                >
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="active">启用</a-select-option>
                  <a-select-option value="inactive">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" class="text-right">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <SearchOutlined />
                  搜索
                </a-button>
                <a-button @click="handleReset">重置</a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="warehouses"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeLabel(record.type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 'active' ? 'green' : 'red'">
              {{ record.status === "active" ? "启用" : "禁用" }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewZones(record)">
                查看区域
              </a-button>
              <a-button type="link" size="small" @click="editWarehouse(record)">
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="deleteWarehouse(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑仓库模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑仓库' : '新增仓库'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      width="600px"
    >
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="仓库编码" name="code">
              <a-input v-model:value="form.code" placeholder="请输入仓库编码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="仓库名称" name="name">
              <a-input v-model:value="form.name" placeholder="请输入仓库名称" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="仓库类型" name="type">
              <a-select v-model:value="form.type" placeholder="请选择仓库类型">
                <a-select-option value="distribution">配送中心</a-select-option>
                <a-select-option value="storage">存储仓库</a-select-option>
                <a-select-option value="production">生产仓库</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="form.status" placeholder="请选择状态">
                <a-select-option value="active">启用</a-select-option>
                <a-select-option value="inactive">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="地址" name="address">
          <a-input v-model:value="form.address" placeholder="请输入地址" />
        </a-form-item>
        <a-form-item label="联系人" name="contact">
          <a-input v-model:value="form.contact" placeholder="请输入联系人" />
        </a-form-item>
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="form.phone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="form.description"
            placeholder="请输入描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { message, Modal } from "ant-design-vue";
import {
  PlusOutlined,
  ExportOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";

const router = useRouter();

// 响应式数据
const loading = ref(false);
const modalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 搜索表单
const searchForm = reactive({
  code: "",
  name: "",
  type: "",
  status: "",
});

// 表单数据
const form = reactive({
  id: "",
  code: "",
  name: "",
  type: "",
  status: "active",
  address: "",
  contact: "",
  phone: "",
  description: "",
});

// 表单验证规则
const rules = {
  code: [{ required: true, message: "请输入仓库编码" }],
  name: [{ required: true, message: "请输入仓库名称" }],
  type: [{ required: true, message: "请选择仓库类型" }],
  status: [{ required: true, message: "请选择状态" }],
  address: [{ required: true, message: "请输入地址" }],
  contact: [{ required: true, message: "请输入联系人" }],
};

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`,
});

// 表格列配置
const columns = [
  {
    title: "仓库编码",
    dataIndex: "code",
    key: "code",
    width: 120,
  },
  {
    title: "仓库名称",
    dataIndex: "name",
    key: "name",
    width: 150,
  },
  {
    title: "仓库类型",
    dataIndex: "type",
    key: "type",
    width: 120,
  },
  {
    title: "地址",
    dataIndex: "address",
    key: "address",
    ellipsis: true,
  },
  {
    title: "联系人",
    dataIndex: "contact",
    key: "contact",
    width: 100,
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    width: 80,
  },
  {
    title: "操作",
    key: "actions",
    width: 200,
    fixed: "right",
  },
];

// 仓库数据
const warehouses = ref([
  {
    id: "1",
    code: "WH001",
    name: "主仓库",
    type: "distribution",
    address: "北京市朝阳区xx路xx号",
    contact: "张三",
    phone: "13800138000",
    status: "active",
    description: "主要配送中心",
  },
  {
    id: "2",
    code: "WH002",
    name: "分拣中心",
    type: "storage",
    address: "上海市浦东新区xx路xx号",
    contact: "李四",
    phone: "13800138001",
    status: "active",
    description: "存储仓库",
  },
  {
    id: "3",
    code: "WH003",
    name: "生产仓库",
    type: "production",
    address: "广州市天河区xx路xx号",
    contact: "王五",
    phone: "13800138002",
    status: "inactive",
    description: "生产仓库",
  },
]);

// 工具方法
const getTypeColor = (type: string) => {
  const colors = {
    distribution: "blue",
    storage: "green",
    production: "orange",
  };
  return colors[type as keyof typeof colors] || "default";
};

const getTypeLabel = (type: string) => {
  const labels = {
    distribution: "配送中心",
    storage: "存储仓库",
    production: "生产仓库",
  };
  return labels[type as keyof typeof labels] || type;
};

// 事件处理方法
const showAddModal = () => {
  isEdit.value = false;
  resetForm();
  modalVisible.value = true;
};

const handleSearch = () => {
  // 实现搜索逻辑
  message.info("搜索功能待实现");
};

const handleReset = () => {
  Object.assign(searchForm, {
    code: "",
    name: "",
    type: "",
    status: "",
  });
};

const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // 重新加载数据
};

const viewZones = (record: any) => {
  router.push(`/warehouse/zones?warehouseId=${record.id}`);
};

const editWarehouse = (record: any) => {
  isEdit.value = true;
  Object.assign(form, record);
  modalVisible.value = true;
};

const deleteWarehouse = (record: any) => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除仓库 "${record.name}" 吗？`,
    okText: "确定",
    cancelText: "取消",
    onOk: () => {
      // 实现删除逻辑
      message.success("删除成功");
    },
  });
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    // 实现提交逻辑
    message.success(isEdit.value ? "更新成功" : "创建成功");
    modalVisible.value = false;
    resetForm();
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(form, {
    id: "",
    code: "",
    name: "",
    type: "",
    status: "active",
    address: "",
    contact: "",
    phone: "",
    description: "",
  });
  formRef.value?.resetFields();
};

// 生命周期
onMounted(() => {
  // 初始化数据
  pagination.total = warehouses.value.length;
});
</script>

<style scoped lang="less">
.warehouse-list {
  .page-header {
    background: white;
    padding: 20px 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);

    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    .page-desc {
      color: #8c8c8c;
      margin: 0;
    }
  }
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;

  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 8px;
  }

  .stat-label {
    color: #8c8c8c;
    font-size: 14px;
  }
}

.content-card {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }
}

.search-form {
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;

  .ant-form-item {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .search-form {
    .ant-col {
      margin-bottom: 16px;
    }
  }
}
</style>
