using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Constants;
using WMS.API.Data;
using WMS.API.Models;
using WMS.API.Services;
using System.Text.Json;

namespace WMS.API.Controllers
{
    /// <summary>
    /// 配置管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ConfigurationController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfigurationResolutionService _configurationService;
        private readonly ILogger<ConfigurationController> _logger;

        public ConfigurationController(
            ApplicationDbContext context,
            IConfigurationResolutionService configurationService,
            ILogger<ConfigurationController> logger
        )
        {
            _context = context;
            _configurationService = configurationService;
            _logger = logger;
        }

        /// <summary>
        /// 解析配置
        /// </summary>
        /// <param name="request">配置解析请求</param>
        /// <returns>配置解析结果</returns>
        [HttpPost("resolve")]
        public async Task<ActionResult> ResolveConfiguration(
            [FromBody] ConfigurationResolutionRequest request
        )
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ConfigKey))
                {
                    return BadRequest("配置键不能为空");
                }

                if (string.IsNullOrWhiteSpace(request.Context.ClientId))
                {
                    request.Context.ClientId = "DEFAULT_CLIENT";
                }

                var result = await _configurationService.ResolveConfigurationAsync(
                    request.ConfigKey,
                    request.Context
                );
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析配置时发生错误: {ConfigKey}", request.ConfigKey);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 批量解析配置
        /// </summary>
        /// <param name="request">批量配置解析请求</param>
        /// <returns>配置解析结果字典</returns>
        [HttpPost("resolve-batch")]
        public async Task<ActionResult> ResolveConfigurations(
            [FromBody] BatchConfigurationResolutionRequest request
        )
        {
            try
            {
                if (request.ConfigKeys == null || !request.ConfigKeys.Any())
                {
                    return BadRequest("配置键列表不能为空");
                }

                if (string.IsNullOrWhiteSpace(request.Context.ClientId))
                {
                    request.Context.ClientId = "DEFAULT_CLIENT";
                }

                var results = await _configurationService.ResolveConfigurationsAsync(
                    request.ConfigKeys,
                    request.Context
                );
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量解析配置时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 获取客户配置
        /// </summary>
        /// <param name="clientId">客户ID</param>
        /// <returns>配置列表</returns>
        [HttpGet("client/{clientId}")]
        public async Task<ActionResult> GetClientConfigurations(string clientId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(clientId))
                {
                    return BadRequest("客户ID不能为空");
                }

                var configurations = await _configurationService.GetClientConfigurationsAsync(
                    clientId
                );
                return Ok(configurations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户配置时发生错误: {ClientId}", clientId);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 创建配置
        /// </summary>
        /// <param name="configuration">配置对象</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<ActionResult<HierarchicalConfiguration>> CreateConfiguration(
            [FromBody] HierarchicalConfiguration configuration
        )
        {
            try
            {
                if (string.IsNullOrWhiteSpace(configuration.ClientId))
                {
                    return BadRequest("客户ID不能为空");
                }

                if (string.IsNullOrWhiteSpace(configuration.ConfigKey))
                {
                    return BadRequest("配置键不能为空");
                }

                configuration.CreatedBy = User.Identity?.Name;
                configuration.CreatedAt = DateTime.UtcNow;
                configuration.UpdatedAt = DateTime.UtcNow;

                _context.HierarchicalConfigurations.Add(configuration);
                await _context.SaveChangesAsync();

                return CreatedAtAction(
                    nameof(GetConfiguration),
                    new { id = configuration.Id },
                    configuration
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建配置时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 获取配置详情
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>配置详情</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<HierarchicalConfiguration>> GetConfiguration(int id)
        {
            try
            {
                var configuration = await _context.HierarchicalConfigurations.FirstOrDefaultAsync(
                    c => c.Id == id
                );

                if (configuration == null)
                {
                    return NotFound();
                }

                return Ok(configuration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置详情时发生错误: {Id}", id);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <param name="configuration">配置对象</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult> UpdateConfiguration(
            int id,
            [FromBody] HierarchicalConfiguration configuration
        )
        {
            try
            {
                if (id != configuration.Id)
                {
                    return BadRequest("配置ID不匹配");
                }

                var existingConfig = await _context.HierarchicalConfigurations.FirstOrDefaultAsync(
                    c => c.Id == id
                );

                if (existingConfig == null)
                {
                    return NotFound();
                }

                existingConfig.ConfigValue = configuration.ConfigValue;
                existingConfig.Priority = configuration.Priority;
                existingConfig.IsActive = configuration.IsActive;
                existingConfig.EffectiveFrom = configuration.EffectiveFrom;
                existingConfig.EffectiveTo = configuration.EffectiveTo;
                existingConfig.Description = configuration.Description;
                existingConfig.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return Ok(existingConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新配置时发生错误: {Id}", id);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 删除配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteConfiguration(int id)
        {
            try
            {
                var configuration = await _context.HierarchicalConfigurations.FirstOrDefaultAsync(
                    c => c.Id == id
                );

                if (configuration == null)
                {
                    return NotFound();
                }

                _context.HierarchicalConfigurations.Remove(configuration);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除配置时发生错误: {Id}", id);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 获取配置应用日志
        /// </summary>
        /// <param name="operationId">操作ID</param>
        /// <returns>配置应用日志</returns>
        [HttpGet("logs/{operationId}")]
        public async Task<ActionResult> GetConfigurationLogs(string operationId)
        {
            try
            {
                var logs = await _context
                    .ConfigurationApplicationLogs.Include(l => l.AppliedConfig)
                    .Where(l => l.OperationId == operationId)
                    .OrderBy(l => l.AppliedAt)
                    .ToListAsync();

                return Ok(logs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置应用日志时发生错误: {OperationId}", operationId);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 获取配置冲突日志
        /// </summary>
        /// <param name="clientId">客户ID</param>
        /// <returns>配置冲突日志</returns>
        [HttpGet("conflicts/{clientId}")]
        public async Task<ActionResult> GetConfigurationConflicts(string clientId)
        {
            try
            {
                var conflicts = await _context
                    .ConfigurationConflictLogs.Where(c => c.ClientId == clientId)
                    .OrderByDescending(c => c.CreatedAt)
                    .Take(100)
                    .ToListAsync();

                return Ok(conflicts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置冲突日志时发生错误: {ClientId}", clientId);
                return StatusCode(500, "服务器内部错误");
            }
        }

        #region 层级仓库配置管理API

        /// <summary>
        /// 设置仓库级配置
        /// </summary>
        /// <param name="warehouseId">仓库ID</param>
        /// <param name="configuration">配置内容</param>
        /// <returns>设置结果</returns>
        [HttpPut("warehouse/{warehouseId}")]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<IActionResult> SetWarehouseConfiguration(int warehouseId, [FromBody] object configuration)
        {
            try
            {
                var warehouse = await _context.Warehouses.FindAsync(warehouseId);
                if (warehouse == null)
                {
                    return NotFound($"Warehouse with ID {warehouseId} not found");
                }

                warehouse.Configuration = JsonSerializer.Serialize(configuration);
                warehouse.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated configuration for warehouse {Id}", warehouseId);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating warehouse configuration {Id}", warehouseId);
                return StatusCode(500, "Internal server error while updating warehouse configuration");
            }
        }

        /// <summary>
        /// 设置区域级配置
        /// </summary>
        /// <param name="zoneId">区域ID</param>
        /// <param name="configuration">配置内容</param>
        /// <returns>设置结果</returns>
        [HttpPut("zone/{zoneId}")]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<IActionResult> SetZoneConfiguration(int zoneId, [FromBody] object configuration)
        {
            try
            {
                var zone = await _context.Zones.FindAsync(zoneId);
                if (zone == null)
                {
                    return NotFound($"Zone with ID {zoneId} not found");
                }

                zone.Configuration = JsonSerializer.Serialize(configuration);
                zone.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated configuration for zone {Id}", zoneId);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating zone configuration {Id}", zoneId);
                return StatusCode(500, "Internal server error while updating zone configuration");
            }
        }

        /// <summary>
        /// 解析储位的最终有效配置
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <returns>解析后的配置</returns>
        [HttpGet("resolve/{storageLocationId}")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<ActionResult<object>> ResolveStorageLocationConfiguration(int storageLocationId)
        {
            try
            {
                var storageLocation = await _context.StorageLocations
                    .Include(s => s.Shelf)
                        .ThenInclude(sh => sh.Zone)
                            .ThenInclude(z => z.Warehouse)
                    .FirstOrDefaultAsync(s => s.Id == storageLocationId);

                if (storageLocation == null)
                {
                    return NotFound($"Storage location with ID {storageLocationId} not found");
                }

                var resolvedConfig = await ResolveHierarchicalConfiguration(storageLocation);

                _logger.LogInformation("Resolved configuration for storage location {Id}", storageLocationId);
                return Ok(resolvedConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving configuration for storage location {Id}", storageLocationId);
                return StatusCode(500, "Internal server error while resolving configuration");
            }
        }

        /// <summary>
        /// 查看配置的层级继承路径
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <returns>配置继承路径</returns>
        [HttpGet("hierarchy/{storageLocationId}")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<ActionResult<object>> GetConfigurationHierarchy(int storageLocationId)
        {
            try
            {
                var storageLocation = await _context.StorageLocations
                    .Include(s => s.Shelf)
                        .ThenInclude(sh => sh.Zone)
                            .ThenInclude(z => z.Warehouse)
                    .FirstOrDefaultAsync(s => s.Id == storageLocationId);

                if (storageLocation == null)
                {
                    return NotFound($"Storage location with ID {storageLocationId} not found");
                }

                var hierarchy = await GetConfigurationInheritancePath(storageLocation);

                return Ok(new
                {
                    StorageLocationId = storageLocationId,
                    HierarchyPath = new
                    {
                        Warehouse = new { Id = storageLocation.Shelf.Zone.WarehouseId, Name = storageLocation.Shelf.Zone.Warehouse.Name },
                        Zone = new { Id = storageLocation.Shelf.ZoneId, Name = storageLocation.Shelf.Zone.Name },
                        Shelf = new { Id = storageLocation.ShelfId, Name = storageLocation.Shelf.Name },
                        StorageLocation = new { Id = storageLocationId, Code = storageLocation.Code }
                    },
                    ConfigurationSources = hierarchy
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving configuration hierarchy for storage location {Id}", storageLocationId);
                return StatusCode(500, "Internal server error while retrieving configuration hierarchy");
            }
        }

        /// <summary>
        /// 批量应用配置到指定层级及其子层级
        /// </summary>
        /// <param name="request">批量配置请求</param>
        /// <returns>应用结果</returns>
        [HttpPost("apply-batch")]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<ActionResult<object>> ApplyBatchConfiguration([FromBody] BatchConfigurationRequest request)
        {
            try
            {
                var appliedCount = 0;

                // 根据目标层级应用配置
                switch (request.TargetLevel.ToUpper())
                {
                    case "WAREHOUSE":
                        appliedCount = await ApplyWarehouseConfiguration(request);
                        break;
                    case "ZONE":
                        appliedCount = await ApplyZoneConfiguration(request);
                        break;
                    case "SHELF":
                        appliedCount = await ApplyShelfConfiguration(request);
                        break;
                    default:
                        return BadRequest($"Unsupported target level: {request.TargetLevel}");
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Applied batch configuration to {Level} {Id}: {Applied} applied", 
                    request.TargetLevel, request.TargetId, appliedCount);

                return Ok(new
                {
                    TargetLevel = request.TargetLevel,
                    TargetId = request.TargetId,
                    AppliedCount = appliedCount,
                    Message = $"Successfully applied configuration to {appliedCount} items"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying batch configuration");
                return StatusCode(500, "Internal server error while applying batch configuration");
            }
        }

        #endregion

        #region 私有方法

        private async Task<Dictionary<string, object>> ResolveHierarchicalConfiguration(StorageLocation storageLocation)
        {
            var resolvedConfig = new Dictionary<string, object>();

            // 仓库级配置
            if (storageLocation.Shelf.Zone.Warehouse.Configuration != null)
            {
                AddConfigurationToResult(resolvedConfig, storageLocation.Shelf.Zone.Warehouse.Configuration, "Warehouse");
            }

            // 区域级配置
            if (storageLocation.Shelf.Zone.Configuration != null)
            {
                AddConfigurationToResult(resolvedConfig, storageLocation.Shelf.Zone.Configuration, "Zone");
            }

            // 货架级配置
            if (storageLocation.Shelf.Configuration != null)
            {
                AddConfigurationToResult(resolvedConfig, storageLocation.Shelf.Configuration, "Shelf");
            }

            return resolvedConfig;
        }

        private void AddConfigurationToResult(Dictionary<string, object> result, object configuration, string source)
        {
            if (configuration != null)
            {
                result[source] = configuration;
            }
        }

        private async Task<List<object>> GetConfigurationInheritancePath(StorageLocation storageLocation)
        {
            var path = new List<object>();

            // 仓库级配置
            if (storageLocation.Shelf.Zone.Warehouse.Configuration != null)
            {
                path.Add(new
                {
                    Level = "Warehouse",
                    TargetId = storageLocation.Shelf.Zone.WarehouseId,
                    TargetName = storageLocation.Shelf.Zone.Warehouse.Name,
                    Configuration = storageLocation.Shelf.Zone.Warehouse.Configuration
                });
            }

            // 区域级配置
            if (storageLocation.Shelf.Zone.Configuration != null)
            {
                path.Add(new
                {
                    Level = "Zone",
                    TargetId = storageLocation.Shelf.ZoneId,
                    TargetName = storageLocation.Shelf.Zone.Name,
                    Configuration = storageLocation.Shelf.Zone.Configuration
                });
            }

            // 货架级配置
            if (storageLocation.Shelf.Configuration != null)
            {
                path.Add(new
                {
                    Level = "Shelf",
                    TargetId = storageLocation.ShelfId,
                    TargetName = storageLocation.Shelf.Name,
                    Configuration = storageLocation.Shelf.Configuration
                });
            }

            return path;
        }

        private async Task<int> ApplyWarehouseConfiguration(BatchConfigurationRequest request)
        {
            var warehouse = await _context.Warehouses.FindAsync(int.Parse(request.TargetId));
            if (warehouse == null) return 0;

            if (warehouse.Configuration == null || request.OverrideExisting)
            {
                warehouse.Configuration = JsonSerializer.Serialize(request.Configuration);
                warehouse.UpdatedAt = DateTime.UtcNow;
                return 1;
            }

            return 0;
        }

        private async Task<int> ApplyZoneConfiguration(BatchConfigurationRequest request)
        {
            var zone = await _context.Zones.FindAsync(int.Parse(request.TargetId));
            if (zone == null) return 0;

            if (zone.Configuration == null || request.OverrideExisting)
            {
                zone.Configuration = JsonSerializer.Serialize(request.Configuration);
                zone.UpdatedAt = DateTime.UtcNow;
                return 1;
            }

            return 0;
        }

        private async Task<int> ApplyShelfConfiguration(BatchConfigurationRequest request)
        {
            var shelf = await _context.Shelves.FindAsync(int.Parse(request.TargetId));
            if (shelf == null) return 0;

            if (shelf.Configuration == null || request.OverrideExisting)
            {
                shelf.Configuration = JsonSerializer.Serialize(request.Configuration);
                shelf.UpdatedAt = DateTime.UtcNow;
                return 1;
            }

            return 0;
        }

        #endregion
    }

    /// <summary>
    /// 配置解析请求
    /// </summary>
    public class ConfigurationResolutionRequest
    {
        /// <summary>
        /// 配置键
        /// </summary>
        public string ConfigKey { get; set; } = string.Empty;

        /// <summary>
        /// 配置上下文
        /// </summary>
        public ConfigurationContext Context { get; set; } = new();
    }

    /// <summary>
    /// 批量配置解析请求
    /// </summary>
    public class BatchConfigurationResolutionRequest
    {
        /// <summary>
        /// 配置键列表
        /// </summary>
        public List<string> ConfigKeys { get; set; } = new();

        /// <summary>
        /// 配置上下文
        /// </summary>
        public ConfigurationContext Context { get; set; } = new();
    }

    /// <summary>
    /// 批量配置应用请求
    /// </summary>
    public class BatchConfigurationRequest
    {
        /// <summary>
        /// 目标层级（WAREHOUSE, ZONE, SHELF）
        /// </summary>
        public string TargetLevel { get; set; } = string.Empty;

        /// <summary>
        /// 目标ID
        /// </summary>
        public string TargetId { get; set; } = string.Empty;

        /// <summary>
        /// 配置键值对
        /// </summary>
        public Dictionary<string, object> Configuration { get; set; } = new();

        /// <summary>
        /// 是否应用到子层级
        /// </summary>
        public bool ApplyToChildren { get; set; } = false;

        /// <summary>
        /// 是否覆盖已存在的配置
        /// </summary>
        public bool OverrideExisting { get; set; } = false;
    }
}
