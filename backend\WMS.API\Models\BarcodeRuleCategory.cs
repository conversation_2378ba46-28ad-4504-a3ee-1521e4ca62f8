using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 条码规则分类
    /// </summary>
    public class BarcodeRuleCategory
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 分类代码，如：MATERIAL, STORAGE_LOCATION, WORK_ORDER, PACKAGE
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CategoryCode { get; set; } = string.Empty;

        /// <summary>
        /// 分类名称，如：物料条码, 储位条码, 工单条码, 包装条码
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 分类描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 关联的解析规则集合
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<BarcodeParsingRule>? BarcodeParsingRules { get; set; }
    }
}
