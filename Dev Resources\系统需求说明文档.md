### **基于WS2812灯带的仓储管理系统 - 系统需求说明**

**版本:** 4.0 (层级架构版)
**日期:** 2025年7月4日

------

### **1.0 系统概述**

本项目旨在开发一个集成了视觉引导功能的现代化仓储管理系统。系统采用四级层级架构（仓库→区域→货架→储位），通过软件精确控制安装在货架上的LED灯带，能够以灯光形式直观地指引仓库作业人员，从而显著提升物料上架、拣选、盘点、移库等所有核心仓储操作的速度和准确性，并实现库存的精确实时管理。系统致力于成为一个功能完整、数据驱动、高度智能化、支持多行业应用的仓储运营平台。

### **2.0 系统架构与核心特性**

#### **2.1 层级架构设计**

系统采用四级层级结构，支持大型复杂仓库管理：

```
🏭 Warehouse (仓库)
└── 🏢 Zone (区域) 
    └── 📚 Shelf (货架)
        └── 📦 StorageLocation (储位)
            ├── ⚡ LocationCapability (储位能力)
            └── 🏷️ LocationClassification (储位分类)
```

**设计优势:**
- **空间组织性**: 清晰的物理和逻辑空间划分
- **配置继承**: 从仓库到储位的配置层级继承机制
- **灵活扩展**: 支持能力和分类系统的灵活扩展
- **多行业适配**: 医疗、电商、制造、食品等行业个性化配置

#### **2.2 混合能力模型**

**核心设计理念**: 结合传统层级关系的性能优势与现代灵活配置的扩展性

**能力系统 (LocationCapability)**:
- 温度控制能力 (精确温度管理)
- 危险品存储能力 (安全认证管理)
- 贵重物品能力 (高安全级别)
- 易碎品处理能力 (特殊包装要求)
- 生物样品存储能力 (医疗级别管理)
- 食品级存储能力 (食品安全认证)

**分类系统 (LocationClassification)**:
- 行业维度: 医疗/电商/制造/食品
- 材料类型: 原料/成品/包装/工具
- 安全等级: 标准/受限/管制/机密
- 处理方式: 常规/易碎/重型/危险品
- 温度要求: 常温/冷藏/冷冻/加热

### **3.0 技术栈约束**

#### **3.1 技术栈约束**

- **后端服务:** 必须使用 .NET 9 Web API 技术栈
- **前端应用:** 必须使用 Vue 3 技术栈
- **数据库:** 必须使用 PostgreSQL 15
- **ORM框架:** Entity Framework Core 9

#### **3.2 部署方式约束**

- **容器化部署:** 系统所有组件（后端、前端）都必须能够通过 Docker 进行容器化部署。必须提供相应的配置文件以支持一键式环境搭建
- **支持离线部署**: 客户现场很可能是内网环境，需要支持导入tar包，然后一键启动

#### **3.3 兼容性要求**

- **跨设备访问:** 系统必须提供统一的Web访问入口，并确保在PC（个人电脑）和PDA（手持数据终端）两种设备上都能获得良好、流畅的操作体验。界面需自适应不同尺寸的屏幕

### **4.0 数据模型与关系**

#### **4.1 核心数据模型**

**Warehouse (仓库模型)**
- 基本信息: 编码、名称、地址、类型、状态
- 物理属性: 总面积、总体积、联系人信息
- 配置信息: 全局配置策略(JSON格式)

**Zone (区域模型)**
- 基本信息: 编码、名称、描述、状态
- 功能属性: 区域类型、安全等级、环境控制
- 环境参数: 温度范围、湿度范围、特殊要求
- 配置信息: 区域级配置策略(JSON格式)

**Shelf (货架模型)**
- 基本信息: 编码、名称、描述、状态
- 物理属性: 尺寸、层数、每层位数、承重
- 安全信息: 材质、制造商、安全认证、检查记录
- 配置信息: 货架级配置策略(JSON格式)

**StorageLocation (储位模型)** - 增强版
- 基本信息: 编码、名称、描述、状态
- 层级关系: 关联货架ID（外键）
- LED控制: ESP32控制器、LED位置、通道号
- 物理属性: 容量、最大重量、尺寸、坐标
- 扩展信息: 能力集合、分类集合、配置信息
- 兼容字段: Zone、ShelfCode（向后兼容）

#### **4.2 高级功能模型**

**LocationCapability (储位能力模型)**
- 能力定义: 能力类型、等级、名称、描述
- 参数配置: 能力参数(JSON)、验证规则(JSON)
- 有效期管理: 生效时间、失效时间、认证信息
- 验证周期: 上次验证、下次验证、验证频率

**LocationClassification (储位分类模型)**
- 分类定义: 维度、类别、值、显示名称
- 业务属性: 标签、属性、业务规则(JSON)
- 继承机制: 是否继承、继承来源、自动分配
- 外部集成: 外部系统引用、同步状态

### **5.0 硬件集成需求**

系统后端必须能够与部署在仓库现场的ESP32硬件控制器进行通信，以实现对灯光的控制。

- **通信方式:** 后端系统作为客户端，通过标准的HTTP网络请求，调用每个ESP32控制器上运行的Web API服务
- **控制器寻址:** 系统必须能够存储和管理每个ESP32控制器的网络地址（IP地址和端口）。当需要控制某个储位的灯光时，系统能根据储位绑定的控制器信息，向正确的地址发送指令
- **健康检查:** 系统应具备监控硬件控制器在线状态的能力，支持自动健康检查（每5分钟）

**LED控制指令增强:**
```csharp
// 增强的LED控制指令
SEND_HTTP_REQUEST(
  target_controller_ip,
  port, 
  command: "SET_LIGHT",
  payload: {
    channel: 0,                 // LED通道号 (0-3)
    start_position: 5,          // 灯带上的起始LED编号
    end_position: 10,           // 灯带上的结束LED编号
    color: "red",               // 灯光颜色 (red, green, blue, yellow, etc.)
    brightness: 255,            // 亮度 (0-255)
    effect: "blinking",         // 灯光效果 (常亮, 闪烁, 呼吸)
    duration: 30000             // 持续时间 (毫秒, 0为持续点亮)
  }
)

// 新增大指示灯控制
SEND_HTTP_REQUEST(
  target_controller_ip,
  port,
  command: "CONTROL_BIG_LED", 
  payload: {
    indicator_index: 1,         // 指示灯索引 (Y轴位置)
    turn_on: true              // 开关状态
  }
)
```

### **6.0 功能性需求**

#### **6.1 用户与权限管理** (增强版)

- **基于角色的访问控制 (RBAC)**: 使用ASP.NET Core Identity框架
- **多级权限系统**:
  - **SuperAdmin**: 系统管理员，拥有所有权限
  - **Admin**: 用户管理和系统配置权限  
  - **WarehouseManager**: 仓库运营管理权限
  - **Operator**: 基础仓库操作权限
  - **ReadOnly**: 只读访问权限

- **JWT认证机制**: 
  - Access Token (1小时有效期)
  - Refresh Token (7天有效期，支持自动刷新)
  - 安全的令牌轮换机制

#### **6.2 层级数据管理** (新增)

**仓库管理**:
- 创建、编辑、查看多个仓库
- 配置仓库级全局策略
- 仓库运营统计和报表

**区域管理**:
- 创建功能区域（常温、冷藏、冷冻、危险品等）
- 环境参数配置（温度、湿度、安全等级）
- 区域级业务规则配置

**货架管理**:
- 货架注册和规格管理
- 安全认证和检查记录
- 货架级存储策略配置

**储位管理** (增强版):
- 层级化储位组织
- LED控制信息配置
- 能力和分类分配
- 批量导入/导出功能

#### **6.3 配置继承系统** (新增)

**配置层级 (优先级从高到低)**:
1. StorageLocation Level (储位级) - 最高优先级
2. Shelf Level (货架级)
3. Zone Level (区域级)  
4. Warehouse Level (仓库级) - 最低优先级

**配置解析功能**:
- 自动配置继承和覆盖
- 配置解析路径追踪
- 冲突检测和解决
- 批量配置应用

**行业配置模板**:
- 医疗行业: 温度监控、审计跟踪、合规管理
- 电商行业: 拣货优化、批次处理、季节调整
- 制造业: FIFO管理、批次追踪、质量控制
- 食品行业: 食品安全、保质期管理、可追溯性

#### **6.4 基础数据管理** (更新)

- **控制器管理**: 提供界面用于注册、编辑和查看所有ESP32硬件控制器及其网络信息和在线状态。ESP32实现了mDNS
- **储位管理**: 
  - 数字化定义仓库物理布局，支持层级化管理
  - 将每个储位与具体的灯光控制信息（控制器ID、LED起止位置）强绑定
  - 提供"灯光测试"功能
  - 支持能力分配和分类管理
- **物料管理**: 提供物料主数据管理功能，记录SKU、名称、规格等信息

#### **6.5 智能条码解析系统** (新增)

**统一条码解析引擎**:
- 支持多种条码标准和自定义格式
- 智能识别条码类型（储位、物料、LPN、任务单等）
- 可配置的解析规则和字段映射
- 客户端特定的解析策略

**解析规则管理**:
- 正则表达式模式匹配
- 字段提取和验证
- 优先级和权重配置
- 解析日志和审计

**应用场景**:
- 储位条码: 自动定位储位层级信息
- 物料条码: 提取SKU、批次、有效期等信息
- LPN条码: 容器/托盘管理
- 任务条码: 快速任务定位和执行

#### **6.6 库存核心管理** (更新)

- **实时库存**: 系统必须能实时、准确地记录每个储位中，每种物料（可按批次、容器区分）的库存数量
- **高级状态管理**:
  - **储位状态**: 储位需支持 `可用`、`锁定`、`维修中`、`已占用`、`部分占用`、`损坏`、`隔离` 等状态
  - **库存状态**: 库存需支持 `合格`、`待检`、`冻结` 等状态。只有 `合格` 状态的库存才能被拣选
- **容器/托盘管理 (LPN)**:
  - 系统需支持生成和管理唯一的容器码（LPN），用于绑定一个或多个物料
  - 所有库内操作均应支持直接扫描LPN，以实现整箱或整托盘的快速移动和处理

#### **6.7 核心作业流程** (增强LED引导)

**入库流程** (智能储位推荐):
1. 作业员在PDA上发起入库，扫描物料（或将物料装入LPN并扫描LPN）
2. 系统基于物料属性、储位能力、分类规则智能推荐储位
3. **点亮推荐储位灯光（绿色）**，支持多个候选储位
4. 作业员选择储位放货后扫描储位码确认，灯光熄灭，库存增加

**出库流程** (批量优化):
1. 作业员在PDA上接收出库任务
2. 系统定位储位，**点亮目标储位灯光（红色）**
3. 支持批量出库，依次点亮多个储位
4. 作业员取货后扫描储位码确认，灯光熄灭，库存扣减

**智能拣货作业**:
1. 主管在PC端通过上传文件或手动创建批量拣货任务
2. 系统生成优化路径的拣货清单，考虑储位布局和作业效率
3. 作业员在PDA上执行任务，系统**依次点亮待拣货储位（蓝色）**
4. 支持波次拣货、批次拣货等多种策略

**库内移库流程** (双向指示):
1. 作业员在PDA上发起移库，扫描源储位、物料（或LPN）
2. 系统或作业员指定目标储位，验证储位能力匹配
3. 系统**同时点亮源储位（红色）和目标储位（绿色）**
4. 作业员完成物理移动并扫描目标储位确认，灯光熄灭，库存位置更新

**盘点与库存矫正流程**:
1. 主管在PC端按仓库、区域、货架、物料等维度创建并分配盘点任务
2. 作业员在PDA上执行任务，系统**依次点亮待盘点储位（黄色）**
3. 作业员清点并录入实际数量，支持差异标记
4. 盘点完成后，系统生成差异报告，支持层级汇总
5. 主管审核差异，并可一键执行库存矫正，所有矫正操作必须留痕

#### **6.8 管理与监控** (多维度分析)

**数据看板 (Dashboard)**:
- **层级概览**: 仓库→区域→货架→储位的层级统计
- **利用率分析**: 按不同维度的空间利用率统计
- **作业效率**: 各类作业的效率指标和趋势分析
- **硬件状态**: ESP32控制器健康状态监控
- **行业指标**: 针对不同行业的专业KPI展示

**报表与分析**:
- **库存报表**: 支持按层级、能力、分类的库存分析
- **作业报表**: 入库、出库、移库、盘点的详细统计
- **效率分析**: 储位周转率、作业时效、准确率统计
- **异常分析**: 差异分析、错误操作统计、硬件故障统计

**操作追溯与异常处理**:
- **审计日志**: 记录所有关键操作的详细信息（何人、何时、何地、何事、为何）
- **异常处理**: 标准流程处理日常操作异常（如拣错货、放错位）
- **数据修正**: 支持有权限的主管执行"冲销"或"回滚"，记录原因
- **合规支持**: 支持医疗、食品等行业的合规审计要求

### **7.0 第三方集成需求**

- **RESTful API**: 系统后端必须提供一套定义清晰、独立于前端UI的业务逻辑API
- **API功能覆盖**: 第三方系统（如ERP、MES）能够以编程方式与本系统进行数据交互和功能调用
- **核心集成功能**:
  - 控制储位LED灯光
  - 查询层级化库存信息
  - 创建和管理作业任务
  - 获取配置和能力信息
  - 实时状态监控
- **安全认证**: API必须使用JWT Token进行安全认证和授权
- **API文档**: 提供Swagger/OpenAPI文档支持

### **8.0 非功能性需求**

- **性能**: 
  - 核心操作响应时间 < 2秒
  - LED控制响应时间 < 1秒
  - 支持并发用户数 > 100
  - 硬件调用超时机制 (10秒)

- **可靠性**: 
  - 系统可用性 > 99.5%
  - 优雅处理硬件离线和网络故障
  - 自动故障恢复机制
  - 数据备份和恢复策略

- **安全性**: 
  - 密码加密存储 (bcrypt)
  - JWT令牌安全机制
  - API访问速率限制
  - 操作审计和监控
  - 数据传输加密 (HTTPS)

- **可扩展性**: 
  - 微服务架构设计
  - 模块化组件设计
  - 配置驱动的业务逻辑
  - 插件化能力和分类系统
  - 水平扩展支持

- **兼容性**:
  - 支持现有数据的平滑迁移
  - 向后兼容的API设计
  - 多浏览器支持
  - 移动设备自适应

### **9.0 行业特定需求**

#### **9.1 医疗行业**
- **温度监控**: 连续监控、实时报警、数据记录
- **合规管理**: FDA、WHO、NMPA等法规支持
- **审计跟踪**: 完整的操作记录和数据完整性
- **访问控制**: 生物识别、双重认证、权限分级

#### **9.2 电商行业**  
- **拣货优化**: 波次拣货、路径优化、批量处理
- **季节管理**: 季节性商品调整、动态储位分配
- **退货处理**: 快速退货流程、质量检查、再入库

#### **9.3 制造业**
- **批次管理**: FIFO执行、批次追踪、质量控制
- **原料管理**: MRP集成、供应商链接、提前期管理
- **成品管理**: 质量分级、包装追踪、发货准备

#### **9.4 食品行业**
- **保质期管理**: 先进先出、过期预警、自动处理
- **可追溯性**: 从原料到成品的完整追踪链
- **食品安全**: HACCP支持、温度记录、卫生标准

### **10.0 部署和维护需求**

- **容器化部署**: Docker + Docker Compose一键部署
- **离线安装**: 支持内网环境tar包安装
- **数据迁移**: 自动数据库迁移和版本管理
- **监控告警**: 系统性能监控、硬件状态监控
- **日志管理**: 结构化日志、集中化管理
- **备份策略**: 自动备份、灾难恢复

---

**总结**: 本系统通过四级层级架构和混合能力模型，实现了高度灵活、可扩展的仓储管理解决方案，能够满足多行业的专业化需求，同时保持优秀的性能和用户体验。