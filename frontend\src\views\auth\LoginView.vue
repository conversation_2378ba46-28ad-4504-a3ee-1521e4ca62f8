<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-left">
        <div class="logo">
          <div class="logo-icon">📦</div>
          <span>WMS-VGL</span>
        </div>
        <div class="tagline">智能仓储管理系统</div>
        <ul class="features">
          <li>可视化LED引导存取</li>
          <li>四级层级结构管理</li>
          <li>实时库存监控</li>
          <li>智能设备集成</li>
          <li>多角色权限控制</li>
        </ul>
      </div>
      
      <div class="login-right">
        <a-form
          :model="form"
          :rules="rules"
          @finish="handleLogin"
          class="login-form"
          layout="vertical"
        >
          <div class="form-title">登录系统</div>
          <div class="form-subtitle">欢迎使用 WMS-VGL 仓库管理系统</div>
          
          <a-form-item name="email" label="邮箱">
            <a-input
              v-model:value="form.email"
              placeholder="请输入邮箱"
              size="large"
            >
              <template #prefix>
                <MailOutlined />
              </template>
            </a-input>
          </a-form-item>
          
          <a-form-item name="password" label="密码">
            <a-input-password
              v-model:value="form.password"
              placeholder="请输入密码"
              size="large"
            >
              <template #prefix>
                <LockOutlined />
              </template>
            </a-input-password>
          </a-form-item>
          
          <a-form-item>
            <div class="login-options">
              <a-checkbox v-model:checked="form.rememberMe">记住我</a-checkbox>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>
          </a-form-item>
          
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              block
              :loading="loading"
            >
              登录
            </a-button>
          </a-form-item>
          
          <div class="quick-login">
            <a-button
              type="link"
              size="small"
              @click="quickLogin"
              :loading="loading"
            >
              快速登录（管理员）
            </a-button>
          </div>
        </a-form>
        
        <div class="demo-accounts">
          <div class="demo-title">快速登录演示账号</div>
          <div class="demo-account" v-for="account in demoAccounts" :key="account.role">
            <div>
              <strong>{{ account.email }}</strong>
              <div style="color: #8c8c8c; font-size: 11px;">{{ account.role }}</div>
            </div>
            <a-button
              type="link"
              size="small"
              @click="fillAccount(account)"
            >
              快速登录
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { MailOutlined, LockOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import type { LoginRequest } from '@/types'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)

const form = reactive<LoginRequest>({
  email: '',
  password: '',
  rememberMe: false,
})

const rules = {
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入有效的邮箱地址' },
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度至少6位' },
  ],
}

const demoAccounts = [
  { role: '超级管理员', email: '<EMAIL>', password: 'Admin@123456' },
  { role: '仓库经理', email: '<EMAIL>', password: 'Manager@123456' },
  { role: '操作员', email: '<EMAIL>', password: 'Operator@123456' },
]

const handleLogin = async () => {
  loading.value = true
  
  try {
    const result = await authStore.login(form)
    
    if (result.success) {
      message.success('登录成功')
      const redirect = router.currentRoute.value.query.redirect as string
      router.push(redirect || '/')
    } else {
      message.error(result.message || '登录失败')
    }
  } catch (error: any) {
    message.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

const quickLogin = () => {
  form.email = '<EMAIL>'
  form.password = 'Admin@123456'
  form.rememberMe = true
  handleLogin()
}

const fillAccount = (account: typeof demoAccounts[0]) => {
  form.email = account.email
  form.password = account.password
}
</script>

<style scoped lang="less">
.login-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.175);
  overflow: hidden;
  width: 100%;
  max-width: 900px;
  display: flex;
  min-height: 500px;
}

.login-left {
  flex: 1;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: white;
}

.logo {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.tagline {
  font-size: 18px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.features {
  list-style: none;
  padding: 0;
  
  li {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    opacity: 0.9;
    
    &::before {
      content: '✓';
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }
}

.login-right {
  flex: 1;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-form {
  width: 100%;
  max-width: 320px;
  margin: 0 auto;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  text-align: center;
}

.form-subtitle {
  color: #8c8c8c;
  text-align: center;
  margin-bottom: 32px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-password {
  color: #1890ff;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

.quick-login {
  text-align: center;
  margin-top: 16px;
}

.demo-accounts {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
}

.demo-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
}

.demo-account {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;

  &:last-child {
    border-bottom: none;
  }
}

@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    max-width: 400px;
  }
  
  .login-left {
    padding: 40px 30px;
    text-align: center;
  }
  
  .login-right {
    padding: 40px 30px;
  }
}
</style>