namespace WMS.API.DTOs.ESP32
{
    public class LedControlRequest
    {
        public List<LedConfiguration> Leds { get; set; } = new List<LedConfiguration>();
    }

    public class LedConfiguration
    {
        public int Channel { get; set; }
        public int Start { get; set; }
        public int Count { get; set; }
        public int Brightness { get; set; }
        public string Color { get; set; } = string.Empty;
    }

    public class LedControlResponse
    {
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public int ProcessedCount { get; set; }
        public int TotalReceived { get; set; }
    }

    public class DeviceInfoResponse
    {
        public bool FactoryMode { get; set; }
        public int LedCount { get; set; }
        public int MaxLedCount { get; set; }
        public int BlinkInterval { get; set; }
        public int LedRefreshRate { get; set; }
        public bool WifiEnabled { get; set; }
        public string WifiSSID { get; set; } = string.Empty;
        public bool WifiConnected { get; set; }
        public bool UseStaticIP { get; set; }
        public string StaticIP { get; set; } = string.Empty;
        public string Subnet { get; set; } = string.Empty;
        public string Gateway { get; set; } = string.Empty;
        public bool EthernetConnected { get; set; }
        public string FirmwareVersion { get; set; } = string.Empty;
        public long Uptime { get; set; }
        public long FreeHeap { get; set; }
    }
}
