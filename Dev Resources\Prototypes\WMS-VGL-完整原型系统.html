<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WMS-VGL 仓库管理系统 - 高保真原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #000000d9;
            line-height: 1.5715;
        }

        .app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 登录页面样式 */
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.175);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            display: flex;
            min-height: 500px;
        }

        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: white;
            position: relative;
        }

        .logo {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tagline {
            font-size: 18px;
            margin-bottom: 32px;
            opacity: 0.9;
        }

        .features {
            list-style: none;
        }

        .features li {
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            opacity: 0.9;
        }

        .features li::before {
            content: '✓';
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-form {
            width: 100%;
            max-width: 320px;
            margin: 0 auto;
        }

        .form-title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
            text-align: center;
        }

        .form-subtitle {
            color: #8c8c8c;
            text-align: center;
            margin-bottom: 32px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            color: #262626;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
            background: #fff;
        }

        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .login-btn {
            width: 100%;
            padding: 12px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 16px;
        }

        .login-btn:hover {
            background: #40a9ff;
        }

        .demo-accounts {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .demo-title {
            font-size: 14px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 12px;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 12px;
        }

        .demo-account:last-child {
            border-bottom: none;
        }

        .demo-btn {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            font-size: 12px;
        }

        /* 主应用样式 */
        .main-app {
            display: none;
            min-height: 100vh;
            flex-direction: column;
        }

        .header {
            background: #001529;
            color: white;
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            position: relative;
            z-index: 1000;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-logo {
            color: white;
            font-size: 20px;
            font-weight: 600;
            margin-right: 32px;
        }

        .nav {
            display: flex;
            gap: 24px;
        }

        .nav-item {
            color: rgba(255, 255, 255, 0.85);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
            cursor: pointer;
        }

        .nav-item:hover, .nav-item.active {
            background-color: #1890ff;
            color: white;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: rgba(255, 255, 255, 0.85);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .logout-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.85);
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .main-layout {
            flex: 1;
            display: flex;
        }

        .sidebar {
            width: 200px;
            background: white;
            border-right: 1px solid #f0f0f0;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.02);
            transition: all 0.3s ease;
            position: relative;
            z-index: 100;
        }

        .sidebar.collapsed {
            width: 0;
            overflow: hidden;
        }

        .sidebar-menu {
            padding: 16px 0;
        }

        .menu-item {
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .menu-item:hover {
            background: #f5f5f5;
        }

        .menu-item.active {
            background: #e6f7ff;
            border-left-color: #1890ff;
            color: #1890ff;
        }

        .menu-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        .content-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-toggle {
            position: absolute;
            top: 16px;
            left: 16px;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px;
            cursor: pointer;
            z-index: 1001;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .sidebar-toggle:hover {
            background: #f5f5f5;
        }

        .page-header {
            background: white;
            padding: 20px 24px;
            border-radius: 8px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .page-desc {
            color: #8c8c8c;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            color: #8c8c8c;
        }

        .breadcrumb-item {
            color: #8c8c8c;
            text-decoration: none;
        }

        .breadcrumb-item:hover {
            color: #1890ff;
        }

        .breadcrumb-item.active {
            color: #262626;
        }

        .content-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #262626;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-success {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
        }

        .btn-warning {
            background: #fa8c16;
            border-color: #fa8c16;
            color: white;
        }

        .btn-danger {
            background: #ff4d4f;
            border-color: #ff4d4f;
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 8px;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #8c8c8c;
            font-size: 14px;
        }

        .search-form {
            padding: 16px 24px;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .form-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .form-item label {
            font-size: 14px;
            color: #262626;
        }

        .form-item input,
        .form-item select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            width: 200px;
            transition: border-color 0.3s;
        }

        .form-item input:focus,
        .form-item select:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th,
        .table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
        }

        .table tr:hover {
            background: #f5f5f5;
        }

        .tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid;
        }

        .tag-success {
            background: #f6ffed;
            border-color: #b7eb8f;
            color: #52c41a;
        }

        .tag-warning {
            background: #fff7e6;
            border-color: #ffd591;
            color: #fa8c16;
        }

        .tag-error {
            background: #fff2f0;
            border-color: #ffccc7;
            color: #ff4d4f;
        }

        .tag-info {
            background: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-text {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .btn-text:hover {
            background: #e6f7ff;
        }

        .btn-text.danger {
            color: #ff4d4f;
        }

        .btn-text.danger:hover {
            background: #fff2f0;
        }

        .btn-text.success {
            color: #52c41a;
        }

        .btn-text.success:hover {
            background: #f6ffed;
        }

        .pagination {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }

        .pagination-info {
            color: #8c8c8c;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.45);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }

        .form-grid .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-grid .form-group label {
            font-size: 14px;
            color: #262626;
        }

        .form-grid .form-group input,
        .form-grid .form-group select,
        .form-grid .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            transition: border-color 0.3s;
        }

        .form-grid .form-group input:focus,
        .form-grid .form-group select:focus,
        .form-grid .form-group textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .hidden {
            display: none !important;
        }

        /* LED控制面板 */
        .led-control-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
            padding: 24px;
            margin-bottom: 24px;
        }

        .led-preview {
            background: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
        }

        .led-strip {
            display: flex;
            gap: 4px;
            margin-bottom: 16px;
        }

        .led-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #d9d9d9;
            border: 1px solid #ccc;
            transition: all 0.3s;
        }

        .led-dot.active {
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .color-picker {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 6px;
            border: 2px solid #d9d9d9;
            cursor: pointer;
            transition: all 0.3s;
        }

        .color-option:hover {
            border-color: #1890ff;
            transform: scale(1.1);
        }

        .color-option.selected {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 快速操作卡片 */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .quick-action-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
            border: 1px solid #f0f0f0;
            transition: all 0.3s;
            cursor: pointer;
        }

        .quick-action-card:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        .quick-action-icon {
            width: 32px;
            height: 32px;
            margin-bottom: 16px;
            fill: #1890ff;
        }

        .quick-action-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .quick-action-desc {
            color: #8c8c8c;
            margin-bottom: 16px;
        }

        /* 标签页 */
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
            margin-bottom: 0;
        }

        .tab {
            padding: 16px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            border-bottom-color: #1890ff;
            color: #1890ff;
        }

        .tab:hover {
            background: #f5f5f5;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
        }

        /* 条码扫描器 */
        .barcode-scanner {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .barcode-scanner input {
            flex: 1;
        }

        .scanner-btn {
            padding: 8px 12px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .scanner-btn:hover {
            background: #40a9ff;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav {
                display: none;
            }

            .sidebar {
                display: none;
            }

            .content-area {
                padding: 16px;
            }

            .search-form {
                flex-direction: column;
                align-items: stretch;
            }

            .form-item {
                width: 100%;
            }

            .form-item input,
            .form-item select {
                width: 100%;
            }

            .stats-cards {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }

        /* PDA适配样式 */
        .pda-form {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
        }

        .pda-form .form-group {
            margin-bottom: 20px;
        }

        .pda-form .form-group label {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
            display: block;
        }

        .pda-form .form-group input,
        .pda-form .form-group select,
        .pda-form .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #d9d9d9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .pda-form .form-group input:focus,
        .pda-form .form-group select:focus,
        .pda-form .form-group textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
        }

        .pda-form .barcode-scanner {
            position: relative;
        }

        .pda-form .barcode-scanner input {
            padding-right: 60px;
        }

        .pda-form .scanner-btn {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            padding: 8px 12px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .pda-action-btn {
            width: 100%;
            padding: 16px;
            border-radius: 8px;
            border: none;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 16px;
        }

        .pda-action-btn.primary {
            background: #1890ff;
            color: white;
        }

        .pda-action-btn.primary:hover {
            background: #40a9ff;
        }

        .pda-action-btn.success {
            background: #52c41a;
            color: white;
        }

        .pda-action-btn.warning {
            background: #fa8c16;
            color: white;
        }

        .info-card {
            background: #f0f2f5;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .info-card .info-title {
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }

        .info-card .info-content {
            color: #8c8c8c;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: 500;
        }

        .status-indicator.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-indicator.warning {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-indicator.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        .hierarchy-tree {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
        }

        .tree-node {
            margin-left: 20px;
            border-left: 1px dashed #d9d9d9;
            padding-left: 20px;
            margin-bottom: 8px;
        }

        .tree-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
            cursor: pointer;
        }

        .tree-item:hover {
            background: #f5f5f5;
        }

        .tree-icon {
            width: 16px;
            height: 16px;
            fill: #1890ff;
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- 登录页面 -->
        <div class="login-page" id="loginPage">
            <div class="login-container">
                <div class="login-left">
                    <div class="logo-section">
                        <div class="logo">
                            <div class="logo-icon">📦</div>
                            WMS-VGL
                        </div>
                        <div class="tagline">智能仓储管理系统</div>
                        <ul class="features">
                            <li>LED视觉引导拣选</li>
                            <li>四级层级管理结构</li>
                            <li>实时库存追踪</li>
                            <li>智能储位推荐</li>
                            <li>多行业适配支持</li>
                        </ul>
                    </div>
                </div>

                <div class="login-right">
                    <form class="login-form" onsubmit="handleLogin(event)">
                        <h1 class="form-title">用户登录</h1>
                        <p class="form-subtitle">请输入您的账号信息</p>

                        <div class="form-group">
                            <label class="form-label">邮箱地址</label>
                            <input 
                                type="email" 
                                class="form-input" 
                                id="email"
                                placeholder="请输入邮箱地址" 
                                required
                                value="<EMAIL>"
                            >
                        </div>

                        <div class="form-group">
                            <label class="form-label">密码</label>
                            <input 
                                type="password" 
                                class="form-input" 
                                id="password"
                                placeholder="请输入密码" 
                                required
                                value="Admin@123456"
                            >
                        </div>

                        <button type="submit" class="login-btn">
                            登录系统
                        </button>

                        <div class="demo-accounts">
                            <div class="demo-title">快速登录演示账号</div>
                            <div class="demo-account">
                                <div>
                                    <strong><EMAIL></strong>
                                    <div style="color: #8c8c8c; font-size: 11px;">超级管理员</div>
                                </div>
                                <button type="button" class="demo-btn" onclick="quickLogin()">
                                    快速登录
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 主应用 -->
        <div class="main-app" id="mainApp">
            <!-- 头部导航 -->
            <header class="header">
                <div class="header-left">
                    <div class="header-logo">WMS-VGL</div>
                    <nav class="nav">
                        <span class="nav-item" onclick="switchModule('dashboard')">首页</span>
                        <span class="nav-item" onclick="switchModule('warehouse')">仓库管理</span>
                        <span class="nav-item" onclick="switchModule('inventory')">库存管理</span>
                        <span class="nav-item" onclick="switchModule('operations')">作业管理</span>
                        <span class="nav-item" onclick="switchModule('hardware')">硬件管理</span>
                        <span class="nav-item" onclick="switchModule('users')">用户管理</span>
                    </nav>
                </div>
                <div class="user-info">
                    <div class="user-avatar">A</div>
                    <span id="currentUser">管理员</span>
                    <button class="logout-btn" onclick="logout()">退出</button>
                </div>
            </header>

            <!-- 主布局 -->
            <div class="main-layout">
                <!-- 侧边栏切换按钮 -->
                <button class="sidebar-toggle" onclick="toggleSidebar()" title="切换侧边栏">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                    </svg>
                </button>

                <!-- 侧边栏 -->
                <div class="sidebar" id="sidebar">
                    <div class="sidebar-menu" id="sidebarMenu">
                        <!-- 动态生成菜单 -->
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="content-area" id="contentArea">
                    <!-- 首页内容 -->
                    <div class="page-content active" id="dashboard">
                        <!-- 系统概览 -->
                        <div class="page-section" id="overview">
                            <div class="breadcrumb">
                                <span class="breadcrumb-item active">首页</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">系统概览</h1>
                                <p class="page-desc">WMS-VGL 智能仓储管理系统总览</p>
                            </div>

                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-number">3</div>
                                    <div class="stat-label">仓库总数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">12</div>
                                    <div class="stat-label">区域总数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">156</div>
                                    <div class="stat-label">货架总数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">2,340</div>
                                    <div class="stat-label">储位总数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">1,234</div>
                                    <div class="stat-label">库存SKU</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">24</div>
                                    <div class="stat-label">ESP32设备</div>
                                </div>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">系统架构概览</h2>
                                </div>
                                <div class="hierarchy-tree" style="padding: 24px;">
                                    <div class="tree-item">
                                        <svg class="tree-icon" viewBox="0 0 24 24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/></svg>
                                        <strong>主仓库 (WH001)</strong>
                                    </div>
                                    <div class="tree-node">
                                        <div class="tree-item">
                                            <svg class="tree-icon" viewBox="0 0 24 24"><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>
                                            A区域 (常温区)
                                        </div>
                                        <div class="tree-node">
                                            <div class="tree-item">
                                                <svg class="tree-icon" viewBox="0 0 24 24"><path d="M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z"/></svg>
                                                货架A01 (80个储位)
                                            </div>
                                            <div class="tree-item">
                                                <svg class="tree-icon" viewBox="0 0 24 24"><path d="M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z"/></svg>
                                                货架A02 (80个储位)
                                            </div>
                                        </div>
                                        <div class="tree-item">
                                            <svg class="tree-icon" viewBox="0 0 24 24"><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>
                                            B区域 (冷藏区)
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <div class="quick-action-card" onclick="switchToPage('warehouse', 'warehouse-list')">
                                    <svg class="quick-action-icon" viewBox="0 0 24 24">
                                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                                    </svg>
                                    <div class="quick-action-title">仓库管理</div>
                                    <div class="quick-action-desc">管理仓库层级结构和配置</div>
                                </div>
                                <div class="quick-action-card" onclick="switchToPage('inventory', 'inventory-list')">
                                    <svg class="quick-action-icon" viewBox="0 0 24 24">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <div class="quick-action-title">库存查询</div>
                                    <div class="quick-action-desc">实时查看库存状态</div>
                                </div>
                                <div class="quick-action-card" onclick="switchToPage('operations', 'inbound')">
                                    <svg class="quick-action-icon" viewBox="0 0 24 24">
                                        <path d="M9 11H7l5-5 5 5h-2v7h-6v-7z"/>
                                    </svg>
                                    <div class="quick-action-title">入库作业</div>
                                    <div class="quick-action-desc">执行入库操作</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 仓库管理页面 -->
                    <div class="page-content" id="warehouse">
                        <!-- 仓库列表 -->
                        <div class="page-section" id="warehouse-list">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">仓库管理</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">仓库管理</h1>
                                <p class="page-desc">管理仓库基础信息，配置全局策略和层级结构</p>
                            </div>

                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-number">3</div>
                                    <div class="stat-label">总仓库数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">12</div>
                                    <div class="stat-label">总区域数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">156</div>
                                    <div class="stat-label">总货架数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">2,340</div>
                                    <div class="stat-label">总储位数</div>
                                </div>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">仓库列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" onclick="showModal('addWarehouseModal')">新增仓库</button>
                                        <button class="btn">导出数据</button>
                                    </div>
                                </div>

                                <div class="search-form">
                                    <div class="form-item">
                                        <label>仓库编码</label>
                                        <input type="text" placeholder="输入仓库编码">
                                    </div>
                                    <div class="form-item">
                                        <label>仓库名称</label>
                                        <input type="text" placeholder="输入仓库名称">
                                    </div>
                                    <div class="form-item">
                                        <label>仓库类型</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="distribution">配送中心</option>
                                            <option value="storage">存储仓库</option>
                                            <option value="production">生产仓库</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label>状态</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="active">启用</option>
                                            <option value="inactive">禁用</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label style="visibility: hidden;">操作</label>
                                        <div class="btn-group">
                                            <button class="btn btn-primary">搜索</button>
                                            <button class="btn">重置</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>仓库编码</th>
                                                <th>仓库名称</th>
                                                <th>仓库类型</th>
                                                <th>地址</th>
                                                <th>联系人</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>WH001</td>
                                                <td>主仓库</td>
                                                <td>配送中心</td>
                                                <td>北京市朝阳区xx路xx号</td>
                                                <td>张三</td>
                                                <td><span class="tag tag-success">启用</span></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text" onclick="switchToPage('warehouse', 'zone-list', 'WH001')">查看区域</button>
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text danger">删除</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>WH002</td>
                                                <td>分拣中心</td>
                                                <td>存储仓库</td>
                                                <td>上海市浦东新区xx路xx号</td>
                                                <td>李四</td>
                                                <td><span class="tag tag-success">启用</span></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text" onclick="switchToPage('warehouse', 'zone-list', 'WH002')">查看区域</button>
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text danger">删除</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>WH003</td>
                                                <td>生产仓库</td>
                                                <td>生产仓库</td>
                                                <td>广州市天河区xx路xx号</td>
                                                <td>王五</td>
                                                <td><span class="tag tag-warning">禁用</span></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text" onclick="switchToPage('warehouse', 'zone-list', 'WH003')">查看区域</button>
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text danger">删除</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="pagination">
                                    <div class="pagination-info">显示 1-3 条，共 3 条数据</div>
                                    <div class="pagination-controls">
                                        <button class="btn">上一页</button>
                                        <button class="btn btn-primary">1</button>
                                        <button class="btn">下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 区域管理 -->
                        <div class="page-section hidden" id="zone-list">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item" onclick="switchToPage('warehouse', 'warehouse-list')">仓库管理</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">区域管理</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">区域管理</h1>
                                <p class="page-desc" id="zonePageDesc">管理仓库区域，配置环境控制参数</p>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">区域列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" onclick="showModal('addZoneModal')">新增区域</button>
                                        <button class="btn">导出数据</button>
                                    </div>
                                </div>

                                <div class="search-form">
                                    <div class="form-item">
                                        <label>区域编码</label>
                                        <input type="text" placeholder="输入区域编码">
                                    </div>
                                    <div class="form-item">
                                        <label>区域名称</label>
                                        <input type="text" placeholder="输入区域名称">
                                    </div>
                                    <div class="form-item">
                                        <label>区域类型</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="ambient">常温</option>
                                            <option value="refrigerated">冷藏</option>
                                            <option value="frozen">冷冻</option>
                                            <option value="hazmat">危险品</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label style="visibility: hidden;">操作</label>
                                        <div class="btn-group">
                                            <button class="btn btn-primary">搜索</button>
                                            <button class="btn">重置</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>区域编码</th>
                                                <th>区域名称</th>
                                                <th>区域类型</th>
                                                <th>温度范围</th>
                                                <th>安全等级</th>
                                                <th>货架数量</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="zoneTableBody">
                                            <!-- 动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 货架管理 -->
                        <div class="page-section hidden" id="shelf-list">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item" onclick="switchToPage('warehouse', 'warehouse-list')">仓库管理</a>
                                <span>/</span>
                                <a href="#" class="breadcrumb-item" onclick="goBackToZones()">区域管理</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">货架管理</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">货架管理</h1>
                                <p class="page-desc" id="shelfPageDesc">管理货架信息，配置安全认证</p>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">货架列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" onclick="showModal('addShelfModal')">新增货架</button>
                                        <button class="btn">导出数据</button>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>货架编码</th>
                                                <th>货架名称</th>
                                                <th>货架类型</th>
                                                <th>尺寸(长×宽×高)</th>
                                                <th>最大承重</th>
                                                <th>储位数量</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="shelfTableBody">
                                            <!-- 动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 储位管理 -->
                        <div class="page-section hidden" id="location-list">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item" onclick="switchToPage('warehouse', 'warehouse-list')">仓库管理</a>
                                <span>/</span>
                                <a href="#" class="breadcrumb-item" onclick="goBackToZones()">区域管理</a>
                                <span>/</span>
                                <a href="#" class="breadcrumb-item" onclick="goBackToShelves()">货架管理</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">储位管理</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">储位管理</h1>
                                <p class="page-desc" id="locationPageDesc">管理储位信息，配置LED控制参数</p>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">储位列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" onclick="showModal('addLocationModal')">新增储位</button>
                                        <button class="btn">批量导入</button>
                                        <button class="btn">导出数据</button>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>储位编码</th>
                                                <th>储位名称</th>
                                                <th>ESP32设备</th>
                                                <th>LED位置</th>
                                                <th>容量</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="locationTableBody">
                                            <!-- 动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 库存管理页面 -->
                    <div class="page-content" id="inventory">
                        <!-- 库存查询 -->
                        <div class="page-section" id="inventory-list">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">库存管理</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">库存管理</h1>
                                <p class="page-desc">实时查看库存状态，执行入库、出库、移库等操作</p>
                            </div>

                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-number">1,234</div>
                                    <div class="stat-label">总库存SKU</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">98,765</div>
                                    <div class="stat-label">库存总数量</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">1,567</div>
                                    <div class="stat-label">占用储位</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">89.5%</div>
                                    <div class="stat-label">储位利用率</div>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <div class="quick-action-card" onclick="switchToPage('operations', 'inbound')">
                                    <svg class="quick-action-icon" viewBox="0 0 24 24">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <div class="quick-action-title">快速入库</div>
                                    <div class="quick-action-desc">扫描物料条码，快速执行入库操作</div>
                                    <button class="btn btn-primary">开始入库</button>
                                </div>
                                <div class="quick-action-card" onclick="switchToPage('operations', 'outbound')">
                                    <svg class="quick-action-icon" viewBox="0 0 24 24">
                                        <path d="M9 11H7l5-5 5 5h-2v7h-6v-7z"/>
                                    </svg>
                                    <div class="quick-action-title">快速出库</div>
                                    <div class="quick-action-desc">扫描储位条码，快速执行出库操作</div>
                                    <button class="btn btn-warning">开始出库</button>
                                </div>
                                <div class="quick-action-card" onclick="switchToPage('operations', 'transfer')">
                                    <svg class="quick-action-icon" viewBox="0 0 24 24">
                                        <path d="M6.99 11L3 15l3.99 4v-3H14v-2H6.99v-3zM21 9l-3.99-4v3H10v2h7.01v3L21 9z"/>
                                    </svg>
                                    <div class="quick-action-title">库内移库</div>
                                    <div class="quick-action-desc">在储位间移动物料，优化库存布局</div>
                                    <button class="btn btn-success">开始移库</button>
                                </div>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">库存列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary">导出库存</button>
                                        <button class="btn btn-success">库存盘点</button>
                                    </div>
                                </div>

                                <div class="search-form">
                                    <div class="form-item">
                                        <label>物料编码</label>
                                        <div class="barcode-scanner">
                                            <input type="text" placeholder="输入或扫描物料编码">
                                            <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                        </div>
                                    </div>
                                    <div class="form-item">
                                        <label>储位编码</label>
                                        <div class="barcode-scanner">
                                            <input type="text" placeholder="输入或扫描储位编码">
                                            <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                        </div>
                                    </div>
                                    <div class="form-item">
                                        <label>库存状态</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="qualified">合格</option>
                                            <option value="pending">待检</option>
                                            <option value="frozen">冻结</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label style="visibility: hidden;">操作</label>
                                        <div class="btn-group">
                                            <button class="btn btn-primary">搜索</button>
                                            <button class="btn">重置</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>物料编码</th>
                                                <th>物料名称</th>
                                                <th>储位编码</th>
                                                <th>批次号</th>
                                                <th>库存数量</th>
                                                <th>库存状态</th>
                                                <th>入库时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>MAT001</td>
                                                <td>电子元器件A</td>
                                                <td>A01-001</td>
                                                <td>B20240120</td>
                                                <td>500 个</td>
                                                <td><span class="tag tag-success">合格</span></td>
                                                <td>2024-01-20 09:30</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text" onclick="lightLocation('A01-001')">点亮储位</button>
                                                        <button class="btn-text success">入库</button>
                                                        <button class="btn-text warning">出库</button>
                                                        <button class="btn-text">移库</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>MAT002</td>
                                                <td>原材料B</td>
                                                <td>A01-002</td>
                                                <td>B20240119</td>
                                                <td>200 kg</td>
                                                <td><span class="tag tag-info">待检</span></td>
                                                <td>2024-01-19 14:15</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text" onclick="lightLocation('A01-002')">点亮储位</button>
                                                        <button class="btn-text success">入库</button>
                                                        <button class="btn-text warning">出库</button>
                                                        <button class="btn-text">移库</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>MAT003</td>
                                                <td>成品C</td>
                                                <td>A01-003</td>
                                                <td>B20240118</td>
                                                <td>150 箱</td>
                                                <td><span class="tag tag-error">冻结</span></td>
                                                <td>2024-01-18 16:45</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text" onclick="lightLocation('A01-003')">点亮储位</button>
                                                        <button class="btn-text success">入库</button>
                                                        <button class="btn-text warning">出库</button>
                                                        <button class="btn-text">移库</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="pagination">
                                    <div class="pagination-info">显示 1-3 条，共 1,234 条数据</div>
                                    <div class="pagination-controls">
                                        <button class="btn">上一页</button>
                                        <button class="btn btn-primary">1</button>
                                        <button class="btn">2</button>
                                        <button class="btn">3</button>
                                        <button class="btn">下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 库存统计 -->
                        <div class="page-section hidden" id="stock-statistics">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">库存统计</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">库存统计</h1>
                                <p class="page-desc">查看库存统计数据和分析报告</p>
                            </div>

                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-number">1,234</div>
                                    <div class="stat-label">总库存SKU</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">98,765</div>
                                    <div class="stat-label">库存总数量</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">¥2,567,890</div>
                                    <div class="stat-label">库存总价值</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">89.5%</div>
                                    <div class="stat-label">储位利用率</div>
                                </div>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">库存分类统计</h2>
                                    <div class="btn-group">
                                        <button class="btn">导出报告</button>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>物料类别</th>
                                                <th>SKU数量</th>
                                                <th>库存数量</th>
                                                <th>库存价值</th>
                                                <th>占用储位</th>
                                                <th>筆诸号数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>电子元器件</td>
                                                <td>456</td>
                                                <td>25,678</td>
                                                <td>¥567,890</td>
                                                <td>234</td>
                                                <td>1,234</td>
                                            </tr>
                                            <tr>
                                                <td>原材料</td>
                                                <td>234</td>
                                                <td>15,432</td>
                                                <td>¥234,567</td>
                                                <td>156</td>
                                                <td>567</td>
                                            </tr>
                                            <tr>
                                                <td>成品</td>
                                                <td>544</td>
                                                <td>57,655</td>
                                                <td>¥1,765,433</td>
                                                <td>1,177</td>
                                                <td>2,456</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 库存预警 -->
                        <div class="page-section hidden" id="stock-alerts">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">库存预警</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">库存预警</h1>
                                <p class="page-desc">查看库存预警信息和处理建议</p>
                            </div>

                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-number">23</div>
                                    <div class="stat-label">低库存预警</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">8</div>
                                    <div class="stat-label">过期预警</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">5</div>
                                    <div class="stat-label">高库存预警</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">36</div>
                                    <div class="stat-label">总预警数</div>
                                </div>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">预警列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary">批量处理</button>
                                        <button class="btn">导出</button>
                                    </div>
                                </div>

                                <div class="search-form">
                                    <div class="form-item">
                                        <label>预警类型</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="low_stock">低库存</option>
                                            <option value="expiry">过期</option>
                                            <option value="high_stock">高库存</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label>预警级别</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="high">高</option>
                                            <option value="medium">中</option>
                                            <option value="low">低</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label style="visibility: hidden;">操作</label>
                                        <div class="btn-group">
                                            <button class="btn btn-primary">搜索</button>
                                            <button class="btn">重置</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>预警类型</th>
                                                <th>级别</th>
                                                <th>物料编码</th>
                                                <th>物料名称</th>
                                                <th>当前库存</th>
                                                <th>阈值</th>
                                                <th>有效期</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="tag tag-warning">低库存</span></td>
                                                <td><span class="tag tag-error">高</span></td>
                                                <td>MAT001</td>
                                                <td>电子元器件A</td>
                                                <td>15</td>
                                                <td>50</td>
                                                <td>2025-01-20</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text success">补货</button>
                                                        <button class="btn-text">忽略</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-error">过期</span></td>
                                                <td><span class="tag tag-error">高</span></td>
                                                <td>MAT002</td>
                                                <td>原材料B</td>
                                                <td>200</td>
                                                <td>-</td>
                                                <td>2024-01-15</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text warning">出库</button>
                                                        <button class="btn-text danger">销毁</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="tag tag-info">高库存</span></td>
                                                <td><span class="tag tag-warning">中</span></td>
                                                <td>MAT003</td>
                                                <td>成品C</td>
                                                <td>5000</td>
                                                <td>1000</td>
                                                <td>2024-07-18</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text warning">出库</button>
                                                        <button class="btn-text">调整</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="pagination">
                                    <div class="pagination-info">显示 1-3 条，共 36 条数据</div>
                                    <div class="pagination-controls">
                                        <button class="btn">上一页</button>
                                        <button class="btn btn-primary">1</button>
                                        <button class="btn">2</button>
                                        <button class="btn">3</button>
                                        <button class="btn">下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 作业管理页面 -->
                    <div class="page-content" id="operations">
                    <!-- 入库作业 -->
                    <div class="page-section" id="inbound">
                        <div class="breadcrumb">
                            <a href="#" class="breadcrumb-item">首页</a>
                            <span>/</span>
                            <span class="breadcrumb-item active">入库作业</span>
                        </div>

                        <div class="page-header">
                            <h1 class="page-title">入库作业</h1>
                            <p class="page-desc">执行物料入库操作，系统提供智能储位推荐</p>
                        </div>

                        <div class="pda-form">
                            <div class="info-card">
                                <div class="info-title">入库流程</div>
                                <div class="info-content">1. 扫描物料码 → 2. 填写数量 → 3. 获取推荐储位 → 4. 确认入库</div>
                            </div>

                            <div class="form-group">
                                <label>物料编码 *</label>
                                <div class="barcode-scanner">
                                    <input type="text" placeholder="扫描物料条码" id="inboundMaterial">
                                    <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>物料名称</label>
                                <input type="text" placeholder="系统自动识别" readonly id="materialName">
                            </div>

                            <div class="form-group">
                                <label>入库数量 *</label>
                                <input type="number" placeholder="输入入库数量" min="1" value="1">
                            </div>

                            <div class="form-group">
                                <label>批次号</label>
                                <input type="text" placeholder="输入批次号（可选）">
                            </div>

                            <div class="form-group">
                                <label>LPN容器码</label>
                                <div class="barcode-scanner">
                                    <input type="text" placeholder="扫描LPN条码（可选）">
                                    <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                </div>
                            </div>

                            <button class="pda-action-btn primary" onclick="recommendLocation()">
                                获取储位推荐
                            </button>

                            <div class="content-card" id="recommendationCard" style="display: none;">
                                <div class="card-header">
                                    <h2 class="card-title">推荐储位</h2>
                                </div>
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>储位编码</th>
                                                <th>区域</th>
                                                <th>匹配度</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recommendationBody">
                                            <!-- 动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- 出库作业 -->
                        <div class="page-section hidden" id="outbound">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">出库作业</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">出库作业</h1>
                                <p class="page-desc">执行物料出库操作，系统自动定位储位并点亮LED</p>
                            </div>

                            <div class="pda-form">
                                <div class="info-card">
                                    <div class="info-title">出库流程</div>
                                    <div class="info-content">1. 扫描储位码 → 2. 确认物料 → 3. 填写数量 → 4. 执行出库</div>
                                </div>

                                <div class="form-group">
                                    <label>储位编码 *</label>
                                    <div class="barcode-scanner">
                                        <input type="text" placeholder="扫描储位条码" id="outboundLocation">
                                        <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>物料编码</label>
                                    <input type="text" placeholder="系统自动识别" readonly id="outboundMaterial">
                                </div>

                                <div class="form-group">
                                    <label>物料名称</label>
                                    <input type="text" placeholder="系统自动识别" readonly>
                                </div>

                                <div class="form-group">
                                    <label>当前库存</label>
                                    <input type="text" placeholder="系统自动识别" readonly>
                                </div>

                                <div class="form-group">
                                    <label>出库数量 *</label>
                                    <input type="number" placeholder="输入出库数量" min="1" value="1">
                                </div>

                                <div class="form-group">
                                    <label>出库原因</label>
                                    <select>
                                        <option value="sale">销售出库</option>
                                        <option value="production">生产领料</option>
                                        <option value="transfer">调拨出库</option>
                                        <option value="return">退货出库</option>
                                    </select>
                                </div>

                                <button class="pda-action-btn warning" onclick="executeOutbound()">
                                    执行出库
                                </button>
                            </div>
                        </div>

                        <!-- 移库作业 -->
                        <div class="page-section hidden" id="transfer">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">移库作业</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">移库作业</h1>
                                <p class="page-desc">在储位间移动物料，系统同时点亮源储位和目标储位</p>
                            </div>

                            <div class="pda-form">
                                <div class="info-card">
                                    <div class="info-title">移库流程</div>
                                    <div class="info-content">1. 扫描源储位 → 2. 扫描目标储位 → 3. 确认信息 → 4. 执行移库</div>
                                </div>

                                <div class="form-group">
                                    <label>源储位编码 *</label>
                                    <div class="barcode-scanner">
                                        <input type="text" placeholder="扫描源储位条码" id="sourceLocation">
                                        <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>目标储位编码 *</label>
                                    <div class="barcode-scanner">
                                        <input type="text" placeholder="扫描目标储位条码" id="targetLocation">
                                        <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>物料编码</label>
                                    <input type="text" placeholder="系统自动识别" readonly>
                                </div>

                                <div class="form-group">
                                    <label>物料名称</label>
                                    <input type="text" placeholder="系统自动识别" readonly>
                                </div>

                                <div class="form-group">
                                    <label>当前库存</label>
                                    <input type="text" placeholder="系统自动识别" readonly>
                                </div>

                                <div class="form-group">
                                    <label>移库数量 *</label>
                                    <input type="number" placeholder="输入移库数量" min="1" value="1">
                                </div>

                                <div class="form-group">
                                    <label>移库原因</label>
                                    <select>
                                        <option value="optimization">储位优化</option>
                                        <option value="maintenance">设备维护</option>
                                        <option value="consolidation">库存整理</option>
                                        <option value="damage">储位损坏</option>
                                    </select>
                                </div>

                                <button class="pda-action-btn success" onclick="executeTransfer()">
                                    执行移库
                                </button>
                            </div>
                        </div>

                        <!-- 拣货作业 -->
                        <div class="page-section hidden" id="picking">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">拣货作业</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">拣货作业</h1>
                                <p class="page-desc">按订单执行拣货任务，LED引导最优路径</p>
                            </div>

                            <div class="pda-form">
                                <div class="info-card">
                                    <div class="info-title">拣货流程</div>
                                    <div class="info-content">1. 扫描拣货单 → 2. 按LED指引拣货 → 3. 扫码确认 → 4. 完成任务</div>
                                </div>

                                <div class="form-group">
                                    <label>拣货单号 *</label>
                                    <div class="barcode-scanner">
                                        <input type="text" placeholder="扫描拣货单条码" id="pickingOrder">
                                        <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                    </div>
                                </div>

                                <div class="info-card">
                                    <div class="info-title">当前任务</div>
                                    <div class="info-content" id="currentTask">请先扫描拣货单</div>
                                </div>

                                <div class="form-group" id="pickingDetails" style="display: none;">
                                    <label>当前拣货物料</label>
                                    <input type="text" placeholder="物料信息" readonly id="currentMaterial">
                                </div>

                                <div class="form-group" id="pickingLocation" style="display: none;">
                                    <label>储位编码 *</label>
                                    <div class="barcode-scanner">
                                        <input type="text" placeholder="扫描储位确认" id="pickLocation">
                                        <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                    </div>
                                </div>

                                <div class="form-group" id="pickingQuantity" style="display: none;">
                                    <label>拣货数量</label>
                                    <input type="number" placeholder="拣货数量" min="1" value="1" id="pickQty">
                                </div>

                                <button class="pda-action-btn primary" onclick="startPicking()" id="startPickingBtn">
                                    开始拣货
                                </button>

                                <button class="pda-action-btn success" onclick="confirmPicking()" id="confirmPickingBtn" style="display: none;">
                                    确认拣货
                                </button>
                            </div>
                        </div>

                        <!-- 盘点作业 -->
                        <div class="page-section hidden" id="counting">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">盘点作业</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">盘点作业</h1>
                                <p class="page-desc">执行库存盘点，系统LED引导盘点位置</p>
                            </div>

                            <div class="pda-form">
                                <div class="info-card">
                                    <div class="info-title">盘点流程</div>
                                    <div class="info-content">1. 扫描盘点任务 → 2. 按LED指引盘点 → 3. 录入实际数量 → 4. 提交结果</div>
                                </div>

                                <div class="form-group">
                                    <label>盘点任务号 *</label>
                                    <div class="barcode-scanner">
                                        <input type="text" placeholder="扫描盘点任务条码" id="countingTask">
                                        <button class="scanner-btn" onclick="scanBarcode(this)">扫码</button>
                                    </div>
                                </div>

                                <div class="info-card" id="taskInfo" style="display: none;">
                                    <div class="info-title">盘点任务信息</div>
                                    <div class="info-content">
                                        <div>任务类型: 区域盘点</div>
                                        <div>盘点区域: A区域</div>
                                        <div>预计储位: 50个</div>
                                        <div>已完成: <span id="completedCount">0</span> / <span id="totalCount">50</span></div>
                                    </div>
                                </div>

                                <div class="form-group" id="currentLocationGroup" style="display: none;">
                                    <label>当前储位</label>
                                    <input type="text" placeholder="储位编码" readonly id="currentCountLocation">
                                </div>

                                <div class="form-group" id="expectedMaterialGroup" style="display: none;">
                                    <label>预期物料</label>
                                    <input type="text" placeholder="预期物料信息" readonly id="expectedMaterial">
                                </div>

                                <div class="form-group" id="systemQtyGroup" style="display: none;">
                                    <label>系统数量</label>
                                    <input type="text" placeholder="系统库存数量" readonly id="systemQty">
                                </div>

                                <div class="form-group" id="actualQtyGroup" style="display: none;">
                                    <label>实际数量 *</label>
                                    <input type="number" placeholder="输入实际盘点数量" min="0" id="actualQty">
                                </div>

                                <div class="form-group" id="countingNoteGroup" style="display: none;">
                                    <label>备注</label>
                                    <textarea placeholder="差异原因或备注信息（可选）" rows="3" id="countingNote"></textarea>
                                </div>

                                <button class="pda-action-btn primary" onclick="startCounting()" id="startCountingBtn">
                                    开始盘点
                                </button>

                                <button class="pda-action-btn success" onclick="submitCount()" id="submitCountBtn" style="display: none;">
                                    提交盘点
                                </button>

                                <button class="pda-action-btn" onclick="nextLocation()" id="nextLocationBtn" style="display: none;">
                                    下一储位
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 硬件管理页面 -->
                    <div class="page-content" id="hardware">
                        <!-- ESP32设备管理 -->
                        <div class="page-section" id="esp32-list">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">ESP32设备管理</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">ESP32设备管理</h1>
                                <p class="page-desc">管理ESP32控制器设备，配置LED灯带控制参数</p>
                            </div>

                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-number">24</div>
                                    <div class="stat-label">设备总数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">22</div>
                                    <div class="stat-label">在线设备</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">2</div>
                                    <div class="stat-label">离线设备</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">98.5%</div>
                                    <div class="stat-label">健康率</div>
                                </div>
                            </div>

                            <!-- LED控制测试面板 -->
                            <div class="led-control-panel">
                                <h2 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">LED灯带控制测试</h2>
                                <div class="form-grid" style="margin-bottom: 24px;">
                                    <div class="form-group">
                                        <label>选择设备</label>
                                        <select id="deviceSelect">
                                            <option value="">请选择设备</option>
                                            <option value="*************">ESP32-001 (*************)</option>
                                            <option value="192.168.1.101">ESP32-002 (192.168.1.101)</option>
                                            <option value="192.168.1.102">ESP32-003 (192.168.1.102)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>通道号</label>
                                        <select id="channelSelect">
                                            <option value="0">通道 0</option>
                                            <option value="1">通道 1</option>
                                            <option value="2">通道 2</option>
                                            <option value="3">通道 3</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>起始位置</label>
                                        <input type="number" id="startPos" value="0" min="0" max="255">
                                    </div>
                                    <div class="form-group">
                                        <label>结束位置</label>
                                        <input type="number" id="endPos" value="10" min="0" max="255">
                                    </div>
                                    <div class="form-group">
                                        <label>亮度 (0-31)</label>
                                        <input type="range" id="brightness" min="0" max="31" value="20">
                                        <span id="brightnessValue">20</span>
                                    </div>
                                    <div class="form-group">
                                        <label>持续时间 (秒)</label>
                                        <input type="number" id="duration" value="30" min="0" max="300">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>选择颜色</label>
                                    <div class="color-picker">
                                        <div class="color-option selected" style="background-color: #ff4d4f;" data-color="red"></div>
                                        <div class="color-option" style="background-color: #52c41a;" data-color="green"></div>
                                        <div class="color-option" style="background-color: #1890ff;" data-color="blue"></div>
                                        <div class="color-option" style="background-color: #faad14;" data-color="yellow"></div>
                                        <div class="color-option" style="background-color: #ffffff; border-color: #d9d9d9;" data-color="white"></div>
                                        <div class="color-option" style="background-color: #000000;" data-color="black"></div>
                                        <div class="color-option" style="background-color: #13c2c2;" data-color="cyan"></div>
                                        <div class="color-option" style="background-color: #eb2f96;" data-color="magenta"></div>
                                    </div>
                                </div>

                                <div class="led-preview">
                                    <div class="led-strip" id="ledStrip">
                                        <!-- LED点将通过JavaScript动态生成 -->
                                    </div>
                                </div>

                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="sendLEDCommand()">发送控制指令</button>
                                    <button class="btn btn-success" onclick="testAllLEDs()">测试所有LED</button>
                                    <button class="btn btn-warning" onclick="turnOffAllLEDs()">关闭所有LED</button>
                                </div>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">ESP32设备列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary" onclick="showModal('addDeviceModal')">新增设备</button>
                                        <button class="btn btn-success">扫描设备</button>
                                        <button class="btn">刷新状态</button>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>设备编码</th>
                                                <th>设备名称</th>
                                                <th>IP地址</th>
                                                <th>端口</th>
                                                <th>在线状态</th>
                                                <th>最后心跳</th>
                                                <th>绑定储位</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>ESP32-001</td>
                                                <td>1号货架控制器</td>
                                                <td>*************</td>
                                                <td>80</td>
                                                <td><span class="tag tag-success">在线</span></td>
                                                <td>2024-01-20 14:30:25</td>
                                                <td>A01-001~A01-020</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text success" onclick="testConnection('*************')">测试连接</button>
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text">LED测试</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>ESP32-002</td>
                                                <td>2号货架控制器</td>
                                                <td>192.168.1.101</td>
                                                <td>80</td>
                                                <td><span class="tag tag-success">在线</span></td>
                                                <td>2024-01-20 14:30:12</td>
                                                <td>A01-021~A01-040</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text success" onclick="testConnection('192.168.1.101')">测试连接</button>
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text">LED测试</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>ESP32-003</td>
                                                <td>3号货架控制器</td>
                                                <td>192.168.1.102</td>
                                                <td>80</td>
                                                <td><span class="tag tag-error">离线</span></td>
                                                <td>2024-01-20 13:45:30</td>
                                                <td>A01-041~A01-060</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text success" onclick="testConnection('192.168.1.102')">测试连接</button>
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text">LED测试</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 设备监控 -->
                        <div class="page-section hidden" id="device-monitor">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">设备监控</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">设备监控</h1>
                                <p class="page-desc">实时监控ESP32设备状态和性能指标</p>
                            </div>

                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-number">24</div>
                                    <div class="stat-label">设备总数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">22</div>
                                    <div class="stat-label">在线设备</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">12ms</div>
                                    <div class="stat-label">平均响应时间</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">98.5%</div>
                                    <div class="stat-label">健康率</div>
                                </div>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">设备状态监控</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary">刷新状态</button>
                                        <button class="btn">导出报告</button>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>设备编码</th>
                                                <th>IP地址</th>
                                                <th>状态</th>
                                                <th>CPU使用率</th>
                                                <th>内存使用率</th>
                                                <th>响应时间</th>
                                                <th>最后心跳</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>ESP32-001</td>
                                                <td>*************</td>
                                                <td><span class="tag tag-success">在线</span></td>
                                                <td>15%</td>
                                                <td>67%</td>
                                                <td>12ms</td>
                                                <td>2024-01-20 14:30:25</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">详情</button>
                                                        <button class="btn-text">重启</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>ESP32-002</td>
                                                <td>192.168.1.101</td>
                                                <td><span class="tag tag-success">在线</span></td>
                                                <td>8%</td>
                                                <td>54%</td>
                                                <td>8ms</td>
                                                <td>2024-01-20 14:30:12</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">详情</button>
                                                        <button class="btn-text">重启</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>ESP32-003</td>
                                                <td>192.168.1.102</td>
                                                <td><span class="tag tag-error">离线</span></td>
                                                <td>-</td>
                                                <td>-</td>
                                                <td>超时</td>
                                                <td>2024-01-20 13:45:30</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">详情</button>
                                                        <button class="btn-text danger">故障排查</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- LED控制 -->
                        <div class="page-section hidden" id="led-control">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">LED控制</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">LED控制中心</h1>
                                <p class="page-desc">集中控制所有ESP32设备的LED灯带</p>
                            </div>

                            <!-- 复用LED控制面板内容 -->
                            <div class="led-control-panel">
                                <h2 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">LED灯带控制测试</h2>
                                <div class="form-grid" style="margin-bottom: 24px;">
                                    <div class="form-group">
                                        <label>选择设备</label>
                                        <select id="deviceSelect2">
                                            <option value="">请选择设备</option>
                                            <option value="*************">ESP32-001 (*************)</option>
                                            <option value="192.168.1.101">ESP32-002 (192.168.1.101)</option>
                                            <option value="192.168.1.102">ESP32-003 (192.168.1.102)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>通道号</label>
                                        <select id="channelSelect2">
                                            <option value="0">通道 0</option>
                                            <option value="1">通道 1</option>
                                            <option value="2">通道 2</option>
                                            <option value="3">通道 3</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>起始位置</label>
                                        <input type="number" id="startPos2" value="0" min="0" max="255">
                                    </div>
                                    <div class="form-group">
                                        <label>结束位置</label>
                                        <input type="number" id="endPos2" value="10" min="0" max="255">
                                    </div>
                                    <div class="form-group">
                                        <label>亮度 (0-31)</label>
                                        <input type="range" id="brightness2" min="0" max="31" value="20">
                                        <span id="brightnessValue2">20</span>
                                    </div>
                                    <div class="form-group">
                                        <label>持续时间 (秒)</label>
                                        <input type="number" id="duration2" value="30" min="0" max="300">
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>选择颜色</label>
                                    <div class="color-picker">
                                        <div class="color-option selected" style="background-color: #ff4d4f;" data-color="red"></div>
                                        <div class="color-option" style="background-color: #52c41a;" data-color="green"></div>
                                        <div class="color-option" style="background-color: #1890ff;" data-color="blue"></div>
                                        <div class="color-option" style="background-color: #faad14;" data-color="yellow"></div>
                                        <div class="color-option" style="background-color: #ffffff; border-color: #d9d9d9;" data-color="white"></div>
                                        <div class="color-option" style="background-color: #000000;" data-color="black"></div>
                                        <div class="color-option" style="background-color: #13c2c2;" data-color="cyan"></div>
                                        <div class="color-option" style="background-color: #eb2f96;" data-color="magenta"></div>
                                    </div>
                                </div>

                                <div class="led-preview">
                                    <div class="led-strip" id="ledStrip2">
                                        <!-- LED点将通过JavaScript动态生成 -->
                                    </div>
                                </div>

                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="sendLEDCommand()">发送控制指令</button>
                                    <button class="btn btn-success" onclick="testAllLEDs()">测试所有LED</button>
                                    <button class="btn btn-warning" onclick="turnOffAllLEDs()">关闭所有LED</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户管理页面 -->
                    <div class="page-content" id="users">
                        <div class="page-section" id="user-list">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">用户管理</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">用户管理</h1>
                                <p class="page-desc">管理系统用户账号、角色权限和访问控制</p>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">用户列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary">新增用户</button>
                                        <button class="btn">导出用户</button>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>用户名</th>
                                                <th>邮箱</th>
                                                <th>角色</th>
                                                <th>部门</th>
                                                <th>状态</th>
                                                <th>最后登录</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>张三</td>
                                                <td><EMAIL></td>
                                                <td><span class="tag tag-error">超级管理员</span></td>
                                                <td>IT部门</td>
                                                <td><span class="tag tag-success">活跃</span></td>
                                                <td>2024-01-20 15:30</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text">重置密码</button>
                                                        <button class="btn-text danger">禁用</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>李四</td>
                                                <td><EMAIL></td>
                                                <td><span class="tag tag-warning">仓库经理</span></td>
                                                <td>仓储部门</td>
                                                <td><span class="tag tag-success">活跃</span></td>
                                                <td>2024-01-20 14:15</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text">重置密码</button>
                                                        <button class="btn-text danger">禁用</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>王五</td>
                                                <td><EMAIL></td>
                                                <td><span class="tag tag-info">操作员</span></td>
                                                <td>仓储部门</td>
                                                <td><span class="tag tag-success">活跃</span></td>
                                                <td>2024-01-20 13:45</td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">编辑</button>
                                                        <button class="btn-text">重置密码</button>
                                                        <button class="btn-text danger">禁用</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 角色权限管理 -->
                        <div class="page-section hidden" id="role-management">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">角色权限</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">角色权限管理</h1>
                                <p class="page-desc">管理系统角色和权限分配</p>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">角色列表</h2>
                                    <div class="btn-group">
                                        <button class="btn btn-primary">新增角色</button>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>角色名称</th>
                                                <th>权限级别</th>
                                                <th>描述</th>
                                                <th>用户数量</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>超级管理员</td>
                                                <td><span class="tag tag-error">最高权限</span></td>
                                                <td>系统最高权限，可访问所有功能</td>
                                                <td>1</td>
                                                <td><span class="tag tag-success">启用</span></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">查看权限</button>
                                                        <button class="btn-text">编辑</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>仓库经理</td>
                                                <td><span class="tag tag-warning">管理权限</span></td>
                                                <td>仓库操作管理，设备管理权限</td>
                                                <td>3</td>
                                                <td><span class="tag tag-success">启用</span></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">查看权限</button>
                                                        <button class="btn-text">编辑</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>操作员</td>
                                                <td><span class="tag tag-info">操作权限</span></td>
                                                <td>基础仓库作业操作权限</td>
                                                <td>8</td>
                                                <td><span class="tag tag-success">启用</span></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">查看权限</button>
                                                        <button class="btn-text">编辑</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>只读用户</td>
                                                <td><span class="tag tag-default">只读权限</span></td>
                                                <td>仅可查看数据，无操作权限</td>
                                                <td>12</td>
                                                <td><span class="tag tag-success">启用</span></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-text">查看权限</button>
                                                        <button class="btn-text">编辑</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 操作日志 -->
                        <div class="page-section hidden" id="user-logs">
                            <div class="breadcrumb">
                                <a href="#" class="breadcrumb-item">首页</a>
                                <span>/</span>
                                <span class="breadcrumb-item active">操作日志</span>
                            </div>

                            <div class="page-header">
                                <h1 class="page-title">操作日志</h1>
                                <p class="page-desc">查看系统操作记录和审计日志</p>
                            </div>

                            <div class="content-card">
                                <div class="card-header">
                                    <h2 class="card-title">操作日志</h2>
                                    <div class="btn-group">
                                        <button class="btn">导出日志</button>
                                    </div>
                                </div>

                                <div class="search-form">
                                    <div class="form-item">
                                        <label>用户名</label>
                                        <input type="text" placeholder="输入用户名">
                                    </div>
                                    <div class="form-item">
                                        <label>操作类型</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="login">登录</option>
                                            <option value="logout">登出</option>
                                            <option value="inbound">入库</option>
                                            <option value="outbound">出库</option>
                                            <option value="transfer">移库</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label>时间范围</label>
                                        <input type="date">
                                    </div>
                                    <div class="form-item">
                                        <label style="visibility: hidden;">操作</label>
                                        <div class="btn-group">
                                            <button class="btn btn-primary">搜索</button>
                                            <button class="btn">重置</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>用户</th>
                                                <th>操作类型</th>
                                                <th>操作描述</th>
                                                <th>IP地址</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>2024-01-20 15:30:25</td>
                                                <td>张三</td>
                                                <td>入库</td>
                                                <td>物料MAT001入库至储位A01-001</td>
                                                <td>*************</td>
                                                <td><span class="tag tag-success">成功</span></td>
                                            </tr>
                                            <tr>
                                                <td>2024-01-20 15:28:15</td>
                                                <td>李四</td>
                                                <td>出库</td>
                                                <td>物料MAT002从储位A01-002出库</td>
                                                <td>192.168.1.101</td>
                                                <td><span class="tag tag-success">成功</span></td>
                                            </tr>
                                            <tr>
                                                <td>2024-01-20 15:25:08</td>
                                                <td>王五</td>
                                                <td>登录</td>
                                                <td>用户登录系统</td>
                                                <td>192.168.1.102</td>
                                                <td><span class="tag tag-success">成功</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="pagination">
                                    <div class="pagination-info">显示 1-3 条，共 156 条数据</div>
                                    <div class="pagination-controls">
                                        <button class="btn">上一页</button>
                                        <button class="btn btn-primary">1</button>
                                        <button class="btn">2</button>
                                        <button class="btn">3</button>
                                        <button class="btn">下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通用模态框 -->
        <div class="modal-overlay hidden" id="modalOverlay">
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title" id="modalTitle">标题</h3>
                    <button class="btn-text" onclick="hideModal()">×</button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 动态内容 -->
                </div>
                <div class="modal-footer">
                    <button class="btn" onclick="hideModal()">取消</button>
                    <button class="btn btn-primary" id="modalConfirmBtn">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局数据
        let currentWarehouse = null;
        let currentZone = null;
        let currentShelf = null;

        // 侧边栏切换
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const contentArea = document.getElementById('contentArea');
            sidebar.classList.toggle('collapsed');
        }

        // 模拟数据
        const mockData = {
            warehouses: [
                { id: 'WH001', code: 'WH001', name: '主仓库', type: '配送中心', address: '北京市朝阳区xx路xx号', contact: '张三', status: 'active' },
                { id: 'WH002', code: 'WH002', name: '分拣中心', type: '存储仓库', address: '上海市浦东新区xx路xx号', contact: '李四', status: 'active' },
                { id: 'WH003', code: 'WH003', name: '生产仓库', type: '生产仓库', address: '广州市天河区xx路xx号', contact: '王五', status: 'inactive' }
            ],
            zones: {
                WH001: [
                    { id: 'Z001', code: 'A', name: 'A区域', type: 'ambient', tempRange: '15-25°C', securityLevel: '标准', shelfCount: 6 },
                    { id: 'Z002', code: 'B', name: 'B区域', type: 'refrigerated', tempRange: '2-8°C', securityLevel: '中等', shelfCount: 4 },
                    { id: 'Z003', code: 'C', name: 'C区域', type: 'frozen', tempRange: '-18°C', securityLevel: '高', shelfCount: 2 }
                ],
                WH002: [
                    { id: 'Z004', code: 'A', name: 'A区域', type: 'ambient', tempRange: '15-25°C', securityLevel: '标准', shelfCount: 8 },
                    { id: 'Z005', code: 'B', name: 'B区域', type: 'ambient', tempRange: '15-25°C', securityLevel: '标准', shelfCount: 6 }
                ],
                WH003: [
                    { id: 'Z006', code: 'A', name: 'A区域', type: 'ambient', tempRange: '15-25°C', securityLevel: '标准', shelfCount: 4 }
                ]
            },
            shelves: {
                Z001: [
                    { id: 'S001', code: 'A01', name: '货架A01', type: 'standard', dimensions: '2.0×1.2×2.5m', maxWeight: '1000kg', locationCount: 80 },
                    { id: 'S002', code: 'A02', name: '货架A02', type: 'standard', dimensions: '2.0×1.2×2.5m', maxWeight: '1000kg', locationCount: 80 }
                ],
                Z002: [
                    { id: 'S003', code: 'B01', name: '货架B01', type: 'refrigerated', dimensions: '2.0×1.2×2.5m', maxWeight: '800kg', locationCount: 60 },
                    { id: 'S004', code: 'B02', name: '货架B02', type: 'refrigerated', dimensions: '2.0×1.2×2.5m', maxWeight: '800kg', locationCount: 60 }
                ]
            },
            locations: {
                S001: [
                    { id: 'L001', code: 'A01-001', name: '储位A01-001', device: 'ESP32-001', ledPos: '0-5', capacity: '100L', status: 'available' },
                    { id: 'L002', code: 'A01-002', name: '储位A01-002', device: 'ESP32-001', ledPos: '6-11', capacity: '100L', status: 'occupied' }
                ]
            }
        };

        // 登录处理
        function handleLogin(event) {
            event.preventDefault();
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('mainApp').style.display = 'flex';
            // 初始化首页
            switchModule('dashboard');
        }

        function quickLogin() {
            handleLogin(new Event('submit'));
        }

        // 退出登录
        function logout() {
            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('loginPage').style.display = 'flex';
        }

        // 模块切换
        function switchModule(module) {
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            if (event && event.target) {
                event.target.classList.add('active');
            }

            // 隐藏所有页面内容
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            const targetModule = document.getElementById(module);
            if (targetModule) {
                targetModule.classList.add('active');
            }

            // 生成侧边栏菜单
            generateSidebarMenu(module);

            // 模块特定初始化，显示默认页面
            if (module === 'dashboard') {
                showPageSection('dashboard', 'overview');
            } else if (module === 'warehouse') {
                showPageSection('warehouse', 'warehouse-list');
            } else if (module === 'inventory') {
                showPageSection('inventory', 'inventory-list');
            } else if (module === 'operations') {
                showPageSection('operations', 'inbound');
            } else if (module === 'hardware') {
                showPageSection('hardware', 'esp32-list');
                // 延迟初始化LED控制，等待DOM更新
                setTimeout(() => {
                    initLEDControl();
                }, 100);
            } else if (module === 'users') {
                showPageSection('users', 'user-list');
            }
        }

        // 生成侧边栏菜单
        function generateSidebarMenu(module) {
            const menuContainer = document.getElementById('sidebarMenu');
            if (!menuContainer) return;
            
            menuContainer.innerHTML = '';

            const menus = {
                dashboard: [
                    { id: 'overview', name: '系统概览', icon: '📊' }
                ],
                warehouse: [
                    { id: 'warehouse-list', name: '仓库列表', icon: '🏢' },
                    { id: 'zone-list', name: '区域管理', icon: '🏗️' },
                    { id: 'shelf-list', name: '货架管理', icon: '📚' },
                    { id: 'location-list', name: '储位管理', icon: '📦' }
                ],
                inventory: [
                    { id: 'inventory-list', name: '库存查询', icon: '📋' },
                    { id: 'stock-statistics', name: '库存统计', icon: '📊' },
                    { id: 'stock-alerts', name: '库存预警', icon: '⚠️' }
                ],
                operations: [
                    { id: 'inbound', name: '入库作业', icon: '📥' },
                    { id: 'outbound', name: '出库作业', icon: '📤' },
                    { id: 'transfer', name: '移库作业', icon: '🔄' },
                    { id: 'picking', name: '拣货作业', icon: '🛒' },
                    { id: 'counting', name: '盘点作业', icon: '🔍' }
                ],
                hardware: [
                    { id: 'esp32-list', name: 'ESP32设备', icon: '🔌' },
                    { id: 'device-monitor', name: '设备监控', icon: '📺' },
                    { id: 'led-control', name: 'LED控制', icon: '💡' }
                ],
                users: [
                    { id: 'user-list', name: '用户管理', icon: '👥' },
                    { id: 'role-management', name: '角色权限', icon: '🔐' },
                    { id: 'user-logs', name: '操作日志', icon: '📝' }
                ]
            };

            const moduleMenus = menus[module] || [];
            
            moduleMenus.forEach((menu, index) => {
                const menuItem = document.createElement('div');
                menuItem.className = `menu-item ${index === 0 ? 'active' : ''}`;
                menuItem.innerHTML = `
                    <span class="menu-icon">${menu.icon}</span>
                    ${menu.name}
                `;
                menuItem.onclick = (e) => {
                    e.preventDefault();
                    
                    // 更新菜单项状态
                    document.querySelectorAll('.menu-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    menuItem.classList.add('active');
                    
                    // 显示对应页面
                    showPageSection(module, menu.id);
                };
                menuContainer.appendChild(menuItem);
            });
        }

        // 显示页面章节
        function showPageSection(module, sectionId) {
            // 隐藏当前模块的所有章节
            const currentModule = document.getElementById(module);
            if (currentModule) {
                currentModule.querySelectorAll('.page-section').forEach(section => {
                    section.classList.add('hidden');
                });

                // 显示目标章节
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    targetSection.classList.remove('hidden');
                } else {
                    // 如果页面不存在，显示开发中提示
                    showNotFoundPage(currentModule, sectionId);
                }
            }
        }

        // 显示页面未找到提示
        function showNotFoundPage(moduleElement, sectionId) {
            // 创建一个临时的未找到页面
            let notFoundSection = document.getElementById('temp-not-found');
            if (!notFoundSection) {
                notFoundSection = document.createElement('div');
                notFoundSection.id = 'temp-not-found';
                notFoundSection.className = 'page-section';
                notFoundSection.innerHTML = `
                    <div class="page-header">
                        <h1 class="page-title">页面开发中</h1>
                        <p class="page-desc">该功能正在开发中，敬请期待...</p>
                    </div>
                    <div class="content-card">
                        <div style="padding: 60px; text-align: center; color: #8c8c8c;">
                            <div style="font-size: 48px; margin-bottom: 16px;">🚧</div>
                            <h3>功能开发中</h3>
                            <p>页面 "${sectionId}" 正在开发中，请稍后再试。</p>
                            <button class="btn btn-primary" onclick="switchModule('dashboard')" style="margin-top: 16px;">返回首页</button>
                        </div>
                    </div>
                `;
                moduleElement.appendChild(notFoundSection);
            } else {
                notFoundSection.querySelector('p').textContent = `页面 "${sectionId}" 正在开发中，请稍后再试。`;
            }
            notFoundSection.classList.remove('hidden');
        }

        // 页面切换函数
        function switchToPage(module, page, param) {
            switchModule(module);
            showPageSection(module, page);
            
            if (page === 'zone-list' && param) {
                currentWarehouse = param;
                loadZoneData(param);
            } else if (page === 'shelf-list' && param) {
                currentZone = param;
                loadShelfData(param);
            } else if (page === 'location-list' && param) {
                currentShelf = param;
                loadLocationData(param);
            }
        }

        // 加载区域数据
        function loadZoneData(warehouseId) {
            const warehouse = mockData.warehouses.find(w => w.id === warehouseId);
            document.getElementById('zonePageDesc').textContent = `管理 ${warehouse.name} 的区域信息`;
            
            const zones = mockData.zones[warehouseId] || [];
            const tbody = document.getElementById('zoneTableBody');
            tbody.innerHTML = zones.map(zone => `
                <tr>
                    <td>${zone.code}</td>
                    <td>${zone.name}</td>
                    <td>${zone.type}</td>
                    <td>${zone.tempRange}</td>
                    <td>${zone.securityLevel}</td>
                    <td>${zone.shelfCount}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-text" onclick="switchToPage('warehouse', 'shelf-list', '${zone.id}')">查看货架</button>
                            <button class="btn-text">编辑</button>
                            <button class="btn-text danger">删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 加载货架数据
        function loadShelfData(zoneId) {
            const zone = Object.values(mockData.zones).flat().find(z => z.id === zoneId);
            document.getElementById('shelfPageDesc').textContent = `管理 ${zone.name} 的货架信息`;
            
            const shelves = mockData.shelves[zoneId] || [];
            const tbody = document.getElementById('shelfTableBody');
            tbody.innerHTML = shelves.map(shelf => `
                <tr>
                    <td>${shelf.code}</td>
                    <td>${shelf.name}</td>
                    <td>${shelf.type}</td>
                    <td>${shelf.dimensions}</td>
                    <td>${shelf.maxWeight}</td>
                    <td>${shelf.locationCount}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-text" onclick="switchToPage('warehouse', 'location-list', '${shelf.id}')">查看储位</button>
                            <button class="btn-text">编辑</button>
                            <button class="btn-text danger">删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 加载储位数据
        function loadLocationData(shelfId) {
            const shelf = Object.values(mockData.shelves).flat().find(s => s.id === shelfId);
            document.getElementById('locationPageDesc').textContent = `管理 ${shelf.name} 的储位信息`;
            
            const locations = mockData.locations[shelfId] || [];
            const tbody = document.getElementById('locationTableBody');
            tbody.innerHTML = locations.map(location => `
                <tr>
                    <td>${location.code}</td>
                    <td>${location.name}</td>
                    <td>${location.device}</td>
                    <td>${location.ledPos}</td>
                    <td>${location.capacity}</td>
                    <td><span class="tag tag-${location.status === 'available' ? 'success' : 'warning'}">${location.status === 'available' ? '可用' : '占用'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-text" onclick="lightLocation('${location.code}')">点亮储位</button>
                            <button class="btn-text">编辑</button>
                            <button class="btn-text danger">删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 返回函数
        function goBackToZones() {
            if (currentWarehouse) {
                switchToPage('warehouse', 'zone-list', currentWarehouse);
            }
        }

        function goBackToShelves() {
            if (currentZone) {
                switchToPage('warehouse', 'shelf-list', currentZone);
            }
        }

        // 模态框控制
        function showModal(modalId) {
            document.getElementById('modalOverlay').classList.remove('hidden');
            // 这里可以根据modalId设置不同的模态框内容
        }

        function hideModal() {
            document.getElementById('modalOverlay').classList.add('hidden');
        }

        // 条码扫描模拟
        function scanBarcode(button) {
            const input = button.previousElementSibling;
            const codes = ['MAT001', 'MAT002', 'MAT003', 'A01-001', 'A01-002', 'A01-003', 'LPN001234'];
            const randomCode = codes[Math.floor(Math.random() * codes.length)];
            input.value = randomCode;
            
            // 模拟物料识别
            if (randomCode.startsWith('MAT')) {
                const materialName = {
                    'MAT001': '电子元器件A',
                    'MAT002': '原材料B', 
                    'MAT003': '成品C'
                };
                const nameInput = document.getElementById('materialName');
                if (nameInput) {
                    nameInput.value = materialName[randomCode] || '';
                }
            }
            
            alert(`扫描到条码: ${randomCode}`);
        }

        // 储位点亮
        function lightLocation(locationCode) {
            alert(`正在点亮储位: ${locationCode}`);
            // 这里可以添加实际的LED控制逻辑
        }

        // 作业操作函数
        function recommendLocation() {
            const materialCode = document.getElementById('inboundMaterial').value;
            if (!materialCode) {
                alert('请先输入物料编码');
                return;
            }
            
            const recommendationCard = document.getElementById('recommendationCard');
            recommendationCard.style.display = 'block';
            
            const tbody = document.getElementById('recommendationBody');
            tbody.innerHTML = `
                <tr>
                    <td>A01-005</td>
                    <td>A区域</td>
                    <td>95%</td>
                    <td><button class="btn btn-primary" onclick="selectLocation('A01-005')">选择</button></td>
                </tr>
                <tr>
                    <td>A01-008</td>
                    <td>A区域</td>
                    <td>88%</td>
                    <td><button class="btn btn-primary" onclick="selectLocation('A01-008')">选择</button></td>
                </tr>
                <tr>
                    <td>A01-012</td>
                    <td>A区域</td>
                    <td>82%</td>
                    <td><button class="btn btn-primary" onclick="selectLocation('A01-012')">选择</button></td>
                </tr>
            `;
        }

        function selectLocation(locationCode) {
            alert(`已选择储位: ${locationCode}，正在点亮LED...`);
            // 这里添加实际的入库逻辑
        }

        function executeOutbound() {
            alert('正在执行出库操作...');
        }

        function executeTransfer() {
            alert('正在执行移库操作...');
        }

        // 拣货作业函数
        function startPicking() {
            const pickingOrder = document.getElementById('pickingOrder').value;
            if (!pickingOrder) {
                alert('请先扫描拣货单');
                return;
            }
            
            // 模拟开始拣货
            document.getElementById('currentTask').textContent = '任务已启动，请按LED指引拣货';
            document.getElementById('pickingDetails').style.display = 'block';
            document.getElementById('pickingLocation').style.display = 'block';
            document.getElementById('pickingQuantity').style.display = 'block';
            document.getElementById('currentMaterial').value = 'MAT001 - 电子元器件A';
            document.getElementById('pickLocation').value = 'A01-001';
            document.getElementById('startPickingBtn').style.display = 'none';
            document.getElementById('confirmPickingBtn').style.display = 'block';
            
            alert('正在点亮储位A01-001，请前往拣货');
        }

        function confirmPicking() {
            const location = document.getElementById('pickLocation').value;
            const qty = document.getElementById('pickQty').value;
            
            if (!location || !qty) {
                alert('请确认储位和数量');
                return;
            }
            
            alert(`拣货确认：${location}，数量：${qty}`);
            // 重置表单或继续下一个拣货任务
            document.getElementById('currentTask').textContent = '拣货任务已完成';
        }

        // 盘点作业函数
        function startCounting() {
            const countingTask = document.getElementById('countingTask').value;
            if (!countingTask) {
                alert('请先扫描盘点任务');
                return;
            }
            
            // 模拟开始盘点
            document.getElementById('taskInfo').style.display = 'block';
            document.getElementById('currentLocationGroup').style.display = 'block';
            document.getElementById('expectedMaterialGroup').style.display = 'block';
            document.getElementById('systemQtyGroup').style.display = 'block';
            document.getElementById('actualQtyGroup').style.display = 'block';
            document.getElementById('countingNoteGroup').style.display = 'block';
            
            document.getElementById('currentCountLocation').value = 'A01-001';
            document.getElementById('expectedMaterial').value = 'MAT001 - 电子元器件A';
            document.getElementById('systemQty').value = '500';
            
            document.getElementById('startCountingBtn').style.display = 'none';
            document.getElementById('submitCountBtn').style.display = 'block';
            document.getElementById('nextLocationBtn').style.display = 'block';
            
            alert('正在点亮储位A01-001，请开始盘点');
        }

        function submitCount() {
            const actualQty = document.getElementById('actualQty').value;
            const systemQty = document.getElementById('systemQty').value;
            
            if (actualQty === '') {
                alert('请输入实际盘点数量');
                return;
            }
            
            const diff = parseInt(actualQty) - parseInt(systemQty);
            if (diff !== 0) {
                alert(`发现差异：${diff > 0 ? '+' : ''}${diff}`);
            } else {
                alert('盘点无差异');
            }
            
            // 更新完成计数
            const completed = document.getElementById('completedCount');
            completed.textContent = parseInt(completed.textContent) + 1;
        }

        function nextLocation() {
            // 模拟下一储位
            const locations = ['A01-002', 'A01-003', 'A01-004'];
            const currentCompleted = parseInt(document.getElementById('completedCount').textContent);
            
            if (currentCompleted < locations.length) {
                document.getElementById('currentCountLocation').value = locations[currentCompleted];
                document.getElementById('actualQty').value = '';
                document.getElementById('countingNote').value = '';
                alert(`正在点亮储位${locations[currentCompleted]}，请继续盘点`);
            } else {
                alert('盘点任务已完成');
            }
        }

        // LED控制相关函数
        function initLEDControl() {
            // 初始化LED预览
            const ledStrip = document.getElementById('ledStrip');
            if (ledStrip) {
                ledStrip.innerHTML = '';
                for (let i = 0; i < 50; i++) {
                    const led = document.createElement('div');
                    led.className = 'led-dot';
                    led.id = `led-${i}`;
                    ledStrip.appendChild(led);
                }
                updateLEDPreview();
            }

            // 颜色选择器事件
            document.querySelectorAll('.color-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.color-option').forEach(o => o.classList.remove('selected'));
                    this.classList.add('selected');
                    updateLEDPreview();
                });
            });

            // 亮度滑块事件
            const brightnessSlider = document.getElementById('brightness');
            if (brightnessSlider) {
                brightnessSlider.addEventListener('input', function() {
                    document.getElementById('brightnessValue').textContent = this.value;
                });
            }

            // 输入框变化监听
            const startPos = document.getElementById('startPos');
            const endPos = document.getElementById('endPos');
            if (startPos) startPos.addEventListener('input', updateLEDPreview);
            if (endPos) endPos.addEventListener('input', updateLEDPreview);
        }

        function updateLEDPreview() {
            const startPos = parseInt(document.getElementById('startPos')?.value || 0);
            const endPos = parseInt(document.getElementById('endPos')?.value || 10);
            const selectedColor = document.querySelector('.color-option.selected')?.dataset.color || 'red';
            
            // 重置所有LED
            document.querySelectorAll('.led-dot').forEach(led => {
                led.style.backgroundColor = '#d9d9d9';
                led.classList.remove('active');
            });
            
            // 点亮指定范围的LED
            for (let i = startPos; i <= endPos && i < 50; i++) {
                const led = document.getElementById(`led-${i}`);
                if (led) {
                    led.style.backgroundColor = getColorValue(selectedColor);
                    led.classList.add('active');
                }
            }
        }

        function getColorValue(color) {
            const colors = {
                red: '#ff4d4f',
                green: '#52c41a',
                blue: '#1890ff',
                yellow: '#faad14',
                white: '#ffffff',
                black: '#000000',
                cyan: '#13c2c2',
                magenta: '#eb2f96'
            };
            return colors[color] || '#d9d9d9';
        }

        function sendLEDCommand() {
            const device = document.getElementById('deviceSelect')?.value;
            if (!device) {
                alert('请选择设备');
                return;
            }
            alert('LED控制指令已发送');
        }

        function testAllLEDs() {
            alert('正在测试所有LED...');
        }

        function turnOffAllLEDs() {
            alert('正在关闭所有LED...');
        }

        function testConnection(ip) {
            alert(`正在测试连接到 ${ip}...`);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 点击遮罩层关闭模态框
            const modalOverlay = document.getElementById('modalOverlay');
            if (modalOverlay) {
                modalOverlay.addEventListener('click', function(e) {
                    if (e.target === this) {
                        hideModal();
                    }
                });
            }

            // 初始化首页显示
            const dashboardModule = document.getElementById('dashboard');
            if (dashboardModule && dashboardModule.classList.contains('active')) {
                generateSidebarMenu('dashboard');
                showPageSection('dashboard', 'overview');
            }
        });
    </script>
</body>
</html>