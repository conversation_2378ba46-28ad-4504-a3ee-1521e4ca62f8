using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;
using WMS.API.Services;

namespace WMS.API.Controllers
{
    /// <summary>
    /// 认证控制器
    /// 提供用户登录、注销等认证功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IJwtTokenService jwtTokenService,
            ApplicationDbContext context,
            IConfiguration configuration,
            ILogger<AuthController> logger
        )
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _jwtTokenService = jwtTokenService;
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="request">登录请求</param>
        /// <returns>登录结果</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest request)
        {
            try
            {
                // 查找用户
                var user = await _userManager.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    _logger.LogWarning("登录失败：用户不存在 {Email}", request.Email);
                    return BadRequest(new { Message = "邮箱或密码错误" });
                }

                // 检查用户是否激活
                if (!user.IsActive)
                {
                    _logger.LogWarning("登录失败：用户已被停用 {Email}", request.Email);
                    return BadRequest(new { Message = "账户已被停用，请联系管理员" });
                }

                // 验证密码
                var passwordCheck = await _userManager.CheckPasswordAsync(user, request.Password);
                if (!passwordCheck)
                {
                    // 记录失败尝试
                    await _userManager.AccessFailedAsync(user);
                    _logger.LogWarning("登录失败：密码错误 {Email}", request.Email);
                    return BadRequest(new { Message = "邮箱或密码错误" });
                }

                // 检查账户是否被锁定
                if (await _userManager.IsLockedOutAsync(user))
                {
                    _logger.LogWarning("登录失败：账户已锁定 {Email}", request.Email);
                    return BadRequest(new { Message = "账户已锁定，请稍后再试" });
                }

                // 登录成功，重置失败计数
                await _userManager.ResetAccessFailedCountAsync(user);

                // 更新最后登录时间
                user.LastLoginAt = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);

                // 获取用户角色
                var roles = await _userManager.GetRolesAsync(user);

                // 生成JWT令牌
                var accessToken = await _jwtTokenService.GenerateAccessTokenAsync(user, roles);
                var refreshToken = _jwtTokenService.GenerateRefreshToken();

                // 保存刷新令牌到数据库
                _logger.LogInformation("开始保存RefreshToken到数据库 {Email}", request.Email);
                var refreshTokenEntity = new RefreshToken
                {
                    Token = refreshToken,
                    UserId = user.Id,
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddDays(7), // 7天过期
                    ClientIpAddress = GetClientIpAddress(),
                    UserAgent = Request.Headers["User-Agent"].ToString(),
                };

                _context.RefreshTokens.Add(refreshTokenEntity);
                _logger.LogInformation("RefreshToken已添加到Context，准备保存 {Email}", request.Email);
                
                await _context.SaveChangesAsync();
                _logger.LogInformation("RefreshToken保存成功 {Email}", request.Email);

                _logger.LogInformation("开始构造LoginResponse {Email}", request.Email);

                return Ok(
                    new LoginResponse
                    {
                        IsSuccess = true,
                        Message = "登录成功",
                        AccessToken = accessToken,
                        RefreshToken = refreshToken,
                        ExpiresIn = GetTokenExpiryMinutes(),
                        User = new UserInfo
                        {
                            Id = user.Id,
                            Email = user.Email!,
                            FullName = user.FullName,
                            EmployeeId = user.EmployeeId,
                            Department = user.Department,
                            Position = user.Position,
                            Roles = roles.ToList(),
                        },
                    }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录过程中发生错误 {Email}", request.Email);
                return StatusCode(500, new { Message = "登录失败，请稍后重试" });
            }
        }

        /// <summary>
        /// 用户注销
        /// </summary>
        /// <param name="request">注销请求</param>
        /// <returns>注销结果</returns>
        [HttpPost("logout")]
        [Authorize]
        public async Task<ActionResult> Logout([FromBody] LogoutRequest request)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user != null && !string.IsNullOrEmpty(request.RefreshToken))
                {
                    // 撤销刷新令牌
                    var refreshToken = await _context.RefreshTokens.FirstOrDefaultAsync(rt =>
                        rt.Token == request.RefreshToken && rt.UserId == user.Id
                    );

                    if (refreshToken != null && refreshToken.IsActive)
                    {
                        refreshToken.IsRevoked = true;
                        refreshToken.RevokedAt = DateTime.UtcNow;
                        refreshToken.RevokedReason = "User logout";
                        await _context.SaveChangesAsync();
                    }
                }

                _logger.LogInformation("用户注销成功 {UserId}", User.Identity?.Name);
                return Ok(new { Message = "注销成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注销过程中发生错误");
                return StatusCode(500, new { Message = "注销失败" });
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet("profile")]
        [Authorize]
        public async Task<ActionResult<UserInfo>> GetProfile()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在" });
                }

                var roles = await _userManager.GetRolesAsync(user);

                return Ok(
                    new UserInfo
                    {
                        Id = user.Id,
                        Email = user.Email!,
                        FullName = user.FullName,
                        EmployeeId = user.EmployeeId,
                        Department = user.Department,
                        Position = user.Position,
                        Roles = roles.ToList(),
                    }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息失败");
                return StatusCode(500, new { Message = "获取用户信息失败" });
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="request">修改密码请求</param>
        /// <returns>修改结果</returns>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<ActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在" });
                }

                var result = await _userManager.ChangePasswordAsync(
                    user,
                    request.CurrentPassword,
                    request.NewPassword
                );

                if (result.Succeeded)
                {
                    _logger.LogInformation("用户密码修改成功 {UserId}", user.Id);
                    return Ok(new { Message = "密码修改成功" });
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("密码修改失败 {UserId}: {Errors}", user.Id, errors);
                    return BadRequest(
                        new
                        {
                            Message = "密码修改失败",
                            Errors = result.Errors.Select(e => e.Description),
                        }
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密码过程中发生错误");
                return StatusCode(500, new { Message = "修改密码失败" });
            }
        }

        /// <summary>
        /// 刷新访问令牌
        /// </summary>
        /// <param name="request">刷新令牌请求</param>
        /// <returns>新的访问令牌</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<ActionResult<LoginResponse>> RefreshToken(
            [FromBody] RefreshTokenRequest request
        )
        {
            try
            {
                // 验证刷新令牌
                var refreshToken = await _context
                    .RefreshTokens.Include(rt => rt.User)
                    .FirstOrDefaultAsync(rt => rt.Token == request.RefreshToken);

                if (refreshToken == null || !refreshToken.IsActive)
                {
                    _logger.LogWarning("无效的刷新令牌: {Token}", request.RefreshToken);
                    return BadRequest(new { Message = "无效的刷新令牌" });
                }

                var user = refreshToken.User;
                if (!user.IsActive)
                {
                    _logger.LogWarning("用户账户已被停用: {UserId}", user.Id);
                    return BadRequest(new { Message = "账户已被停用" });
                }

                // 获取用户角色
                var roles = await _userManager.GetRolesAsync(user);

                // 生成新的访问令牌
                var newAccessToken = await _jwtTokenService.GenerateAccessTokenAsync(user, roles);
                var newRefreshToken = _jwtTokenService.GenerateRefreshToken();

                // 撤销旧的刷新令牌
                refreshToken.IsRevoked = true;
                refreshToken.RevokedAt = DateTime.UtcNow;
                refreshToken.RevokedReason = "Token refresh";

                // 创建新的刷新令牌
                var newRefreshTokenEntity = new RefreshToken
                {
                    Token = newRefreshToken,
                    UserId = user.Id,
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddDays(7),
                    ClientIpAddress = GetClientIpAddress(),
                    UserAgent = Request.Headers["User-Agent"].ToString(),
                    ReplacedByTokenId = refreshToken.Id,
                };

                _context.RefreshTokens.Add(newRefreshTokenEntity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("令牌刷新成功 {UserId}", user.Id);

                return Ok(
                    new LoginResponse
                    {
                        IsSuccess = true,
                        Message = "令牌刷新成功",
                        AccessToken = newAccessToken,
                        RefreshToken = newRefreshToken,
                        ExpiresIn = GetTokenExpiryMinutes(),
                        User = new UserInfo
                        {
                            Id = user.Id,
                            Email = user.Email!,
                            FullName = user.FullName,
                            EmployeeId = user.EmployeeId,
                            Department = user.Department,
                            Position = user.Position,
                            Roles = roles.ToList(),
                        },
                    }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新令牌过程中发生错误");
                return StatusCode(500, new { Message = "令牌刷新失败" });
            }
        }

        /// <summary>
        /// 撤销刷新令牌
        /// </summary>
        /// <param name="request">撤销令牌请求</param>
        /// <returns>撤销结果</returns>
        [HttpPost("revoke-token")]
        [Authorize]
        public async Task<ActionResult> RevokeToken([FromBody] RevokeTokenRequest request)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在" });
                }

                var refreshToken = await _context.RefreshTokens.FirstOrDefaultAsync(rt =>
                    rt.Token == request.RefreshToken && rt.UserId == user.Id
                );

                if (refreshToken == null)
                {
                    return BadRequest(new { Message = "令牌不存在" });
                }

                if (!refreshToken.IsActive)
                {
                    return BadRequest(new { Message = "令牌已无效" });
                }

                refreshToken.IsRevoked = true;
                refreshToken.RevokedAt = DateTime.UtcNow;
                refreshToken.RevokedReason = "Manual revocation";

                await _context.SaveChangesAsync();

                _logger.LogInformation("刷新令牌已撤销 {UserId}", user.Id);

                return Ok(new { Message = "令牌撤销成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "撤销令牌过程中发生错误");
                return StatusCode(500, new { Message = "令牌撤销失败" });
            }
        }

        /// <summary>
        /// 测试用户查找 (临时诊断接口)
        /// </summary>
        [HttpGet("test-user/{email}")]
        [AllowAnonymous]
        public async Task<ActionResult> TestUserLookup(string email)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在", Email = email });
                }

                return Ok(new 
                { 
                    Message = "用户找到",
                    UserId = user.Id,
                    Email = user.Email,
                    FullName = user.FullName,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试用户查找失败 {Email}", email);
                return StatusCode(500, new { Message = "测试失败", Error = ex.Message });
            }
        }

        /// <summary>
        /// 测试密码验证 (临时诊断接口)
        /// </summary>
        [HttpPost("test-password")]
        [AllowAnonymous]
        public async Task<ActionResult> TestPasswordCheck([FromBody] TestPasswordRequest request)
        {
            try
            {
                var user = await _userManager.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在" });
                }

                var passwordCheck = await _userManager.CheckPasswordAsync(user, request.Password);
                
                return Ok(new 
                { 
                    Message = "密码验证完成",
                    PasswordValid = passwordCheck,
                    IsActive = user.IsActive,
                    IsLockedOut = await _userManager.IsLockedOutAsync(user)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试密码验证失败 {Email}", request.Email);
                return StatusCode(500, new { Message = "测试失败", Error = ex.Message, StackTrace = ex.StackTrace });
            }
        }

        public class TestPasswordRequest
        {
            public string Email { get; set; } = string.Empty;
            public string Password { get; set; } = string.Empty;
        }

        /// <summary>
        /// 简化登录接口 (无RefreshToken)
        /// </summary>
        [HttpPost("login-simple")]
        [AllowAnonymous]
        public async Task<ActionResult<LoginResponse>> LoginSimple([FromBody] LoginRequest request)
        {
            try
            {
                _logger.LogInformation("简化登录开始 {Email}", request.Email);

                // 查找用户
                var user = await _userManager.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    return BadRequest(new { Message = "邮箱或密码错误" });
                }

                // 检查用户是否激活
                if (!user.IsActive)
                {
                    return BadRequest(new { Message = "账户已被停用，请联系管理员" });
                }

                // 验证密码
                var passwordCheck = await _userManager.CheckPasswordAsync(user, request.Password);
                if (!passwordCheck)
                {
                    return BadRequest(new { Message = "邮箱或密码错误" });
                }

                // 检查账户是否被锁定
                if (await _userManager.IsLockedOutAsync(user))
                {
                    return BadRequest(new { Message = "账户已锁定，请稍后再试" });
                }

                // 获取用户角色
                var roles = await _userManager.GetRolesAsync(user);

                // 生成JWT令牌
                var accessToken = await _jwtTokenService.GenerateAccessTokenAsync(user, roles);

                _logger.LogInformation("简化登录成功 {Email}", request.Email);

                return Ok(new LoginResponse
                {
                    IsSuccess = true,
                    Message = "登录成功",
                    AccessToken = accessToken,
                    RefreshToken = null, // 暂时不提供RefreshToken
                    ExpiresIn = GetTokenExpiryMinutes(),
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Email = user.Email!,
                        FullName = user.FullName,
                        EmployeeId = user.EmployeeId,
                        Department = user.Department,
                        Position = user.Position,
                        Roles = roles.ToList(),
                    },
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "简化登录过程中发生错误 {Email}", request.Email);
                return StatusCode(500, new { Message = "登录失败", Error = ex.Message });
            }
        }

        /// <summary>
        /// 修复版登录接口 (临时解决方案)
        /// </summary>
        [HttpPost("login-fixed")]
        [AllowAnonymous]
        public async Task<ActionResult<LoginResponse>> LoginFixed([FromBody] LoginRequest request)
        {
            try
            {
                _logger.LogInformation("修复版登录开始 {Email}", request.Email);

                // 查找用户
                var user = await _userManager.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    _logger.LogWarning("登录失败：用户不存在 {Email}", request.Email);
                    return BadRequest(new { Message = "邮箱或密码错误" });
                }

                // 检查用户是否激活
                if (!user.IsActive)
                {
                    _logger.LogWarning("登录失败：用户已被停用 {Email}", request.Email);
                    return BadRequest(new { Message = "账户已被停用，请联系管理员" });
                }

                // 验证密码
                var passwordCheck = await _userManager.CheckPasswordAsync(user, request.Password);
                if (!passwordCheck)
                {
                    await _userManager.AccessFailedAsync(user);
                    _logger.LogWarning("登录失败：密码错误 {Email}", request.Email);
                    return BadRequest(new { Message = "邮箱或密码错误" });
                }

                // 检查账户是否被锁定
                if (await _userManager.IsLockedOutAsync(user))
                {
                    _logger.LogWarning("登录失败：账户已锁定 {Email}", request.Email);
                    return BadRequest(new { Message = "账户已锁定，请稍后再试" });
                }

                // 登录成功，重置失败计数
                await _userManager.ResetAccessFailedCountAsync(user);

                // 更新最后登录时间
                user.LastLoginAt = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);

                // 获取用户角色
                var roles = await _userManager.GetRolesAsync(user);

                // 生成JWT令牌
                var accessToken = await _jwtTokenService.GenerateAccessTokenAsync(user, roles);
                var refreshToken = _jwtTokenService.GenerateRefreshToken();

                // 修复版：使用事务保存RefreshToken
                try
                {
                    using var transaction = await _context.Database.BeginTransactionAsync();
                    
                    // 清理该用户的旧RefreshToken
                    var oldTokens = _context.RefreshTokens.Where(rt => rt.UserId == user.Id && !rt.IsRevoked);
                    foreach (var oldToken in oldTokens)
                    {
                        oldToken.IsRevoked = true;
                        oldToken.RevokedAt = DateTime.UtcNow;
                        oldToken.RevokedReason = "New login";
                    }

                    // 保存刷新令牌到数据库
                    var refreshTokenEntity = new RefreshToken
                    {
                        Token = refreshToken,
                        UserId = user.Id,
                        CreatedAt = DateTime.UtcNow,
                        ExpiresAt = DateTime.UtcNow.AddDays(7),
                        ClientIpAddress = GetClientIpAddress(),
                        UserAgent = Request.Headers["User-Agent"].ToString() ?? "Unknown",
                    };

                    _context.RefreshTokens.Add(refreshTokenEntity);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    
                    _logger.LogInformation("RefreshToken保存成功 {Email}", request.Email);
                }
                catch (Exception dbEx)
                {
                    _logger.LogError(dbEx, "RefreshToken保存失败 {Email}", request.Email);
                    // 继续执行，不保存RefreshToken
                }

                _logger.LogInformation("修复版用户登录成功 {Email}", request.Email);

                return Ok(new LoginResponse
                {
                    IsSuccess = true,
                    Message = "登录成功",
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresIn = GetTokenExpiryMinutes(),
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Email = user.Email!,
                        FullName = user.FullName,
                        EmployeeId = user.EmployeeId,
                        Department = user.Department,
                        Position = user.Position,
                        Roles = roles.ToList(),
                    },
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修复版登录过程中发生错误 {Email}", request.Email);
                return StatusCode(500, new { Message = "登录失败，请稍后重试", Error = ex.Message });
            }
        }

        /// <summary>
        /// 分步诊断登录流程 (临时诊断接口)
        /// </summary>
        [HttpPost("test-login-steps")]
        [AllowAnonymous]
        public async Task<ActionResult> TestLoginSteps([FromBody] TestPasswordRequest request)
        {
            var steps = new List<string>();
            try
            {
                steps.Add("1. 开始诊断");

                // 步骤1: 查找用户
                var user = await _userManager.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    steps.Add("2. 用户查找: 失败 - 用户不存在");
                    return BadRequest(new { Steps = steps });
                }
                steps.Add($"2. 用户查找: 成功 - 用户ID: {user.Id}");

                // 步骤2: 检查用户状态
                if (!user.IsActive)
                {
                    steps.Add("3. 用户状态: 失败 - 用户未激活");
                    return BadRequest(new { Steps = steps });
                }
                steps.Add("3. 用户状态: 成功 - 用户已激活");

                // 步骤3: 验证密码
                var passwordCheck = await _userManager.CheckPasswordAsync(user, request.Password);
                if (!passwordCheck)
                {
                    steps.Add("4. 密码验证: 失败 - 密码错误");
                    return BadRequest(new { Steps = steps });
                }
                steps.Add("4. 密码验证: 成功");

                // 步骤4: 检查锁定状态
                var isLockedOut = await _userManager.IsLockedOutAsync(user);
                if (isLockedOut)
                {
                    steps.Add("5. 锁定检查: 失败 - 账户已锁定");
                    return BadRequest(new { Steps = steps });
                }
                steps.Add("5. 锁定检查: 成功 - 账户未锁定");

                // 步骤5: 获取用户角色
                var roles = await _userManager.GetRolesAsync(user);
                steps.Add($"6. 角色获取: 成功 - 角色数量: {roles.Count}");

                // 步骤6: 测试JWT生成
                try
                {
                    var accessToken = await _jwtTokenService.GenerateAccessTokenAsync(user, roles);
                    steps.Add($"7. JWT生成: 成功 - Token长度: {accessToken.Length}");
                }
                catch (Exception jwtEx)
                {
                    steps.Add($"7. JWT生成: 失败 - 错误: {jwtEx.Message}");
                    return StatusCode(500, new { Steps = steps, JwtError = jwtEx.Message });
                }

                // 步骤7: 测试RefreshToken生成
                try
                {
                    var refreshToken = _jwtTokenService.GenerateRefreshToken();
                    steps.Add($"8. RefreshToken生成: 成功 - Token长度: {refreshToken.Length}");
                }
                catch (Exception rtEx)
                {
                    steps.Add($"8. RefreshToken生成: 失败 - 错误: {rtEx.Message}");
                    return StatusCode(500, new { Steps = steps, RefreshTokenError = rtEx.Message });
                }

                steps.Add("9. 所有步骤完成 - 登录应该可以工作");
                return Ok(new { Message = "诊断完成", Steps = steps });
            }
            catch (Exception ex)
            {
                steps.Add($"ERROR: {ex.Message}");
                _logger.LogError(ex, "登录步骤诊断失败");
                return StatusCode(500, new { Steps = steps, Error = ex.Message, StackTrace = ex.StackTrace });
            }
        }

        #region Helper Methods

        private string GetClientIpAddress()
        {
            var xForwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(xForwardedFor))
            {
                return xForwardedFor.Split(',')[0].Trim();
            }

            var xRealIp = Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(xRealIp))
            {
                return xRealIp;
            }

            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        private int GetTokenExpiryMinutes()
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            return int.Parse(jwtSettings["ExpiryMinutes"] ?? "60");
        }

        #endregion
    }

    /// <summary>
    /// 登录请求
    /// </summary>
    public class LoginRequest
    {
        /// <summary>
        /// 邮箱
        /// </summary>
        [Required(ErrorMessage = "邮箱不能为空")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 记住我
        /// </summary>
        public bool RememberMe { get; set; } = false;
    }

    /// <summary>
    /// 登录响应
    /// </summary>
    public class LoginResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 访问令牌
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// 令牌过期时间（分钟）
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// 用户信息
        /// </summary>
        public UserInfo? User { get; set; }
    }

    /// <summary>
    /// 用户信息
    /// </summary>
    public class UserInfo
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 姓名
        /// </summary>
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// 员工工号
        /// </summary>
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string? Department { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        public string? Position { get; set; }

        /// <summary>
        /// 角色列表
        /// </summary>
        public List<string> Roles { get; set; } = new();
    }

    /// <summary>
    /// 修改密码请求
    /// </summary>
    public class ChangePasswordRequest
    {
        /// <summary>
        /// 当前密码
        /// </summary>
        [Required(ErrorMessage = "当前密码不能为空")]
        public string CurrentPassword { get; set; } = string.Empty;

        /// <summary>
        /// 新密码
        /// </summary>
        [Required(ErrorMessage = "新密码不能为空")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "密码长度必须在8-100字符之间")]
        public string NewPassword { get; set; } = string.Empty;

        /// <summary>
        /// 确认新密码
        /// </summary>
        [Required(ErrorMessage = "确认密码不能为空")]
        [Compare("NewPassword", ErrorMessage = "两次输入的密码不一致")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 注销请求
    /// </summary>
    public class LogoutRequest
    {
        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string? RefreshToken { get; set; }
    }

    /// <summary>
    /// 刷新令牌请求
    /// </summary>
    public class RefreshTokenRequest
    {
        /// <summary>
        /// 刷新令牌
        /// </summary>
        [Required(ErrorMessage = "刷新令牌不能为空")]
        public string RefreshToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// 撤销令牌请求
    /// </summary>
    public class RevokeTokenRequest
    {
        /// <summary>
        /// 要撤销的刷新令牌
        /// </summary>
        [Required(ErrorMessage = "刷新令牌不能为空")]
        public string RefreshToken { get; set; } = string.Empty;
    }
}
