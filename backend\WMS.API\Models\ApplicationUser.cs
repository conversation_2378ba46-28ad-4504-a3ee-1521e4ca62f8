using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace WMS.API.Models
{
    /// <summary>
    /// 应用程序用户实体类
    /// 扩展ASP.NET Core Identity的IdentityUser
    /// </summary>
    public class ApplicationUser : IdentityUser
    {
        /// <summary>
        /// 用户真实姓名
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// 员工工号
        /// </summary>
        [StringLength(50)]
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [StringLength(100)]
        public string? Department { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [StringLength(100)]
        public string? Position { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 账号创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 账号更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者ID
        /// </summary>
        public string? CreatedById { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        public virtual ApplicationUser? CreatedBy { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
}