using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    public enum OutboundOrderStatus
    {
        Pending = 0, // 待处理
        InProgress = 1, // 进行中
        Picked = 2, // 已拣选
        Completed = 3, // 已完成
        Cancelled = 4, // 已取消
    }

    public enum OutboundOrderType
    {
        Sale = 0, // 销售出库
        Transfer = 1, // 调拨出库
        Return = 2, // 退货出库
        Adjustment = 3, // 调整出库
    }

    public class OutboundOrder
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string OrderNumber { get; set; } = string.Empty;

        public OutboundOrderType Type { get; set; } = OutboundOrderType.Sale;

        [StringLength(100)]
        public string? CustomerName { get; set; }

        [StringLength(50)]
        public string? CustomerCode { get; set; }

        public OutboundOrderStatus Status { get; set; } = OutboundOrderStatus.Pending;

        public DateTime? ExpectedDate { get; set; }
        public DateTime? ActualDate { get; set; }

        [StringLength(500)]
        public string? Remarks { get; set; }

        [StringLength(100)]
        public string? CreatedBy { get; set; }

        [StringLength(100)]
        public string? ProcessedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // 导航属性
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<OutboundOrderItem>? Items { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<InventoryTransaction>? Transactions { get; set; }
    }

    public class OutboundOrderItem
    {
        public int Id { get; set; }

        public int OutboundOrderId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual OutboundOrder? OutboundOrder { get; set; }

        public int MaterialId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual Material? Material { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal RequiredQuantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? PickedQuantity { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? LpnCode { get; set; }

        [StringLength(200)]
        public string? Remarks { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // 导航属性 - 拣选任务
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<PickingTask>? PickingTasks { get; set; }
    }

    public enum PickingTaskStatus
    {
        Pending = 0, // 待拣选
        InProgress = 1, // 拣选中
        Completed = 2, // 已完成
        Cancelled = 3, // 已取消
    }

    public class PickingTask
    {
        public int Id { get; set; }

        public int OutboundOrderItemId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual OutboundOrderItem? OutboundOrderItem { get; set; }

        public int StorageLocationId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual StorageLocation? StorageLocation { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal RequiredQuantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? PickedQuantity { get; set; }

        public PickingTaskStatus Status { get; set; } = PickingTaskStatus.Pending;

        [StringLength(100)]
        public string? AssignedTo { get; set; }

        [StringLength(100)]
        public string? CompletedBy { get; set; }

        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }

        [StringLength(200)]
        public string? Remarks { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
