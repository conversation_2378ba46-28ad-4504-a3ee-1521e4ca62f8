using WMS.API.Models;

namespace WMS.API.Services
{
    /// <summary>
    /// JWT Token服务接口
    /// </summary>
    public interface IJwtTokenService
    {
        /// <summary>
        /// 生成JWT访问令牌
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="roles">用户角色列表</param>
        /// <returns>JWT令牌</returns>
        Task<string> GenerateAccessTokenAsync(ApplicationUser user, IList<string> roles);

        /// <summary>
        /// 生成刷新令牌
        /// </summary>
        /// <returns>刷新令牌</returns>
        string GenerateRefreshToken();

        /// <summary>
        /// 验证刷新令牌
        /// </summary>
        /// <param name="token">刷新令牌</param>
        /// <returns>是否有效</returns>
        bool ValidateRefreshToken(string token);

        /// <summary>
        /// 从JWT令牌中提取用户ID
        /// </summary>
        /// <param name="token">JWT令牌</param>
        /// <returns>用户ID</returns>
        string? GetUserIdFromToken(string token);
    }
}