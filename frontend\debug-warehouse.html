<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库API调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input, select, textarea { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 120px; }
    </style>
</head>
<body>
    <h1>🔧 仓库API调试工具</h1>
    
    <div class="section">
        <h2>📋 当前配置</h2>
        <p><strong>API Base URL:</strong> <span id="apiBaseUrl"></span></p>
        <p><strong>当前时间:</strong> <span id="currentTime"></span></p>
        <p><strong>浏览器:</strong> <span id="browserInfo"></span></p>
    </div>

    <div class="section">
        <h2>🌐 网络连接测试</h2>
        <button class="button" onclick="testConnection()">测试API连接</button>
        <button class="button" onclick="testCORS()">测试CORS</button>
        <div id="connectionResult"></div>
    </div>

    <div class="section">
        <h2>📦 仓库API测试</h2>
        
        <h3>获取仓库列表</h3>
        <button class="button" onclick="testGetWarehouses()">GET /warehouses</button>
        <div id="getWarehousesResult"></div>

        <h3>创建仓库</h3>
        <div class="form-group">
            <label>仓库编码:</label>
            <input type="text" id="warehouseCode" value="TEST001" />
        </div>
        <div class="form-group">
            <label>仓库名称:</label>
            <input type="text" id="warehouseName" value="测试仓库" />
        </div>
        <div class="form-group">
            <label>仓库类型:</label>
            <select id="warehouseType">
                <option value="distribution">配送中心</option>
                <option value="storage">存储仓库</option>
                <option value="production">生产仓库</option>
            </select>
        </div>
        <div class="form-group">
            <label>状态:</label>
            <select id="warehouseStatus">
                <option value="Active">运营中</option>
                <option value="Maintenance">维护中</option>
                <option value="Inactive">已停用</option>
            </select>
        </div>
        <div class="form-group">
            <label>地址:</label>
            <input type="text" id="warehouseAddress" value="测试地址123号" />
        </div>
        <div class="form-group">
            <label>联系人:</label>
            <input type="text" id="contactPerson" value="张三" />
        </div>
        <div class="form-group">
            <label>联系电话:</label>
            <input type="text" id="contactPhone" value="13800138000" />
        </div>
        <button class="button" onclick="testCreateWarehouse()">POST /warehouses</button>
        <div id="createWarehouseResult"></div>
    </div>

    <div class="section">
        <h2>📊 请求详情日志</h2>
        <button class="button" onclick="clearLogs()">清空日志</button>
        <pre id="requestLogs"></pre>
    </div>

    <script>
        // 初始化页面信息
        document.getElementById('apiBaseUrl').textContent = 'http://localhost:8080/api';
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
        document.getElementById('browserInfo').textContent = navigator.userAgent;

        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logElement = document.getElementById('requestLogs');
            logElement.textContent = logs.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLogs() {
            logs = [];
            document.getElementById('requestLogs').textContent = '';
        }

        async function makeRequest(url, options = {}) {
            const fullUrl = `http://localhost:8080/api${url}`;
            
            log(`🚀 发起请求: ${options.method || 'GET'} ${fullUrl}`);
            
            if (options.body) {
                log(`📤 请求体: ${JSON.stringify(JSON.parse(options.body), null, 2)}`);
            }
            
            try {
                const response = await fetch(fullUrl, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                log(`📥 响应状态: ${response.status} ${response.statusText}`);
                log(`📥 响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
                
                const responseText = await response.text();
                log(`📥 响应体: ${responseText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }
                
                return responseText ? JSON.parse(responseText) : null;
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<p class="info">正在测试连接...</p>';
            
            try {
                const response = await fetch('http://localhost:8080/api/health', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = '<p class="success">✅ API服务连接成功</p>';
                } else {
                    resultDiv.innerHTML = `<p class="error">❌ API服务响应异常: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 无法连接到API服务: ${error.message}</p>`;
            }
        }

        async function testCORS() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML += '<p class="info">正在测试CORS...</p>';
            
            try {
                const response = await fetch('http://localhost:8080/api/warehouses', {
                    method: 'OPTIONS'
                });
                resultDiv.innerHTML += '<p class="success">✅ CORS配置正常</p>';
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">❌ CORS配置问题: ${error.message}</p>`;
            }
        }

        async function testGetWarehouses() {
            const resultDiv = document.getElementById('getWarehousesResult');
            resultDiv.innerHTML = '<p class="info">正在获取仓库列表...</p>';
            
            try {
                const data = await makeRequest('/warehouses');
                resultDiv.innerHTML = `<p class="success">✅ 获取成功</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 获取失败: ${error.message}</p>`;
            }
        }

        async function testCreateWarehouse() {
            const resultDiv = document.getElementById('createWarehouseResult');
            resultDiv.innerHTML = '<p class="info">正在创建仓库...</p>';
            
            const warehouseData = {
                code: document.getElementById('warehouseCode').value,
                name: document.getElementById('warehouseName').value,
                warehouseType: document.getElementById('warehouseType').value,
                status: document.getElementById('warehouseStatus').value,
                address: document.getElementById('warehouseAddress').value,
                contactPerson: document.getElementById('contactPerson').value,
                contactPhone: document.getElementById('contactPhone').value,
                description: "通过调试工具创建的测试仓库",
                totalArea: 1000.0,
                totalVolume: 3000.0,
                configuration: "{}"
            };
            
            try {
                const data = await makeRequest('/warehouses', {
                    method: 'POST',
                    body: JSON.stringify(warehouseData)
                });
                resultDiv.innerHTML = `<p class="success">✅ 创建成功</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ 创建失败: ${error.message}</p>`;
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
