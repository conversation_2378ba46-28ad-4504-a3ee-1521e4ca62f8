#\!/bin/bash

# 获取token
echo "=== 获取认证Token ==="
TOKEN_RESPONSE=$(curl -s -X POST "http://************:5000/api/Auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin@123456",
    "rememberMe": false
  }')

TOKEN=$(echo "$TOKEN_RESPONSE"  < /dev/null |  grep -o '"accessToken":"[^"]*"' | sed 's/"accessToken":"\(.*\)"/\1/')
echo "Token获取成功"

# 测试1: 为储位添加能力
echo -e "\n=== 测试1: 为储位添加能力 ==="
RESPONSE=$(curl -s -X POST "http://************:5000/api/StorageLocations/1/capabilities" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "HTTP_STATUS:%{http_code}" \
  -d '{
    "capabilityType": 1,
    "capabilityLevel": 3,
    "capabilityName": "温度控制",
    "description": "精确温度控制，适合生鲜食品存储",
    "parameters": "{\"targetTemperature\":4,\"tolerance\":1,\"unit\":\"celsius\",\"monitoringInterval\":300}",
    "isEnabled": true,
    "priority": 1,
    "validationRules": "{\"temperatureRange\":{\"min\":2,\"max\":8},\"alertThreshold\":0.5}",
    "effectiveFrom": "2025-07-04T00:00:00Z",
    "certifications": "{\"haccp\":true,\"iso22000\":true}"
  }')

HTTP_STATUS=$(echo "$RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "HTTP状态码: $HTTP_STATUS"
echo "响应内容: $RESPONSE_BODY"

# 测试2: 为储位添加分类
echo -e "\n=== 测试2: 为储位添加分类 ==="
RESPONSE=$(curl -s -X POST "http://************:5000/api/StorageLocations/1/classifications" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "HTTP_STATUS:%{http_code}" \
  -d '{
    "dimension": 1,
    "category": "Industry",
    "value": "Food",
    "displayName": "食品行业",
    "description": "适用于食品存储的储位",
    "tags": "[\"food-grade\",\"haccp-compliant\"]",
    "properties": "{\"temperatureControlled\":true,\"humidityControlled\":false,\"sanitationRequired\":true}",
    "priority": 1,
    "isEnabled": true,
    "businessRules": "{\"autoAssignment\":true,\"materialTypes\":[\"fresh\",\"frozen\",\"dry_goods\"],\"restrictions\":[\"no_chemicals\",\"no_non_food\"]}"
  }')

HTTP_STATUS=$(echo "$RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "HTTP状态码: $HTTP_STATUS"
echo "响应内容: $RESPONSE_BODY"

# 测试3: 货架安全检查 
echo -e "\n=== 测试3: 货架安全检查 ==="
RESPONSE=$(curl -s -X POST "http://************:5000/api/Shelves/1/inspection" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "HTTP_STATUS:%{http_code}" \
  -d '{
    "inspectionType": "safety",
    "result": "passed",
    "inspectorName": "李工程师",
    "nextInspectionDate": "2025-10-04T10:00:00Z"
  }')

HTTP_STATUS=$(echo "$RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "HTTP状态码: $HTTP_STATUS"
echo "响应内容: $RESPONSE_BODY"

# 测试4: 设置仓库级配置
echo -e "\n=== 测试4: 设置仓库级配置 ==="
RESPONSE=$(curl -s -X PUT "http://************:5000/api/Configuration/warehouse/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "HTTP_STATUS:%{http_code}" \
  -d '{
    "globalSettings": {
      "operatingHours": "06:00-22:00",
      "defaultLanguage": "zh-CN"
    },
    "securitySettings": {
      "accessCardRequired": true
    }
  }')

HTTP_STATUS=$(echo "$RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "HTTP状态码: $HTTP_STATUS"
echo "响应内容: $RESPONSE_BODY"
