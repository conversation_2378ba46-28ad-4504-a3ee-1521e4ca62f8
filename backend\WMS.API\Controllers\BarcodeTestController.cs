using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WMS.API.DTOs.Barcode;
using WMS.API.Services;

namespace WMS.API.Controllers
{
    /// <summary>
    /// 条码解析测试控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class BarcodeTestController : ControllerBase
    {
        private readonly IBarcodeParsingService _barcodeParsingService;
        private readonly IConfigurationResolutionService _configurationService;
        private readonly ILogger<BarcodeTestController> _logger;

        public BarcodeTestController(
            IBarcodeParsingService barcodeParsingService,
            IConfigurationResolutionService configurationService,
            ILogger<BarcodeTestController> logger
        )
        {
            _barcodeParsingService = barcodeParsingService;
            _configurationService = configurationService;
            _logger = logger;
        }

        /// <summary>
        /// 测试物料条码解析
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpPost("test-material-barcode")]
        public async Task<ActionResult> TestMaterialBarcode()
        {
            try
            {
                var testCases = new[]
                {
                    new
                    {
                        Barcode = "MATSKU001QTY100BATCHB2024001UNIQUEUC12345678",
                        Description = "标准物料条码",
                    },
                    new { Barcode = "SKU12345", Description = "简化物料条码" },
                    new { Barcode = "MATINVALIDFORMAT", Description = "无效格式条码" },
                };

                var results = new List<object>();

                foreach (var testCase in testCases)
                {
                    var context = new BarcodeContext
                    {
                        OperationType = "INBOUND",
                        ClientId = "DEFAULT_CLIENT",
                        UserId = User.Identity?.Name,
                    };

                    var result = await _barcodeParsingService.ParseBarcodeAsync(
                        testCase.Barcode,
                        context
                    );

                    results.Add(
                        new
                        {
                            TestCase = testCase.Description,
                            Barcode = testCase.Barcode,
                            IsValid = result.IsValid,
                            MaterialSku = result.MaterialSku,
                            Quantity = result.Quantity,
                            BatchNumber = result.BatchNumber,
                            UniqueCode = result.UniqueCode,
                            MatchedRule = result.MatchedRule?.RuleName,
                            ValidationErrors = result.ValidationErrors,
                            ProcessingTimeMs = result.ProcessingTimeMs,
                        }
                    );
                }

                return Ok(new { TestResults = results });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试物料条码解析时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 测试储位条码解析
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpPost("test-location-barcode")]
        public async Task<ActionResult> TestLocationBarcode()
        {
            try
            {
                var testCases = new[]
                {
                    new { Barcode = "LOCA01-B02-C03", Description = "标准储位条码" },
                    new { Barcode = "A01-B02-C03", Description = "简化储位条码" },
                };

                var results = new List<object>();

                foreach (var testCase in testCases)
                {
                    var context = new BarcodeContext
                    {
                        OperationType = "INBOUND",
                        ClientId = "DEFAULT_CLIENT",
                        UserId = User.Identity?.Name,
                    };

                    var result = await _barcodeParsingService.ParseBarcodeAsync(
                        testCase.Barcode,
                        context
                    );

                    results.Add(
                        new
                        {
                            TestCase = testCase.Description,
                            Barcode = testCase.Barcode,
                            IsValid = result.IsValid,
                            StorageLocationCode = result.StorageLocationCode,
                            ExtendedFields = result.ExtendedFields,
                            MatchedRule = result.MatchedRule?.RuleName,
                            ValidationErrors = result.ValidationErrors,
                            ProcessingTimeMs = result.ProcessingTimeMs,
                        }
                    );
                }

                return Ok(new { TestResults = results });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试储位条码解析时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 测试配置解析
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpPost("test-configuration")]
        public async Task<ActionResult> TestConfiguration()
        {
            try
            {
                var testScenarios = new[]
                {
                    new ConfigurationContext
                    {
                        ClientId = "DEFAULT_CLIENT",
                        ZoneCode = "A",
                        ShelfCode = "A01",
                        LocationCode = "A01-01",
                        MaterialCategory = "MEDICINE",
                        OperationType = "INBOUND",
                        UserId = User.Identity?.Name,
                    },
                    new ConfigurationContext
                    {
                        ClientId = "DEFAULT_CLIENT",
                        ZoneCode = "B",
                        ShelfCode = "B01",
                        LocationCode = "B01-01",
                        MaterialCategory = "GENERAL",
                        OperationType = "OUTBOUND",
                        UserId = User.Identity?.Name,
                    },
                };

                var configKeys = new[]
                {
                    "uniqueCodeControl",
                    "storageLocationPolicy",
                    "inventoryRules",
                    "ledIndicator",
                };
                var results = new List<object>();

                foreach (var scenario in testScenarios)
                {
                    var scenarioResults = await _configurationService.ResolveConfigurationsAsync(
                        configKeys,
                        scenario
                    );

                    results.Add(
                        new
                        {
                            Scenario = $"Zone:{scenario.ZoneCode}, Shelf:{scenario.ShelfCode}, Location:{scenario.LocationCode}, Category:{scenario.MaterialCategory}",
                            Configurations = scenarioResults.Select(r => new
                            {
                                ConfigKey = r.Key,
                                ConfigValue = r.Value.ConfigValue,
                                AppliedLevel = r.Value.AppliedConfigLevel,
                                HasConflicts = r.Value.HasConflicts,
                                ResolutionPath = r.Value.ResolutionPath,
                            }),
                        }
                    );
                }

                return Ok(new { TestResults = results });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试配置解析时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 创建测试数据
        /// </summary>
        /// <returns>创建结果</returns>
        [HttpPost("create-test-data")]
        public Task<ActionResult> CreateTestData()
        {
            try
            {
                // 这里可以创建一些测试用的物料、储位等数据
                // 为了简化，我们只返回一个成功的消息

                var testDataInfo = new
                {
                    Message = "测试数据创建完成",
                    CreatedItems = new
                    {
                        BarcodeRules = "已创建默认的条码解析规则",
                        ValidationRules = "已创建默认的验证规则",
                        Configurations = "已创建默认的分层配置",
                        SuggestedTestBarcodes = new[]
                        {
                            "MATSKU001QTY100BATCHB2024001UNIQUEUC12345678",
                            "SKU12345",
                            "LOCA01-B02-C03",
                            "A01-B02-C03",
                        },
                    },
                };

                return Task.FromResult<ActionResult>(Ok(testDataInfo));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建测试数据时发生错误");
                return Task.FromResult<ActionResult>(StatusCode(500, "服务器内部错误"));
            }
        }

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <returns>系统状态</returns>
        [HttpGet("system-status")]
        public async Task<ActionResult> GetSystemStatus()
        {
            try
            {
                var rules = await _barcodeParsingService.GetActiveRulesAsync();
                var clientConfigs = await _configurationService.GetClientConfigurationsAsync(
                    "DEFAULT_CLIENT"
                );

                var status = new
                {
                    BarcodeParsingRules = rules.Count,
                    ActiveRulesByCategory = rules
                        .GroupBy(r => r.Category?.CategoryCode)
                        .ToDictionary(g => g.Key ?? "Unknown", g => g.Count()),
                    HierarchicalConfigurations = clientConfigs.Count,
                    ConfigurationsByLevel = clientConfigs
                        .GroupBy(c => c.ConfigLevel)
                        .ToDictionary(g => g.Key.ToString(), g => g.Count()),
                    SystemTime = DateTime.UtcNow,
                    Status = "运行正常",
                };

                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统状态时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }
    }
}
