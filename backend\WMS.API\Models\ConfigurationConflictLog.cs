using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMS.API.Models
{
    /// <summary>
    /// 配置冲突类型枚举
    /// </summary>
    public enum ConfigurationConflictType
    {
        /// <summary>
        /// 规则冲突
        /// </summary>
        RULE_CONFLICT,

        /// <summary>
        /// 优先级冲突
        /// </summary>
        PRIORITY_CONFLICT,

        /// <summary>
        /// 继承冲突
        /// </summary>
        INHERITANCE_CONFLICT,
    }

    /// <summary>
    /// 配置冲突记录表
    /// </summary>
    public class ConfigurationConflictLog
    {
        /// <summary>
        /// 冲突记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 冲突类型
        /// </summary>
        public ConfigurationConflictType ConflictType { get; set; }

        /// <summary>
        /// 冲突影响范围
        /// </summary>
        [Column(TypeName = "text")]
        public string? TargetScope { get; set; }

        /// <summary>
        /// 冲突的配置详情（JSON格式）
        /// </summary>
        [Column(TypeName = "jsonb")]
        public string? ConflictingConfigs { get; set; }

        /// <summary>
        /// 解决策略
        /// </summary>
        [StringLength(50)]
        public string? ResolutionStrategy { get; set; }

        /// <summary>
        /// 最终解决值（JSON格式）
        /// </summary>
        [Column(TypeName = "jsonb")]
        public string? ResolvedValue { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 解决者
        /// </summary>
        [StringLength(100)]
        public string? ResolvedBy { get; set; }

        /// <summary>
        /// 冲突描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }
    }
}
