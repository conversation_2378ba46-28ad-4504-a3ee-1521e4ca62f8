using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using WMS.API.Models;

namespace WMS.API.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options) { }

        public DbSet<ESP32Controller> ESP32Controllers { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<Zone> Zones { get; set; }
        public DbSet<Shelf> Shelves { get; set; }
        public DbSet<StorageLocation> StorageLocations { get; set; }
        public DbSet<LocationCapability> LocationCapabilities { get; set; }
        public DbSet<LocationClassification> LocationClassifications { get; set; }
        public DbSet<Material> Materials { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }

        // 条码解析相关表
        public DbSet<BarcodeRuleCategory> BarcodeRuleCategories { get; set; }
        public DbSet<BarcodeParsingRule> BarcodeParsingRules { get; set; }
        public DbSet<BarcodeValidationRule> BarcodeValidationRules { get; set; }
        public DbSet<UniqueCodeRegistry> UniqueCodeRegistries { get; set; }
        public DbSet<BarcodeParsingLog> BarcodeParsingLogs { get; set; }

        // 分层配置相关表
        public DbSet<HierarchicalConfiguration> HierarchicalConfigurations { get; set; }
        public DbSet<ConfigurationApplicationLog> ConfigurationApplicationLogs { get; set; }
        public DbSet<ConfigurationConflictLog> ConfigurationConflictLogs { get; set; }

        // 出入库相关表
        public DbSet<InboundOrder> InboundOrders { get; set; }
        public DbSet<InboundOrderItem> InboundOrderItems { get; set; }
        public DbSet<OutboundOrder> OutboundOrders { get; set; }
        public DbSet<OutboundOrderItem> OutboundOrderItems { get; set; }
        public DbSet<PickingTask> PickingTasks { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // ApplicationUser 配置
            modelBuilder.Entity<ApplicationUser>(entity =>
            {
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.EmployeeId).HasMaxLength(50);
                entity.Property(e => e.Department).HasMaxLength(100);
                entity.Property(e => e.Position).HasMaxLength(100);
                entity.Property(e => e.Remarks).HasMaxLength(500);

                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.LastLoginAt).HasColumnType("timestamptz");

                // 自引用关系（创建者）
                entity
                    .HasOne(e => e.CreatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedById)
                    .OnDelete(DeleteBehavior.SetNull);

                // 员工工号唯一约束（如果不为空）
                entity
                    .HasIndex(e => e.EmployeeId)
                    .IsUnique()
                    .HasFilter("\"EmployeeId\" IS NOT NULL");
            });

            // RefreshToken 配置
            modelBuilder.Entity<RefreshToken>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Token).IsRequired().HasMaxLength(500);
                entity.Property(e => e.UserId).IsRequired();
                entity.Property(e => e.RevokedReason).HasMaxLength(200);
                entity.Property(e => e.ClientIpAddress).HasMaxLength(50);
                entity.Property(e => e.UserAgent).HasMaxLength(500);

                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ExpiresAt).HasColumnType("timestamptz");
                entity.Property(e => e.RevokedAt).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                // 自引用关系（令牌替换）
                entity
                    .HasOne(e => e.ReplacedByToken)
                    .WithMany()
                    .HasForeignKey(e => e.ReplacedByTokenId)
                    .OnDelete(DeleteBehavior.SetNull);

                // 索引
                entity.HasIndex(e => e.Token).IsUnique();
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => new { e.UserId, e.IsRevoked });
            });

            // Warehouse 配置
            modelBuilder.Entity<Warehouse>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Address).HasMaxLength(300);
                entity.Property(e => e.WarehouseType).HasMaxLength(50);
                entity.Property(e => e.ContactPerson).HasMaxLength(50);
                entity.Property(e => e.ContactPhone).HasMaxLength(20);
                entity.Property(e => e.Configuration).HasColumnType("jsonb");
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.UpdatedBy).HasMaxLength(100);
                entity.HasIndex(e => e.Code).IsUnique();
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
            });

            // Zone 配置
            modelBuilder.Entity<Zone>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.TemperatureRange).HasColumnType("jsonb");
                entity.Property(e => e.HumidityRange).HasColumnType("jsonb");
                entity.Property(e => e.AccessControl).HasColumnType("jsonb");
                entity.Property(e => e.SpecialRequirements).HasColumnType("jsonb");
                entity.Property(e => e.Configuration).HasColumnType("jsonb");
                entity.Property(e => e.ResponsiblePerson).HasMaxLength(50);
                entity.Property(e => e.ContactInfo).HasMaxLength(50);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.UpdatedBy).HasMaxLength(100);
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.Warehouse)
                    .WithMany(w => w.Zones)
                    .HasForeignKey(e => e.WarehouseId)
                    .OnDelete(DeleteBehavior.Restrict);

                // 复合唯一约束：同一仓库内区域编码唯一
                entity.HasIndex(e => new { e.WarehouseId, e.Code }).IsUnique();
            });

            // Shelf 配置
            modelBuilder.Entity<Shelf>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Material).HasMaxLength(50);
                entity.Property(e => e.Manufacturer).HasMaxLength(100);
                entity.Property(e => e.SafetyCertifications).HasColumnType("jsonb");
                entity.Property(e => e.StoragePolicy).HasColumnType("jsonb");
                entity.Property(e => e.AccessRestrictions).HasColumnType("jsonb");
                entity.Property(e => e.Configuration).HasColumnType("jsonb");
                entity.Property(e => e.ResponsiblePerson).HasMaxLength(50);
                entity.Property(e => e.Remarks).HasMaxLength(1000);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.UpdatedBy).HasMaxLength(100);
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ManufactureDate).HasColumnType("timestamptz");
                entity.Property(e => e.InstallationDate).HasColumnType("timestamptz");
                entity.Property(e => e.LastInspectionDate).HasColumnType("timestamptz");
                entity.Property(e => e.NextInspectionDate).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.Zone)
                    .WithMany(z => z.Shelves)
                    .HasForeignKey(e => e.ZoneId)
                    .OnDelete(DeleteBehavior.Restrict);

                // 复合唯一约束：同一区域内货架编码唯一
                entity.HasIndex(e => new { e.ZoneId, e.Code }).IsUnique();
            });

            // LocationCapability 配置
            modelBuilder.Entity<LocationCapability>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CapabilityName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Parameters).HasColumnType("jsonb");
                entity.Property(e => e.Configuration).HasColumnType("jsonb");
                entity.Property(e => e.ValidationRules).HasColumnType("jsonb");
                entity.Property(e => e.Certifications).HasColumnType("jsonb");
                entity.Property(e => e.Remarks).HasMaxLength(500);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.UpdatedBy).HasMaxLength(100);
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.EffectiveFrom).HasColumnType("timestamptz");
                entity.Property(e => e.EffectiveTo).HasColumnType("timestamptz");
                entity.Property(e => e.LastVerificationDate).HasColumnType("timestamptz");
                entity.Property(e => e.NextVerificationDate).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.StorageLocation)
                    .WithMany(s => s.Capabilities)
                    .HasForeignKey(e => e.StorageLocationId)
                    .OnDelete(DeleteBehavior.Cascade);

                // 复合唯一约束：同一储位的同类型能力唯一
                entity.HasIndex(e => new { e.StorageLocationId, e.CapabilityType }).IsUnique();
            });

            // LocationClassification 配置
            modelBuilder.Entity<LocationClassification>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Category).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Value).IsRequired().HasMaxLength(100);
                entity.Property(e => e.DisplayName).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Tags).HasColumnType("jsonb");
                entity.Property(e => e.Properties).HasColumnType("jsonb");
                entity.Property(e => e.Configuration).HasColumnType("jsonb");
                entity.Property(e => e.InheritedFrom).HasMaxLength(100);
                entity.Property(e => e.ValidationRules).HasColumnType("jsonb");
                entity.Property(e => e.BusinessRules).HasColumnType("jsonb");
                entity.Property(e => e.ExternalSystemRef).HasMaxLength(100);
                entity.Property(e => e.Remarks).HasMaxLength(500);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.UpdatedBy).HasMaxLength(100);
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.EffectiveFrom).HasColumnType("timestamptz");
                entity.Property(e => e.EffectiveTo).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.StorageLocation)
                    .WithMany(s => s.Classifications)
                    .HasForeignKey(e => e.StorageLocationId)
                    .OnDelete(DeleteBehavior.Cascade);

                // 复合唯一约束：同一储位的同维度同类别分类唯一
                entity
                    .HasIndex(e => new
                    {
                        e.StorageLocationId,
                        e.Dimension,
                        e.Category,
                    })
                    .IsUnique();
            });

            // StorageLocation 配置（更新）
            modelBuilder.Entity<StorageLocation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Name).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Dimensions).HasColumnType("jsonb");
                entity.Property(e => e.Level).HasMaxLength(20);
                entity.Property(e => e.Position).HasMaxLength(20);
                entity.Property(e => e.Coordinates).HasColumnType("jsonb");
                entity.Property(e => e.Properties).HasColumnType("jsonb");
                entity.Property(e => e.Configuration).HasColumnType("jsonb");
                entity.Property(e => e.ResponsiblePerson).HasMaxLength(50);
                entity.Property(e => e.Remarks).HasMaxLength(1000);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.UpdatedBy).HasMaxLength(100);
                // 保留的兼容性字段
                entity.Property(e => e.Zone).HasMaxLength(20);
                entity.Property(e => e.Aisle).HasMaxLength(20);
                entity.Property(e => e.ShelfCode).HasMaxLength(20);
                entity.HasIndex(e => e.Code).IsUnique();
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.LastInventoryDate).HasColumnType("timestamptz");
                entity.Property(e => e.NextInventoryDate).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.Shelf)
                    .WithMany(s => s.StorageLocations)
                    .HasForeignKey(e => e.ShelfId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity
                    .HasOne(e => e.ESP32Controller)
                    .WithMany(c => c.StorageLocations)
                    .HasForeignKey(e => e.ESP32ControllerId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // ESP32Controller 配置
            modelBuilder.Entity<ESP32Controller>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.IpAddress).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(200);
                entity.Property(e => e.MdnsName).HasMaxLength(100);
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.LastHeartbeat).HasColumnType("timestamptz");
                // 移除IP地址唯一约束，因为IP可能会变动
                // entity.HasIndex(e => e.IpAddress).IsUnique();
            });

            // Material 配置
            modelBuilder.Entity<Material>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Sku).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Unit).HasMaxLength(50);
                entity.Property(e => e.Category).HasMaxLength(100);
                entity.HasIndex(e => e.Sku).IsUnique();
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
            });

            // Inventory 配置
            modelBuilder.Entity<Inventory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Quantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.BatchNumber).HasMaxLength(50);
                entity.Property(e => e.LpnCode).HasMaxLength(50);
                // 配置DateTime字段为timestamp with time zone类型
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ExpiryDate).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.StorageLocation)
                    .WithMany(s => s.Inventories)
                    .HasForeignKey(e => e.StorageLocationId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity
                    .HasOne(e => e.Material)
                    .WithMany(m => m.Inventories)
                    .HasForeignKey(e => e.MaterialId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // BarcodeRuleCategory 配置
            modelBuilder.Entity<BarcodeRuleCategory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CategoryCode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CategoryName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasIndex(e => e.CategoryCode).IsUnique();
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
            });

            // BarcodeParsingRule 配置
            modelBuilder.Entity<BarcodeParsingRule>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RuleName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.RegexPattern).IsRequired().HasColumnType("text");
                entity.Property(e => e.FieldMappings).IsRequired().HasColumnType("jsonb");
                entity.Property(e => e.ClientId).HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.Category)
                    .WithMany(c => c.BarcodeParsingRules)
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.Restrict);

                // 索引
                entity.HasIndex(e => e.CategoryId);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.ClientId);
            });

            // BarcodeValidationRule 配置
            modelBuilder.Entity<BarcodeValidationRule>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RuleName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Configuration).IsRequired().HasColumnType("jsonb");
                entity.Property(e => e.ClientId).HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");

                // 索引
                entity.HasIndex(e => e.RuleType);
                entity.HasIndex(e => e.ClientId);
            });

            // UniqueCodeRegistry 配置
            modelBuilder.Entity<UniqueCodeRegistry>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UniqueCode).IsRequired().HasMaxLength(100);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.Remarks).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.RegistrationDate).HasColumnType("timestamptz");
                entity.Property(e => e.LastOperationDate).HasColumnType("timestamptz");

                // 唯一约束
                entity.HasIndex(e => e.UniqueCode).IsUnique();

                // 外键关系
                entity
                    .HasOne(e => e.Material)
                    .WithMany()
                    .HasForeignKey(e => e.MaterialId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity
                    .HasOne(e => e.StorageLocation)
                    .WithMany()
                    .HasForeignKey(e => e.StorageLocationId)
                    .OnDelete(DeleteBehavior.SetNull);

                // 索引
                entity.HasIndex(e => e.MaterialId);
                entity.HasIndex(e => e.Status);
            });

            // BarcodeParsingLog 配置
            modelBuilder.Entity<BarcodeParsingLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OriginalBarcode).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ParsedResult).HasColumnType("jsonb");
                entity.Property(e => e.ErrorMessage).HasColumnType("text");
                entity.Property(e => e.UserId).HasMaxLength(450);
                entity.Property(e => e.ClientId).HasMaxLength(50);
                entity.Property(e => e.OperationType).HasMaxLength(50);
                entity.Property(e => e.StorageLocationCode).HasMaxLength(100);
                entity.Property(e => e.ParsedAt).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.MatchedRule)
                    .WithMany()
                    .HasForeignKey(e => e.MatchedRuleId)
                    .OnDelete(DeleteBehavior.SetNull);

                // 索引
                entity.HasIndex(e => e.ParsedAt);
                entity.HasIndex(e => e.IsSuccess);
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.ClientId);
            });

            // HierarchicalConfiguration 配置
            modelBuilder.Entity<HierarchicalConfiguration>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ClientId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TargetType).HasMaxLength(50);
                entity.Property(e => e.TargetId).HasMaxLength(100);
                entity.Property(e => e.ConfigKey).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ConfigValue).IsRequired().HasColumnType("jsonb");
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.EffectiveFrom).HasColumnType("timestamptz");
                entity.Property(e => e.EffectiveTo).HasColumnType("timestamptz");

                // 索引
                entity.HasIndex(e => new { e.ClientId, e.ConfigLevel });
                entity.HasIndex(e => new { e.TargetType, e.TargetId });
                entity.HasIndex(e => e.ConfigKey);
                entity.HasIndex(e => e.Priority);
            });

            // ConfigurationApplicationLog 配置
            modelBuilder.Entity<ConfigurationApplicationLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OperationId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ClientId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ZoneCode).HasMaxLength(20);
                entity.Property(e => e.ShelfCode).HasMaxLength(50);
                entity.Property(e => e.LocationCode).HasMaxLength(100);
                entity.Property(e => e.MaterialCategory).HasMaxLength(50);
                entity.Property(e => e.ConfigKey).IsRequired().HasMaxLength(100);
                entity.Property(e => e.AppliedConfigLevel).HasMaxLength(20);
                entity.Property(e => e.FinalConfigValue).IsRequired().HasColumnType("jsonb");
                entity.Property(e => e.ConfigResolutionPath).HasColumnType("text");
                entity.Property(e => e.UserId).HasMaxLength(100);
                entity.Property(e => e.AppliedAt).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.AppliedConfig)
                    .WithMany()
                    .HasForeignKey(e => e.AppliedConfigId)
                    .OnDelete(DeleteBehavior.SetNull);

                // 索引
                entity.HasIndex(e => e.OperationId);
                entity.HasIndex(e => e.AppliedAt);
            });

            // ConfigurationConflictLog 配置
            modelBuilder.Entity<ConfigurationConflictLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ClientId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TargetScope).HasColumnType("text");
                entity.Property(e => e.ConflictingConfigs).HasColumnType("jsonb");
                entity.Property(e => e.ResolutionStrategy).HasMaxLength(50);
                entity.Property(e => e.ResolvedValue).HasColumnType("jsonb");
                entity.Property(e => e.ResolvedBy).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");

                // 索引
                entity.HasIndex(e => e.ClientId);
                entity.HasIndex(e => e.ConflictType);
                entity.HasIndex(e => e.CreatedAt);
            });

            // InboundOrder 配置
            modelBuilder.Entity<InboundOrder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SupplierName).HasMaxLength(100);
                entity.Property(e => e.SupplierCode).HasMaxLength(50);
                entity.Property(e => e.Remarks).HasMaxLength(500);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.ProcessedBy).HasMaxLength(100);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ExpectedDate).HasColumnType("timestamptz");
                entity.Property(e => e.ActualDate).HasColumnType("timestamptz");

                // 索引
                entity.HasIndex(e => e.OrderNumber).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.SupplierCode);
                entity.HasIndex(e => e.CreatedAt);
            });

            // InboundOrderItem 配置
            modelBuilder.Entity<InboundOrderItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ExpectedQuantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.ActualQuantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.BatchNumber).HasMaxLength(50);
                entity.Property(e => e.LpnCode).HasMaxLength(50);
                entity.Property(e => e.Remarks).HasMaxLength(200);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ExpiryDate).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.InboundOrder)
                    .WithMany(o => o.Items)
                    .HasForeignKey(e => e.InboundOrderId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasOne(e => e.Material)
                    .WithMany()
                    .HasForeignKey(e => e.MaterialId)
                    .OnDelete(DeleteBehavior.Restrict);

                // 索引
                entity.HasIndex(e => e.InboundOrderId);
                entity.HasIndex(e => e.MaterialId);
            });

            // OutboundOrder 配置
            modelBuilder.Entity<OutboundOrder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CustomerName).HasMaxLength(100);
                entity.Property(e => e.CustomerCode).HasMaxLength(50);
                entity.Property(e => e.Remarks).HasMaxLength(500);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.ProcessedBy).HasMaxLength(100);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ExpectedDate).HasColumnType("timestamptz");
                entity.Property(e => e.ActualDate).HasColumnType("timestamptz");

                // 索引
                entity.HasIndex(e => e.OrderNumber).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.CustomerCode);
                entity.HasIndex(e => e.CreatedAt);
            });

            // OutboundOrderItem 配置
            modelBuilder.Entity<OutboundOrderItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RequiredQuantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.PickedQuantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.BatchNumber).HasMaxLength(50);
                entity.Property(e => e.LpnCode).HasMaxLength(50);
                entity.Property(e => e.Remarks).HasMaxLength(200);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.OutboundOrder)
                    .WithMany(o => o.Items)
                    .HasForeignKey(e => e.OutboundOrderId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasOne(e => e.Material)
                    .WithMany()
                    .HasForeignKey(e => e.MaterialId)
                    .OnDelete(DeleteBehavior.Restrict);

                // 索引
                entity.HasIndex(e => e.OutboundOrderId);
                entity.HasIndex(e => e.MaterialId);
            });

            // PickingTask 配置
            modelBuilder.Entity<PickingTask>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RequiredQuantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.PickedQuantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.AssignedTo).HasMaxLength(100);
                entity.Property(e => e.CompletedBy).HasMaxLength(100);
                entity.Property(e => e.Remarks).HasMaxLength(200);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.StartedAt).HasColumnType("timestamptz");
                entity.Property(e => e.CompletedAt).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.OutboundOrderItem)
                    .WithMany(o => o.PickingTasks)
                    .HasForeignKey(e => e.OutboundOrderItemId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity
                    .HasOne(e => e.StorageLocation)
                    .WithMany()
                    .HasForeignKey(e => e.StorageLocationId)
                    .OnDelete(DeleteBehavior.Restrict);

                // 索引
                entity.HasIndex(e => e.OutboundOrderItemId);
                entity.HasIndex(e => e.StorageLocationId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.AssignedTo);
            });

            // InventoryTransaction 配置
            modelBuilder.Entity<InventoryTransaction>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TransactionNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Quantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.BeforeQuantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.AfterQuantity).HasColumnType("decimal(18,3)");
                entity.Property(e => e.BatchNumber).HasMaxLength(50);
                entity.Property(e => e.LpnCode).HasMaxLength(50);
                entity.Property(e => e.UniqueCode).HasMaxLength(100);
                entity.Property(e => e.Reason).HasMaxLength(500);
                entity.Property(e => e.Remarks).HasMaxLength(200);
                entity.Property(e => e.OperatedBy).HasMaxLength(100);
                entity.Property(e => e.ApprovedBy).HasMaxLength(100);
                entity.Property(e => e.BarcodeData).HasMaxLength(500);
                entity.Property(e => e.ParsedBy).HasMaxLength(100);
                entity.Property(e => e.LedColor).HasMaxLength(20);
                entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ExpiryDate).HasColumnType("timestamptz");
                entity.Property(e => e.OperatedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ApprovedAt).HasColumnType("timestamptz");
                entity.Property(e => e.ParsedAt).HasColumnType("timestamptz");
                entity.Property(e => e.LedActivatedAt).HasColumnType("timestamptz");

                // 外键关系
                entity
                    .HasOne(e => e.StorageLocation)
                    .WithMany()
                    .HasForeignKey(e => e.StorageLocationId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity
                    .HasOne(e => e.TargetStorageLocation)
                    .WithMany()
                    .HasForeignKey(e => e.TargetStorageLocationId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity
                    .HasOne(e => e.Material)
                    .WithMany()
                    .HasForeignKey(e => e.MaterialId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity
                    .HasOne(e => e.InboundOrder)
                    .WithMany(o => o.Transactions)
                    .HasForeignKey(e => e.InboundOrderId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity
                    .HasOne(e => e.OutboundOrder)
                    .WithMany(o => o.Transactions)
                    .HasForeignKey(e => e.OutboundOrderId)
                    .OnDelete(DeleteBehavior.SetNull);

                // 索引
                entity.HasIndex(e => e.TransactionNumber).IsUnique();
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.StorageLocationId);
                entity.HasIndex(e => e.MaterialId);
                entity.HasIndex(e => e.UniqueCode);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.OperatedBy);
            });
        }
    }
}
