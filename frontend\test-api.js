// 简单的API测试脚本
// 在浏览器控制台中运行以测试API功能

console.log('=== WMS-VGL API 测试 ===');

// 测试仓库API
async function testWarehouseAPI() {
  try {
    console.log('测试仓库API...');
    
    // 测试获取仓库列表
    const response = await fetch('http://localhost:8080/api/warehouses');
    if (response.ok) {
      const warehouses = await response.json();
      console.log('✅ 获取仓库列表成功:', warehouses);
    } else {
      console.log('❌ 获取仓库列表失败:', response.status);
    }
    
    // 测试创建仓库
    const createResponse = await fetch('http://localhost:8080/api/warehouses', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: 'TEST001',
        name: '测试仓库',
        warehouseType: 'distribution',
        address: '测试地址',
        contactPerson: '测试联系人',
        contactPhone: '13800138000',
        status: 'Active'
      })
    });
    
    if (createResponse.ok) {
      const newWarehouse = await createResponse.json();
      console.log('✅ 创建仓库成功:', newWarehouse);
    } else {
      console.log('❌ 创建仓库失败:', createResponse.status);
    }
    
  } catch (error) {
    console.log('❌ API测试失败 - 后端服务可能未启动:', error.message);
    console.log('💡 这是正常的，前端会使用模拟数据');
  }
}

// 运行测试
testWarehouseAPI();
