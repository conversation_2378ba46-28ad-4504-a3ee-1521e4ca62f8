using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 条码解析日志
    /// </summary>
    public class BarcodeParsingLog
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 原始条码
        /// </summary>
        [Required]
        [StringLength(500)]
        public string OriginalBarcode { get; set; } = string.Empty;

        /// <summary>
        /// 解析结果（JSON格式）
        /// </summary>
        [Column(TypeName = "jsonb")]
        public string? ParsedResult { get; set; }

        /// <summary>
        /// 匹配的规则ID
        /// </summary>
        public int? MatchedRuleId { get; set; }

        /// <summary>
        /// 匹配的规则实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual BarcodeParsingRule? MatchedRule { get; set; }

        /// <summary>
        /// 解析是否成功
        /// </summary>
        public bool IsSuccess { get; set; } = false;

        /// <summary>
        /// 错误信息
        /// </summary>
        [Column(TypeName = "text")]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 解析时间
        /// </summary>
        public DateTime ParsedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 用户ID
        /// </summary>
        [StringLength(450)]
        public string? UserId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        [StringLength(50)]
        public string? ClientId { get; set; }

        /// <summary>
        /// 操作类型（INBOUND, OUTBOUND, INVENTORY等）
        /// </summary>
        [StringLength(50)]
        public string? OperationType { get; set; }

        /// <summary>
        /// 储位代码（如果相关）
        /// </summary>
        [StringLength(100)]
        public string? StorageLocationCode { get; set; }

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public int ProcessingTimeMs { get; set; }
    }
}
