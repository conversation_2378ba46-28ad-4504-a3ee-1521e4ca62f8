using WMS.API.Models;

namespace WMS.API.DTOs.Barcode
{
    /// <summary>
    /// 条码解析结果
    /// </summary>
    public class BarcodeParsingResult
    {
        /// <summary>
        /// 原始条码
        /// </summary>
        public string OriginalBarcode { get; set; } = string.Empty;

        /// <summary>
        /// 条码分类
        /// </summary>
        public BarcodeRuleCategory? Category { get; set; }

        /// <summary>
        /// 物料SKU
        /// </summary>
        public string? MaterialSku { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public string? BatchNumber { get; set; }

        /// <summary>
        /// LPN代码
        /// </summary>
        public string? LpnCode { get; set; }

        /// <summary>
        /// 唯一码
        /// </summary>
        public string? UniqueCode { get; set; }

        /// <summary>
        /// 工单号
        /// </summary>
        public string? WorkOrderNumber { get; set; }

        /// <summary>
        /// 储位代码
        /// </summary>
        public string? StorageLocationCode { get; set; }

        /// <summary>
        /// 扩展字段
        /// </summary>
        public Dictionary<string, object> ExtendedFields { get; set; } = new();

        /// <summary>
        /// 匹配的解析规则
        /// </summary>
        public BarcodeParsingRule? MatchedRule { get; set; }

        /// <summary>
        /// 是否解析成功
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证错误信息
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new();

        /// <summary>
        /// 解析时间
        /// </summary>
        public DateTime ParsedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public int ProcessingTimeMs { get; set; }
    }

    /// <summary>
    /// 批量条码解析结果
    /// </summary>
    public class BatchBarcodeParsingResult
    {
        /// <summary>
        /// 所有解析结果
        /// </summary>
        public List<BarcodeParsingResult> Results { get; set; } = new();

        /// <summary>
        /// 总条码数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功解析数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败解析数量
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public double SuccessRate => TotalCount > 0 ? (double)SuccessCount / TotalCount * 100 : 0;

        /// <summary>
        /// 总处理时间（毫秒）
        /// </summary>
        public int TotalProcessingTimeMs { get; set; }

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageProcessingTimeMs => TotalCount > 0 ? (double)TotalProcessingTimeMs / TotalCount : 0;

        /// <summary>
        /// 处理开始时间
        /// </summary>
        public DateTime StartedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 处理完成时间
        /// </summary>
        public DateTime CompletedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 摘要信息
        /// </summary>
        public string Summary => $"批量解析完成：共{TotalCount}个条码，成功{SuccessCount}个，失败{FailureCount}个，成功率{SuccessRate:F1}%";
    }
}