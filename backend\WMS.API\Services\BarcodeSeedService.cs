using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Services
{
    /// <summary>
    /// 条码解析数据种子服务
    /// </summary>
    public class BarcodeSeedService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<BarcodeSeedService> _logger;

        public BarcodeSeedService(ApplicationDbContext context, ILogger<BarcodeSeedService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 初始化条码解析基础数据
        /// </summary>
        public async Task SeedBarcodeDataAsync()
        {
            try
            {
                await SeedBarcodeRuleCategoriesAsync();
                await SeedDefaultParsingRulesAsync();
                await SeedDefaultValidationRulesAsync();
                await SeedDefaultHierarchicalConfigurationsAsync();

                _logger.LogInformation("条码解析基础数据初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "条码解析基础数据初始化失败");
                throw;
            }
        }

        private async Task SeedBarcodeRuleCategoriesAsync()
        {
            var categories = new[]
            {
                new BarcodeRuleCategory
                {
                    CategoryCode = "MATERIAL",
                    CategoryName = "物料条码",
                    Description = "物料相关的条码，包含SKU、数量、批次等信息",
                },
                new BarcodeRuleCategory
                {
                    CategoryCode = "STORAGE_LOCATION",
                    CategoryName = "储位条码",
                    Description = "储位相关的条码，包含储位编码信息",
                },
                new BarcodeRuleCategory
                {
                    CategoryCode = "WORK_ORDER",
                    CategoryName = "工单条码",
                    Description = "工单相关的条码，包含单据号等信息",
                },
                new BarcodeRuleCategory
                {
                    CategoryCode = "PACKAGE",
                    CategoryName = "包装条码",
                    Description = "包装相关的条码，包含包装信息和唯一码",
                },
            };

            foreach (var category in categories)
            {
                var existingCategory = await _context.BarcodeRuleCategories.FirstOrDefaultAsync(c =>
                    c.CategoryCode == category.CategoryCode
                );

                if (existingCategory == null)
                {
                    _context.BarcodeRuleCategories.Add(category);
                    _logger.LogInformation(
                        "添加条码规则分类: {CategoryCode}",
                        category.CategoryCode
                    );
                }
            }

            await _context.SaveChangesAsync();
        }

        private async Task SeedDefaultParsingRulesAsync()
        {
            var materialCategory = await _context.BarcodeRuleCategories.FirstOrDefaultAsync(c =>
                c.CategoryCode == "MATERIAL"
            );

            var storageLocationCategory = await _context.BarcodeRuleCategories.FirstOrDefaultAsync(
                c => c.CategoryCode == "STORAGE_LOCATION"
            );

            if (materialCategory == null || storageLocationCategory == null)
            {
                _logger.LogWarning("条码规则分类未找到，跳过解析规则初始化");
                return;
            }

            var rules = new[]
            {
                new BarcodeParsingRule
                {
                    CategoryId = materialCategory.Id,
                    RuleName = "标准物料条码",
                    RegexPattern =
                        @"^MAT([A-Z0-9]{6})QTY(\d+)BATCH([A-Z0-9]{8})UNIQUE([A-Z0-9]{10})$",
                    FieldMappings = JsonSerializer.Serialize(
                        new Dictionary<string, FieldMapping>
                        {
                            ["materialSku"] = new FieldMapping
                            {
                                Group = 1,
                                Required = true,
                                Description = "物料SKU",
                            },
                            ["quantity"] = new FieldMapping
                            {
                                Group = 2,
                                Required = false,
                                Type = "decimal",
                                DefaultValue = 1.0,
                                Description = "数量",
                            },
                            ["batchNumber"] = new FieldMapping
                            {
                                Group = 3,
                                Required = false,
                                Description = "批次号",
                            },
                            ["uniqueCode"] = new FieldMapping
                            {
                                Group = 4,
                                Required = false,
                                Description = "唯一码",
                            },
                        }
                    ),
                    Priority = 10,
                    Description = "格式：MAT{SKU}QTY{数量}BATCH{批次}UNIQUE{唯一码}",
                },
                new BarcodeParsingRule
                {
                    CategoryId = materialCategory.Id,
                    RuleName = "简化物料条码",
                    RegexPattern = @"^([A-Z0-9]{6,20})$",
                    FieldMappings = JsonSerializer.Serialize(
                        new Dictionary<string, FieldMapping>
                        {
                            ["materialSku"] = new FieldMapping
                            {
                                Group = 1,
                                Required = true,
                                Description = "物料SKU",
                            },
                        }
                    ),
                    Priority = 50,
                    Description = "仅包含物料SKU的简化条码",
                },
                new BarcodeParsingRule
                {
                    CategoryId = storageLocationCategory.Id,
                    RuleName = "标准储位条码",
                    RegexPattern = @"^LOC([A-Z]\d{2})-([A-Z]\d{2})-([A-Z]\d{2})$",
                    FieldMappings = JsonSerializer.Serialize(
                        new Dictionary<string, FieldMapping>
                        {
                            ["storageLocationCode"] = new FieldMapping
                            {
                                Group = 0,
                                Required = true,
                                Transform = "removePrefix:LOC",
                                Description = "储位编码",
                            },
                            ["zone"] = new FieldMapping
                            {
                                Group = 1,
                                Required = false,
                                Description = "区域",
                            },
                            ["aisle"] = new FieldMapping
                            {
                                Group = 2,
                                Required = false,
                                Description = "巷道",
                            },
                            ["shelf"] = new FieldMapping
                            {
                                Group = 3,
                                Required = false,
                                Description = "货架",
                            },
                        }
                    ),
                    Priority = 10,
                    Description = "格式：LOC{区域}-{巷道}-{货架}",
                },
                new BarcodeParsingRule
                {
                    CategoryId = storageLocationCategory.Id,
                    RuleName = "简化储位条码",
                    RegexPattern = @"^([A-Z]\d{2}-[A-Z]\d{2}-[A-Z]\d{2})$",
                    FieldMappings = JsonSerializer.Serialize(
                        new Dictionary<string, FieldMapping>
                        {
                            ["storageLocationCode"] = new FieldMapping
                            {
                                Group = 1,
                                Required = true,
                                Description = "储位编码",
                            },
                        }
                    ),
                    Priority = 20,
                    Description = "直接的储位编码格式：{区域}-{巷道}-{货架}",
                },
            };

            foreach (var rule in rules)
            {
                var existingRule = await _context.BarcodeParsingRules.FirstOrDefaultAsync(r =>
                    r.RuleName == rule.RuleName
                );

                if (existingRule == null)
                {
                    _context.BarcodeParsingRules.Add(rule);
                    _logger.LogInformation("添加条码解析规则: {RuleName}", rule.RuleName);
                }
            }

            await _context.SaveChangesAsync();
        }

        private async Task SeedDefaultValidationRulesAsync()
        {
            var validationRules = new[]
            {
                new BarcodeValidationRule
                {
                    RuleName = "默认唯一码管控",
                    RuleType = BarcodeValidationRuleType.UNIQUE_CODE_CONTROL,
                    Configuration = JsonSerializer.Serialize(
                        new
                        {
                            enabled = true,
                            strictMode = false,
                            allowDuplicateOutbound = false,
                            errorMessage = "唯一码已存在，不允许重复入库",
                        }
                    ),
                    Description = "默认的唯一码管控规则",
                },
                new BarcodeValidationRule
                {
                    RuleName = "默认储位管理",
                    RuleType = BarcodeValidationRuleType.SINGLE_SKU_PER_LOCATION,
                    Configuration = JsonSerializer.Serialize(
                        new
                        {
                            enabled = false,
                            allowMixedBatches = true,
                            maxSkuPerLocation = 999,
                            errorMessage = "此储位已存在其他物料，不允许混放",
                        }
                    ),
                    Description = "默认允许混放的储位管理规则",
                },
                new BarcodeValidationRule
                {
                    RuleName = "默认数量验证",
                    RuleType = BarcodeValidationRuleType.QUANTITY_VALIDATION,
                    Configuration = JsonSerializer.Serialize(
                        new
                        {
                            minQuantity = 0.001,
                            maxQuantity = 9999.999,
                            varianceTolerance = 0.05,
                            errorMessage = "数量必须在有效范围内",
                        }
                    ),
                    Description = "默认的数量验证规则",
                },
            };

            foreach (var rule in validationRules)
            {
                var existingRule = await _context.BarcodeValidationRules.FirstOrDefaultAsync(r =>
                    r.RuleName == rule.RuleName
                );

                if (existingRule == null)
                {
                    _context.BarcodeValidationRules.Add(rule);
                    _logger.LogInformation("添加条码验证规则: {RuleName}", rule.RuleName);
                }
            }

            await _context.SaveChangesAsync();
        }

        private async Task SeedDefaultHierarchicalConfigurationsAsync()
        {
            var defaultConfigurations = new[]
            {
                new HierarchicalConfiguration
                {
                    ClientId = "DEFAULT_CLIENT",
                    ConfigLevel = ConfigLevel.CLIENT,
                    ConfigKey = "uniqueCodeControl",
                    ConfigValue = JsonSerializer.Serialize(
                        new
                        {
                            enabled = false,
                            strictMode = false,
                            allowDuplicateOutbound = false,
                            materialTypeExceptions = new string[] { },
                        }
                    ),
                    Priority = 50,
                    Description = "默认客户的唯一码管控配置",
                },
                new HierarchicalConfiguration
                {
                    ClientId = "DEFAULT_CLIENT",
                    ConfigLevel = ConfigLevel.CLIENT,
                    ConfigKey = "storageLocationPolicy",
                    ConfigValue = JsonSerializer.Serialize(
                        new
                        {
                            singleSkuPerLocation = false,
                            allowMixedBatches = true,
                            maxSkuPerLocation = 999,
                            capacityControlLevel = "NORMAL",
                        }
                    ),
                    Priority = 50,
                    Description = "默认客户的储位管理策略",
                },
                new HierarchicalConfiguration
                {
                    ClientId = "DEFAULT_CLIENT",
                    ConfigLevel = ConfigLevel.CLIENT,
                    ConfigKey = "inventoryRules",
                    ConfigValue = JsonSerializer.Serialize(
                        new
                        {
                            quantityVarianceTolerance = 0.05,
                            batchManagementLevel = "NORMAL",
                            expiryDateRequired = false,
                            negativeInventoryAllowed = false,
                        }
                    ),
                    Priority = 50,
                    Description = "默认客户的库存管理规则",
                },
                new HierarchicalConfiguration
                {
                    ClientId = "DEFAULT_CLIENT",
                    ConfigLevel = ConfigLevel.CLIENT,
                    ConfigKey = "ledIndicator",
                    ConfigValue = JsonSerializer.Serialize(
                        new
                        {
                            colorScheme = "STANDARD",
                            blinkPattern = "STANDARD",
                            enableSoundAlert = false,
                            brightness = 80,
                        }
                    ),
                    Priority = 50,
                    Description = "默认客户的LED指示配置",
                },
            };

            foreach (var config in defaultConfigurations)
            {
                var existingConfig = await _context.HierarchicalConfigurations.FirstOrDefaultAsync(
                    c =>
                        c.ClientId == config.ClientId
                        && c.ConfigKey == config.ConfigKey
                        && c.ConfigLevel == config.ConfigLevel
                );

                if (existingConfig == null)
                {
                    _context.HierarchicalConfigurations.Add(config);
                    _logger.LogInformation("添加默认配置: {ConfigKey}", config.ConfigKey);
                }
            }

            await _context.SaveChangesAsync();
        }
    }
}
