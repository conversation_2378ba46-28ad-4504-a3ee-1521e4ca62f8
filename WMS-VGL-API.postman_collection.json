{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "WMS-VGL API Collection (完整版 v5.0)", "description": "Complete API collection for WMS-VGL (Visual Guided Logistics) Warehouse Management System v5.0\n\n## 🚀 v5.0 重大更新 - 完整出入库系统\n全面支持WMS核心业务流程：\n```\n📦 入库管理 - 订单创建、条码入库、库存更新\n📋 出库管理 - 订单创建、拣选作业、状态跟踪\n🎯 拣选任务 - 任务生成、条码拣选、LED引导\n💾 库存事务 - 事务记录、ACID保证、审计跟踪\n📊 物料管理 - 基础数据、SKU管理、规格配置\n📈 库存查询 - 实时库存、多维查询、状态统计\n```\n\n## 🏗️ 层级架构系统\n采用四级层级结构，支持复杂仓库管理：\n```\n🏭 Warehouse (仓库) - 顶级容器，支持多仓库管理\n└── 🏢 Zone (区域) - 功能分区：常温/冷藏/冷冻/危险品\n    └── 📚 Shelf (货架) - 物理货架：标准/重型/悬臂/冷藏专用\n        └── 📦 StorageLocation (储位) - 具体存放位置，连接LED控制\n            ├── ⚡ LocationCapability (储位能力) - 温控/危险品/贵重品等\n            └── 🏷️ LocationClassification (储位分类) - 行业/材料/安全等级\n```\n\n## 🚀 快速开始指南\n\n### 基础配置\n1. **导入Collection**: 将此文件导入到Postman\n2. **环境配置**: 设置Environment变量\n   - `baseUrl`: http://localhost:5000 (本地) 或 http://************:5000 (WSL)\n   - `baseUrlWSL`: Pre-configured WSL gateway URL\n   - `accessToken`: 认证后自动设置\n   - 层级ID变量: `warehouseId`, `zoneId`, `shelfId`, `storageLocationId`\n   - 业务ID变量: `materialId`, `inboundOrderId`, `outboundOrderId`\n\n### 快速测试流程\n1. **认证登录**: 运行 \"01 - 用户认证 > 管理员登录\"\n2. **创建测试物料**: 运行 \"15 - 物料管理 > 创建测试物料\"\n3. **入库测试**:\n   - 创建入库订单 → 条码入库处理 → 验证库存\n4. **出库测试**:\n   - 创建出库订单 → 开始拣选作业 → 条码拣选 → 完成订单\n5. **验证结果**: 查看库存事务和库存变化\n\n## 🔐 默认管理员账户\n- **Email**: <EMAIL>\n- **Password**: Admin@123456\n- **权限**: SuperAdmin (完整系统权限)\n\n## 🌟 v5.0 新功能特性\n\n### 🎯 核心业务流程\n- ✅ **完整入库流程**: 订单管理 → 条码入库 → 库存更新 → 事务记录\n- ✅ **完整出库流程**: 订单管理 → 拣选任务 → 条码拣选 → 库存扣减\n- ✅ **智能拣选系统**: 自动任务生成、FIFO策略、LED视觉引导\n- ✅ **ACID事务管理**: 库存一致性、回滚支持、并发控制\n- ✅ **条码集成**: 统一解析引擎、多格式支持、验证规则\n\n### 📊 数据管理\n- 🏷️ **物料管理**: SKU编码、规格管理、分类体系\n- 📦 **库存管理**: 实时库存、批次追踪、状态管理\n- 📋 **订单管理**: 入库/出库订单、状态跟踪、审计日志\n- 🎯 **任务管理**: 拣选任务、分配调度、进度跟踪\n\n### 🔧 系统功能\n- 🌡️ **温度控制**: 精确温度监控和报警\n- ⚠️ **危险品管理**: 符合安全法规的危险品存储\n- 💎 **贵重品保护**: 高安全级别存储管理\n- 🧪 **生物样品**: 医疗级别的生物样品存储\n- 🏗️ **重型货物**: 承重能力验证和管理\n\n### 🏭 行业解决方案\n- 🏥 **医疗行业**: 温度监控、合规管理、审计跟踪\n- 🛒 **电商行业**: 拣货优化、波次处理、季节调整\n- 🏭 **制造业**: FIFO管理、批次追踪、质量控制\n- 🍎 **食品行业**: 保质期管理、食品安全、可追溯性\n\n## 📋 API分组说明\n- **01-04**: 基础系统功能 (认证、用户、储位、ESP32)\n- **05-07**: 层级架构管理 (仓库、区域、货架)\n- **08**: 能力与分类系统 (储位智能化)\n- **09**: 配置继承管理 (层级配置)\n- **10**: 条码解析系统 (统一解析引擎)\n- **11-16**: 核心业务流程 (入库、出库、拣选、事务、物料、库存)\n- **99**: 测试诊断工具\n\n## ⚡ 测试场景示例\n\n### 🔄 完整业务流程测试\n1. **准备阶段**: 登录 → 创建物料 → 验证储位\n2. **入库流程**: 创建入库订单 → 条码入库 → 验证库存增加\n3. **出库流程**: 创建出库订单 → 生成拣选任务 → 条码拣选 → 验证库存减少\n4. **验证阶段**: 查看事务记录 → 检查数据一致性\n\n### 🎯 高级功能测试\n- **批量操作**: 批量入库、批量拣选、批量事务\n- **错误处理**: 库存不足、条码错误、订单取消\n- **状态管理**: 订单状态流转、任务生命周期\n- **LED集成**: 视觉引导、位置指示、状态显示\n\n## 🔗 相关文档\n- **测试文档**: `/Dev Resources/WMS出入库功能测试文档.md`\n- **业务设计**: `/Dev Resources/WMS条码解析业务流程设计.md`\n- **系统说明**: `/Dev Resources/系统需求说明文档.md`", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "01 - 用户认证", "item": [{"name": "管理员登录", "event": [{"listen": "test", "script": {"exec": ["// 自动保存AccessToken到环境变量", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.isSuccess && response.accessToken) {", "        pm.environment.set('accessToken', response.accessToken);", "        pm.environment.set('refreshToken', response.refreshToken);", "        console.log('✅ Token已保存到环境变量');", "    }", "} else {", "    console.log('❌ 登录失败');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin@123456\",\n  \"rememberMe\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/login", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "login"]}, "description": "使用默认管理员账户登录获取JWT Token"}, "response": []}, {"name": "获取当前用户信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "profile"]}, "description": "获取当前登录用户的详细信息"}, "response": []}, {"name": "刷新Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/refresh-token", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "refresh-token"]}, "description": "使用RefreshToken获取新的AccessToken"}, "response": []}, {"name": "用户注销", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "logout"]}, "description": "注销当前用户并撤销RefreshToken"}, "response": []}], "description": "用户认证相关API，包括登录、注销、Token刷新等功能"}, {"name": "02 - 用户管理", "item": [{"name": "获取用户列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Users", "host": ["{{baseUrl}}"], "path": ["api", "Users"]}, "description": "获取系统中所有用户的列表（需要Admin权限）"}, "response": []}, {"name": "创建新用户", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"fullName\": \"测试用户\",\n  \"employeeId\": \"TEST001\",\n  \"department\": \"测试部门\",\n  \"position\": \"测试员\",\n  \"password\": \"Test@123456\",\n  \"roles\": [\"Operator\"],\n  \"remarks\": \"这是一个测试用户账户\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Users", "host": ["{{baseUrl}}"], "path": ["api", "Users"]}, "description": "创建新的系统用户（需要Admin权限）"}, "response": []}, {"name": "获取指定用户信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Users/<USER>", "host": ["{{baseUrl}}"], "path": ["api", "Users", "{{userId}}"]}, "description": "根据用户ID获取用户详细信息"}, "response": []}, {"name": "更新用户信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"fullName\": \"测试用户（已更新）\",\n  \"employeeId\": \"TEST001\",\n  \"department\": \"测试部门\",\n  \"position\": \"高级测试员\",\n  \"isActive\": true,\n  \"roles\": [\"WarehouseManager\"],\n  \"remarks\": \"用户信息已更新\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Users/<USER>", "host": ["{{baseUrl}}"], "path": ["api", "Users", "{{userId}}"]}, "description": "更新指定用户的信息"}, "response": []}, {"name": "重置用户密码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"newPassword\": \"NewPassword@123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Users/<USER>/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "Users", "{{userId}}", "reset-password"]}, "description": "重置指定用户的密码"}, "response": []}, {"name": "获取系统角色列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Users/<USER>", "host": ["{{baseUrl}}"], "path": ["api", "Users", "roles"]}, "description": "获取系统中所有可用的角色列表"}, "response": []}], "description": "用户管理相关API，包括用户的增删改查和角色管理"}, {"name": "03 - 储位管理", "item": [{"name": "获取储位列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations"]}, "description": "获取所有储位列表，包含关联的控制器和库存信息"}, "response": []}, {"name": "获取可用储位列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/available", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "available"]}, "description": "获取状态为可用的储位列表"}, "response": []}, {"name": "根据ID获取储位信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}"]}, "description": "根据储位ID获取详细信息"}, "response": []}, {"name": "根据储位编码获取信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/by-code/A01-01-01", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "by-code", "A01-01-01"]}, "description": "根据储位编码获取储位信息"}, "response": []}, {"name": "创建新储位", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"B01-01-01\",\n  \"description\": \"Zone B, Aisle 01, She<PERSON> 01, Level 01\",\n  \"esP32ControllerId\": 1,\n  \"startLedPosition\": 30,\n  \"endLedPosition\": 39,\n  \"ledChannel\": 0,\n  \"status\": 0,\n  \"zone\": \"B\",\n  \"aisle\": \"01\",\n  \"shelf\": \"01\",\n  \"level\": \"01\"\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations"]}, "description": "创建新的储位"}, "response": []}, {"name": "更新储位信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": {{storageLocationId}},\n  \"code\": \"B01-01-01\",\n  \"description\": \"Zone B, Aisle 01, Shelf 01, Level 01 (Updated)\",\n  \"esP32ControllerId\": 1,\n  \"startLedPosition\": 30,\n  \"endLedPosition\": 39,\n  \"ledChannel\": 0,\n  \"status\": 0,\n  \"zone\": \"B\",\n  \"aisle\": \"01\",\n  \"shelf\": \"01\",\n  \"level\": \"01\"\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}"]}, "description": "更新指定储位的信息"}, "response": []}, {"name": "测试储位LED灯", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"color\": \"red\",\n  \"brightness\": 255\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}/test-light", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}", "test-light"]}, "description": "测试指定储位的LED灯光显示"}, "response": []}, {"name": "关闭储位LED灯", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}/turn-off-light", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}", "turn-off-light"]}, "description": "关闭指定储位的LED灯光"}, "response": []}, {"name": "下载储位导入模板", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/template", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "template"]}, "description": "下载储位批量导入的Excel模板文件"}, "response": []}, {"name": "批量导入储位", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "选择要导入的Excel文件(.xlsx格式)"}]}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/batch-import", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "batch-import"]}, "description": "批量导入储位Excel文件，支持同时创建多个储位"}, "response": []}, {"name": "根据货架ID获取储位列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/by-shelf/{{shelfId}}", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "by-shelf", "{{shelfId}}"]}, "description": "获取指定货架下的所有储位"}, "response": []}, {"name": "为储位添加能力", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"capabilityType\": 1,\n  \"capabilityLevel\": 3,\n  \"capabilityName\": \"温度控制\",\n  \"description\": \"精确温度控制，适合生鲜食品存储\",\n  \"parameters\": {\n    \"targetTemperature\": 4,\n    \"tolerance\": 1,\n    \"unit\": \"celsius\"\n  },\n  \"isEnabled\": true,\n  \"priority\": 1,\n  \"validationRules\": {\n    \"temperatureRange\": {\"min\": 2, \"max\": 8}\n  },\n  \"certifications\": {\n    \"haccp\": true,\n    \"iso22000\": true\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}/capabilities", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}", "capabilities"]}, "description": "为指定储位添加特殊能力（如温度控制、危险品存储等）"}, "response": []}, {"name": "获取储位能力列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}/capabilities", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}", "capabilities"]}, "description": "获取指定储位的所有能力列表"}, "response": []}, {"name": "根据能力类型查询储位", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/by-capability/1?warehouseId={{warehouseId}}", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "by-capability", "1"], "query": [{"key": "warehouseId", "value": "{{warehouseId}}", "description": "可选：仓库ID过滤"}]}, "description": "查询具有指定能力类型的储位"}, "response": []}, {"name": "为储位添加分类", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"dimension\": 1,\n  \"category\": \"Industry\",\n  \"value\": \"Food\",\n  \"displayName\": \"食品行业\",\n  \"description\": \"适用于食品存储的储位\",\n  \"tags\": [\"food-grade\", \"haccp-compliant\"],\n  \"properties\": {\n    \"temperatureControlled\": true,\n    \"sanitationRequired\": true\n  },\n  \"priority\": 1,\n  \"isEnabled\": true,\n  \"businessRules\": {\n    \"autoAssignment\": true,\n    \"materialTypes\": [\"fresh\", \"frozen\", \"dry_goods\"]\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}/classifications", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}", "classifications"]}, "description": "为指定储位添加业务分类"}, "response": []}, {"name": "获取储位分类列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}/classifications", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}", "classifications"]}, "description": "获取指定储位的所有分类列表"}, "response": []}, {"name": "根据分类查询储位", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/by-classification/Industry/Food", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "by-classification", "Industry", "Food"]}, "description": "查询具有指定分类的储位"}, "response": []}, {"name": "智能储位推荐", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"materialType\": \"Electronics\",\n  \"dimensions\": {\n    \"length\": 10,\n    \"width\": 8,\n    \"height\": 5\n  },\n  \"weight\": 2.5,\n  \"temperatureRequirement\": \"ambient\",\n  \"hazardous\": false,\n  \"fragile\": false,\n  \"securityLevel\": \"standard\",\n  \"preferredZone\": \"A-ZONE\",\n  \"specialRequirements\": [\"anti-static\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/recommend", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "recommend"]}, "description": "基于材料特性和存储要求智能推荐最合适的储位"}, "response": []}], "description": "储位管理相关API，包括储位的增删改查和LED灯光控制"}, {"name": "04 - ESP32控制器管理", "item": [{"name": "获取控制器列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/ESP32Controllers", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers"]}, "description": "获取所有ESP32控制器列表"}, "response": []}, {"name": "获取指定控制器信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/{{controllerId}}", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "{{controllerId}}"]}, "description": "根据控制器ID获取详细信息"}, "response": []}, {"name": "创建新控制器", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"测试控制器\",\n  \"ipAddress\": \"*************\",\n  \"port\": 80,\n  \"description\": \"测试用ESP32控制器\",\n  \"mdnsName\": \"test-controller\",\n  \"isOnline\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/ESP32Controllers", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers"]}, "description": "创建新的ESP32控制器"}, "response": []}, {"name": "更新控制器信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": {{controllerId}},\n  \"name\": \"测试控制器（已更新）\",\n  \"ipAddress\": \"*************\",\n  \"port\": 80,\n  \"description\": \"测试用ESP32控制器（已更新）\",\n  \"mdnsName\": \"test-controller-updated\"\n}"}, "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/{{controllerId}}", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "{{controllerId}}"]}, "description": "更新指定控制器的信息"}, "response": []}, {"name": "测试控制器连接", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/{{controllerId}}/test-connection", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "{{controllerId}}", "test-connection"]}, "description": "测试指定控制器的网络连接状态"}, "response": []}, {"name": "获取设备信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/{{controllerId}}/device-info", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "{{controllerId}}", "device-info"]}, "description": "获取ESP32设备的详细信息"}, "response": []}, {"name": "控制LED灯条", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"channel\": 0,\n  \"startPosition\": 0,\n  \"endPosition\": 10,\n  \"color\": \"blue\",\n  \"brightness\": 200\n}"}, "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/{{controllerId}}/set-light", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "{{controllerId}}", "set-light"]}, "description": "控制指定控制器的LED灯条显示"}, "response": []}, {"name": "关闭LED灯条", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"channel\": 0\n}"}, "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/{{controllerId}}/turn-off-lights", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "{{controllerId}}", "turn-off-lights"]}, "description": "关闭指定控制器的LED灯条"}, "response": []}, {"name": "控制大指示灯", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"indicatorIndex\": 1,\n  \"turnOn\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/{{controllerId}}/control-big-led", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "{{controllerId}}", "control-big-led"]}, "description": "控制指定控制器的大指示灯"}, "response": []}, {"name": "通过货架号控制指示灯", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shelfNumber\": \"01\",\n  \"indicatorIndex\": 1,\n  \"turnOn\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/control-shelf-indicator", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "control-shelf-indicator"]}, "description": "通过货架编号控制对应的大指示灯"}, "response": []}, {"name": "所有控制器健康检查", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/ESP32Controllers/health-check", "host": ["{{baseUrl}}"], "path": ["api", "ESP32Controllers", "health-check"]}, "description": "对所有ESP32控制器进行健康检查"}, "response": []}], "description": "ESP32控制器管理相关API，包括控制器的增删改查和LED灯光控制"}, {"name": "05 - 仓库管理 (层级架构)", "item": [{"name": "获取仓库列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Warehouses", "host": ["{{baseUrl}}"], "path": ["api", "Warehouses"]}, "description": "获取所有仓库列表，包含基本信息和统计数据"}, "response": []}, {"name": "创建新仓库", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"WH001\",\n  \"name\": \"北京主仓库\",\n  \"description\": \"北京地区主要配送仓库\",\n  \"address\": \"北京市朝阳区工业园区123号\",\n  \"warehouseType\": \"配送中心\",\n  \"status\": 0,\n  \"totalArea\": 5000.0,\n  \"totalVolume\": 15000.0,\n  \"contactPerson\": \"李经理\",\n  \"contactPhone\": \"13800138000\",\n  \"configuration\": \"{\\\"operatingHours\\\":\\\"06:00-22:00\\\",\\\"temperatureMonitoring\\\":true,\\\"securityLevel\\\":2}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Warehouses", "host": ["{{baseUrl}}"], "path": ["api", "Warehouses"]}, "description": "创建新的仓库，包含完整的配置信息"}, "response": []}, {"name": "获取仓库详情", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Warehouses/{{warehouseId}}", "host": ["{{baseUrl}}"], "path": ["api", "Warehouses", "{{warehouseId}}"]}, "description": "根据仓库ID获取详细信息"}, "response": []}, {"name": "获取仓库统计信息", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Warehouses/{{warehouseId}}/statistics", "host": ["{{baseUrl}}"], "path": ["api", "Warehouses", "{{warehouseId}}", "statistics"]}, "description": "获取仓库的区域、货架、储位统计信息"}, "response": []}], "description": "仓库管理相关API，支持多仓库、层级配置和统计分析"}, {"name": "06 - 区域管理 (层级架构)", "item": [{"name": "获取区域列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Zones", "host": ["{{baseUrl}}"], "path": ["api", "Zones"]}, "description": "获取所有区域列表"}, "response": []}, {"name": "创建新区域", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"ZONE-A\",\n  \"name\": \"A区常温区域\",\n  \"description\": \"常温货物存储区域\",\n  \"warehouseId\": {{warehouseId}},\n  \"zoneType\": 0,\n  \"status\": 0,\n  \"area\": 1000.0,\n  \"volume\": 3000.0,\n  \"maxWeight\": 50000.0,\n  \"temperatureRange\": \"{\\\"min\\\":15,\\\"max\\\":25,\\\"unit\\\":\\\"celsius\\\"}\",\n  \"securityLevel\": 2,\n  \"configuration\": \"{\\\"temperatureAlerts\\\":false,\\\"continuousMonitoring\\\":true,\\\"accessControl\\\":true}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Zones", "host": ["{{baseUrl}}"], "path": ["api", "Zones"]}, "description": "创建新的功能区域（常温/冷藏/冷冻/危险品等）"}, "response": []}, {"name": "根据仓库查询区域", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Zones/by-warehouse/{{warehouseId}}", "host": ["{{baseUrl}}"], "path": ["api", "Zones", "by-warehouse", "{{warehouseId}}"]}, "description": "查询指定仓库下的所有区域"}, "response": []}, {"name": "按类型查询区域", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Zones/by-type?zoneType=2&warehouseId={{warehouseId}}", "host": ["{{baseUrl}}"], "path": ["api", "Zones", "by-type"], "query": [{"key": "zoneType", "value": "2", "description": "区域类型：1=常温,2=冷藏,3=冷冻,4=危险品"}, {"key": "warehouseId", "value": "{{warehouseId}}", "description": "仓库ID"}]}, "description": "按区域类型查询（常温/冷藏/冷冻/危险品等）"}, "response": []}], "description": "区域管理相关API，支持多功能区域、环境控制和安全管理"}, {"name": "07 - 货架管理 (层级架构)", "item": [{"name": "获取货架列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Shelves", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>"]}, "description": "获取所有货架列表"}, "response": []}, {"name": "创建新货架", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"RACK-A001\",\n  \"name\": \"标准货架001\",\n  \"description\": \"五层标准货架，适用于箱装货物\",\n  \"zoneId\": {{zoneId}},\n  \"shelfType\": 1,\n  \"status\": 1,\n  \"length\": 2.5,\n  \"width\": 1.0,\n  \"height\": 2.5,\n  \"levels\": 5,\n  \"positionsPerLevel\": 4,\n  \"maxWeight\": 2000.0,\n  \"maxWeightPerPosition\": 100.0,\n  \"material\": \"钢制\",\n  \"manufacturer\": \"标准货架公司\",\n  \"configuration\": \"{\\\"allowMixedSKU\\\":true,\\\"fifoRequired\\\":false}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Shelves", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>"]}, "description": "创建新的货架（标准/重型/悬臂/冷藏等类型）"}, "response": []}, {"name": "根据区域查询货架", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Shelves/by-zone/{{zoneId}}", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "by-zone", "{{zoneId}}"]}, "description": "查询指定区域下的所有货架"}, "response": []}, {"name": "货架安全检查记录", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shelfId\": {{shelfId}},\n  \"inspectionType\": \"safety\",\n  \"result\": \"passed\",\n  \"inspectorName\": \"李工程师\",\n  \"inspectionDate\": \"2025-07-04T10:00:00Z\",\n  \"nextInspectionDate\": \"2025-10-04T10:00:00Z\",\n  \"findings\": [\n    {\n      \"item\": \"结构完整性\",\n      \"status\": \"良好\",\n      \"notes\": \"无变形，连接牢固\"\n    },\n    {\n      \"item\": \"承重测试\",\n      \"status\": \"通过\",\n      \"notes\": \"最大承重2000kg测试通过\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/Shelves/{{shelfId}}/inspection", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "{{shelfId}}", "inspection"]}, "description": "记录货架安全检查结果"}, "response": []}], "description": "货架管理相关API，支持多种货架类型、安全认证和检查记录"}, {"name": "08 - 储位能力与分类管理", "item": [{"name": "为储位添加能力", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"capabilityType\": 1,\n  \"capabilityLevel\": 3,\n  \"capabilityName\": \"温度控制\",\n  \"description\": \"精确温度控制，适合生鲜食品存储\",\n  \"parameters\": \"{\\\"targetTemperature\\\":4,\\\"tolerance\\\":1,\\\"unit\\\":\\\"celsius\\\",\\\"monitoringInterval\\\":300}\",\n  \"isEnabled\": true,\n  \"priority\": 1,\n  \"validationRules\": \"{\\\"temperatureRange\\\":{\\\"min\\\":2,\\\"max\\\":8},\\\"alertThreshold\\\":0.5}\",\n  \"effectiveFrom\": \"2025-07-04T00:00:00Z\",\n  \"certifications\": \"{\\\"haccp\\\":true,\\\"iso22000\\\":true}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}/capabilities", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}", "capabilities"]}, "description": "为储位添加特殊能力（温控/危险品/贵重品等）"}, "response": []}, {"name": "为储位添加分类", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"dimension\": 1,\n  \"category\": \"Industry\",\n  \"value\": \"Food\",\n  \"displayName\": \"食品行业\",\n  \"description\": \"适用于食品存储的储位\",\n  \"tags\": \"[\\\"food-grade\\\",\\\"haccp-compliant\\\"]\",\n  \"properties\": \"{\\\"temperatureControlled\\\":true,\\\"humidityControlled\\\":false,\\\"sanitationRequired\\\":true}\",\n  \"priority\": 1,\n  \"isEnabled\": true,\n  \"businessRules\": \"{\\\"autoAssignment\\\":true,\\\"materialTypes\\\":[\\\"fresh\\\",\\\"frozen\\\",\\\"dry_goods\\\"],\\\"restrictions\\\":[\\\"no_chemicals\\\",\\\"no_non_food\\\"]}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/{{storageLocationId}}/classifications", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "{{storageLocationId}}", "classifications"]}, "description": "为储位添加业务分类（行业/材料/安全等级等）"}, "response": []}, {"name": "根据能力查询储位", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/by-capability/1?warehouseId={{warehouseId}}", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "by-capability", "1"], "query": [{"key": "warehouseId", "value": "{{warehouseId}}", "description": "限制查询范围到指定仓库"}]}, "description": "查询具有特定能力的储位（1=温控,2=危险品,3=贵重品等）"}, "response": []}, {"name": "根据分类查询储位", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/StorageLocations/by-classification/Industry/Food", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "by-classification", "Industry", "Food"]}, "description": "查询具有特定分类的储位（行业/材料/安全等级等）"}, "response": []}, {"name": "智能储位推荐", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"materialType\": \"Food\",\n  \"temperatureRequirement\": \"Refrigerated\",\n  \"hazmatClass\": null,\n  \"specialRequirements\": [\"HACCP\", \"FoodGrade\"],\n  \"quantity\": 100,\n  \"weight\": 50.0,\n  \"dimensions\": {\n    \"length\": 0.6,\n    \"width\": 0.4,\n    \"height\": 0.3\n  },\n  \"preferredZone\": \"COLD-A\",\n  \"proximityPreference\": \"near_dock\"\n}"}, "url": {"raw": "{{baseUrl}}/api/StorageLocations/recommend", "host": ["{{baseUrl}}"], "path": ["api", "StorageLocations", "recommend"]}, "description": "根据物料属性智能推荐最适合的储位"}, "response": []}], "description": "储位能力与分类管理API，支持灵活的能力分配和智能储位推荐"}, {"name": "09 - 配置继承与管理", "item": [{"name": "设置仓库级配置", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"globalSettings\": {\n    \"operatingHours\": \"06:00-22:00\",\n    \"defaultLanguage\": \"zh-CN\",\n    \"temperatureUnit\": \"celsius\",\n    \"weightUnit\": \"kg\"\n  },\n  \"securitySettings\": {\n    \"accessCardRequired\": true,\n    \"biometricAuth\": false,\n    \"visitorEscortRequired\": true\n  },\n  \"operationalSettings\": {\n    \"autoReplenishment\": true,\n    \"cycleCounting\": \"weekly\",\n    \"qualityCheck\": \"random\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Configuration/warehouse/{{warehouseId}}", "host": ["{{baseUrl}}"], "path": ["api", "Configuration", "warehouse", "{{warehouseId}}"]}, "description": "设置仓库级全局配置，作为下级配置的默认值"}, "response": []}, {"name": "设置区域级配置", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"temperatureSettings\": {\n    \"operatingHours\": \"24:00-24:00\",\n    \"monitoringInterval\": 300,\n    \"alertThreshold\": 0.5,\n    \"emergencyProtocol\": \"immediate_notification\"\n  },\n  \"securitySettings\": {\n    \"biometricAuth\": true,\n    \"additionalApproval\": true\n  },\n  \"operationalSettings\": {\n    \"cycleCounting\": \"daily\",\n    \"fifoEnforcement\": true\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Configuration/zone/{{zoneId}}", "host": ["{{baseUrl}}"], "path": ["api", "Configuration", "zone", "{{zoneId}}"]}, "description": "设置区域级配置，可覆盖仓库级配置"}, "response": []}, {"name": "解析储位有效配置", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Configuration/resolve/{{storageLocationId}}", "host": ["{{baseUrl}}"], "path": ["api", "Configuration", "resolve", "{{storageLocationId}}"]}, "description": "解析储位的最终有效配置（继承+覆盖）"}, "response": []}, {"name": "查看配置继承路径", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Configuration/hierarchy/{{storageLocationId}}", "host": ["{{baseUrl}}"], "path": ["api", "Configuration", "hierarchy", "{{storageLocationId}}"]}, "description": "查看配置的层级继承路径和优先级"}, "response": []}, {"name": "批量应用配置", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"targetLevel\": \"Zone\",\n  \"targetId\": {{zoneId}},\n  \"configuration\": {\n    \"operationalSettings\": {\n      \"qualityCheck\": \"every_item\",\n      \"temperatureLogging\": true\n    },\n    \"securitySettings\": {\n      \"additionalApproval\": true\n    }\n  },\n  \"applyToChildren\": true,\n  \"overrideExisting\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/Configuration/apply-batch", "host": ["{{baseUrl}}"], "path": ["api", "Configuration", "apply-batch"]}, "description": "批量应用配置到指定层级及其子层级"}, "response": []}], "description": "层级配置管理API，支持配置继承、覆盖和批量应用"}, {"name": "10 - 条码解析系统", "item": [{"name": "系统状态检查", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/BarcodeTest/system-status", "host": ["{{baseUrl}}"], "path": ["api", "BarcodeTest", "system-status"]}, "description": "检查条码解析系统的整体状态，包括规则数量、配置数量等"}, "response": []}, {"name": "创建测试数据", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/BarcodeTest/create-test-data", "host": ["{{baseUrl}}"], "path": ["api", "BarcodeTest", "create-test-data"]}, "description": "创建条码解析系统的测试数据，包括解析规则和配置"}, "response": []}, {"name": "解析标准物料条码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcode\": \"MATSKU001QTY100BATCHB2024001UNIQUEUC12345678\",\n  \"context\": {\n    \"operationType\": \"INBOUND\",\n    \"clientId\": \"DEFAULT_CLIENT\",\n    \"userId\": \"<EMAIL>\",\n    \"workstationId\": \"WS001\",\n    \"shelfId\": \"A01\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/parse", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "parse"]}, "description": "解析包含完整信息的标准物料条码：SKU、数量、批次、唯一码"}, "response": []}, {"name": "解析简化物料条码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcode\": \"SKU12345\",\n  \"context\": {\n    \"operationType\": \"INBOUND\",\n    \"clientId\": \"DEFAULT_CLIENT\",\n    \"userId\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/parse", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "parse"]}, "description": "解析只包含SKU信息的简化物料条码"}, "response": []}, {"name": "解析标准储位条码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcode\": \"LOCA01-B02-C03\",\n  \"context\": {\n    \"operationType\": \"PUTAWAY\",\n    \"clientId\": \"DEFAULT_CLIENT\",\n    \"userId\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/parse", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "parse"]}, "description": "解析标准储位条码：区域-货架-层级"}, "response": []}, {"name": "解析简化储位条码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcode\": \"A01-B02-C03\",\n  \"context\": {\n    \"operationType\": \"PICKING\",\n    \"clientId\": \"DEFAULT_CLIENT\",\n    \"userId\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/parse", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "parse"]}, "description": "解析简化储位条码：区域-货架-层级"}, "response": []}, {"name": "解析包装条码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcode\": \"PKG2024070300123\",\n  \"context\": {\n    \"operationType\": \"OUTBOUND\",\n    \"clientId\": \"DEFAULT_CLIENT\",\n    \"userId\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/parse", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "parse"]}, "description": "解析包装条码：包装编号信息"}, "response": []}, {"name": "批量解析条码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcodes\": [\n    \"MATSKU001QTY100BATCHB2024001UNIQUEUC12345678\",\n    \"SKU12345\",\n    \"LOCA01-B02-C03\",\n    \"A01-B02-C03\",\n    \"PKG2024070300123\"\n  ],\n  \"context\": {\n    \"operationType\": \"INVENTORY\",\n    \"clientId\": \"DEFAULT_CLIENT\",\n    \"userId\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/parse-batch", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "parse-batch"]}, "description": "批量解析多个条码，用于库存盘点等场景"}, "response": []}, {"name": "条码规则管理 - 获取规则列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Barcode/rules?categoryCode=MATERIAL&isActive=true", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "rules"], "query": [{"key": "categoryCode", "value": "MATERIAL", "description": "条码类别：MATERIAL, STORAGE_LOCATION, WORK_ORDER, PACKAGE"}, {"key": "isActive", "value": "true", "description": "是否只返回活跃的规则"}]}, "description": "获取条码解析规则列表，支持按类别和状态筛选"}, "response": []}, {"name": "条码解析与验证", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcode\": \"MATSKU001QTY100BATCHB2024001UNIQUEUC12345678\",\n  \"validationRules\": {\n    \"requireUniqueCode\": true,\n    \"requireQuantity\": true,\n    \"requireBatch\": false\n  },\n  \"context\": {\n    \"operationType\": \"INBOUND\",\n    \"clientId\": \"DEFAULT_CLIENT\",\n    \"userId\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/parse-and-validate", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "parse-and-validate"]}, "description": "解析条码并进行业务规则验证"}, "response": []}, {"name": "验证唯一码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Barcode/validate-unique-code?uniqueCode=UC12345678&clientId=DEFAULT_CLIENT", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "validate-unique-code"], "query": [{"key": "uniqueCode", "value": "UC12345678", "description": "要验证的唯一码"}, {"key": "clientId", "value": "DEFAULT_CLIENT", "description": "客户端ID"}]}, "description": "验证唯一码是否已存在于系统中"}, "response": []}, {"name": "注册唯一码", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"uniqueCode\": \"UC12345678\",\n  \"clientId\": \"DEFAULT_CLIENT\",\n  \"entityType\": \"Material\",\n  \"entityId\": \"SKU001\",\n  \"additionalData\": {\n    \"batchNumber\": \"B2024001\",\n    \"quantity\": 100,\n    \"expiryDate\": \"2025-12-31\"\n  },\n  \"createdBy\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/register-unique-code", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "register-unique-code"]}, "description": "在系统中注册新的唯一码"}, "response": []}, {"name": "条码规则管理 - 创建新规则", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"categoryId\": 1,\n  \"ruleName\": \"自定义物料条码规则\",\n  \"regexPattern\": \"^CUSTOM([A-Z0-9]{8})QTY(\\\\d+)$\",\n  \"fieldMappings\": {\n    \"materialSku\": {\n      \"Group\": 1,\n      \"Type\": \"string\",\n      \"Required\": true,\n      \"Description\": \"物料SKU\"\n    },\n    \"quantity\": {\n      \"Group\": 2,\n      \"Type\": \"decimal\",\n      \"Required\": false,\n      \"DefaultValue\": 1,\n      \"Description\": \"数量\"\n    }\n  },\n  \"priority\": 20,\n  \"isActive\": true,\n  \"description\": \"客户自定义的物料条码解析规则\",\n  \"clientId\": \"DEFAULT_CLIENT\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Barcode/rules", "host": ["{{baseUrl}}"], "path": ["api", "Barcode", "rules"]}, "description": "创建新的条码解析规则"}, "response": []}, {"name": "配置管理 - 获取分层配置", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"configKey\": \"StorageLocationValidationStrategy\",\n  \"context\": {\n    \"clientId\": \"DEFAULT_CLIENT\",\n    \"zoneId\": \"A\",\n    \"shelfId\": \"A01\",\n    \"locationId\": \"A01-01-01\",\n    \"materialCategoryId\": \"ELECTRONICS\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/Configuration/resolve", "host": ["{{baseUrl}}"], "path": ["api", "Configuration", "resolve"]}, "description": "根据分层上下文解析配置值"}, "response": []}, {"name": "配置管理 - 创建配置", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"configLevel\": \"SHELF\",\n  \"targetType\": \"Shelf\",\n  \"targetId\": \"A01\",\n  \"configKey\": \"StorageLocationValidationStrategy\",\n  \"configValue\": {\n    \"strategy\": \"SINGLE_SKU_PER_LOCATION\",\n    \"allowMixedStorage\": false,\n    \"maxSkuPerLocation\": 1,\n    \"enforceUniqueCode\": true\n  },\n  \"priority\": 10,\n  \"isActive\": true,\n  \"description\": \"A01货架单SKU储存策略\",\n  \"clientId\": \"DEFAULT_CLIENT\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Configuration/configurations", "host": ["{{baseUrl}}"], "path": ["api", "Configuration", "configurations"]}, "description": "创建新的分层配置"}, "response": []}, {"name": "测试物料条码解析", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/BarcodeTest/test-material-barcode", "host": ["{{baseUrl}}"], "path": ["api", "BarcodeTest", "test-material-barcode"]}, "description": "使用预设测试数据测试物料条码解析功能"}, "response": []}, {"name": "测试储位条码解析", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/BarcodeTest/test-location-barcode", "host": ["{{baseUrl}}"], "path": ["api", "BarcodeTest", "test-location-barcode"]}, "description": "使用预设测试数据测试储位条码解析功能"}, "response": []}, {"name": "测试配置解析", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/BarcodeTest/test-configuration", "host": ["{{baseUrl}}"], "path": ["api", "BarcodeTest", "test-configuration"]}, "description": "测试分层配置解析功能"}, "response": []}], "description": "条码解析系统相关API，包括条码解析、规则管理、配置管理等功能"}, {"name": "11 - 入库管理", "item": [{"name": "获取入库订单列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Inbound?status=0", "host": ["{{baseUrl}}"], "path": ["api", "Inbound"], "query": [{"key": "status", "value": "0", "description": "订单状态筛选：0=待处理,1=进行中,2=已完成,3=已取消"}]}, "description": "获取入库订单列表，支持按状态筛选"}, "response": []}, {"name": "创建入库订单", "event": [{"listen": "test", "script": {"exec": ["// 自动保存订单ID到环境变量", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.id) {", "        pm.environment.set('inboundOrderId', response.id);", "        console.log('✅ 入库订单ID已保存:', response.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"supplierName\": \"测试供应商\",\n  \"supplierCode\": \"SUP001\",\n  \"expectedDate\": \"2025-01-10T10:00:00Z\",\n  \"remarks\": \"测试入库订单\",\n  \"items\": [\n    {\n      \"materialId\": 1,\n      \"expectedQuantity\": 100,\n      \"batchNumber\": \"BATCH20250105\",\n      \"lpnCode\": \"LPN001\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/Inbound", "host": ["{{baseUrl}}"], "path": ["api", "Inbound"]}, "description": "创建新的入库订单，包含订单项目"}, "response": []}, {"name": "获取入库订单详情", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Inbound/{{inboundOrderId}}", "host": ["{{baseUrl}}"], "path": ["api", "Inbound", "{{inboundOrderId}}"]}, "description": "根据订单ID获取入库订单详细信息"}, "response": []}, {"name": "根据订单号查询入库订单", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Inbound/by-number/IB20250105xxxx", "host": ["{{baseUrl}}"], "path": ["api", "Inbound", "by-number", "IB20250105xxxx"]}, "description": "根据订单号查询入库订单"}, "response": []}, {"name": "向入库订单添加项目", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"materialId\": 1,\n  \"expectedQuantity\": 50,\n  \"batchNumber\": \"BATCH20250105B\",\n  \"lpnCode\": \"LPN002\",\n  \"remarks\": \"补充入库项目\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Inbound/{{inboundOrderId}}/items", "host": ["{{baseUrl}}"], "path": ["api", "Inbound", "{{inboundOrderId}}", "items"]}, "description": "向现有入库订单添加新的项目"}, "response": []}, {"name": "批量处理入库订单", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"itemId\": 1,\n      \"storageLocationId\": 1,\n      \"actualQuantity\": 100,\n      \"batchNumber\": \"BATCH20250105\",\n      \"uniqueCode\": \"UNIQUE001\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/Inbound/{{inboundOrderId}}/process", "host": ["{{baseUrl}}"], "path": ["api", "Inbound", "{{inboundOrderId}}", "process"]}, "description": "批量处理入库订单的所有项目"}, "response": []}, {"name": "条码入库处理", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcodeData\": \"MATTEST01QTY100BATCH20250105UNIQUEIDENTITY01\",\n  \"storageLocationCode\": \"A01-01-01\",\n  \"actualQuantity\": 100\n}"}, "url": {"raw": "{{baseUrl}}/api/Inbound/items/1/process-barcode", "host": ["{{baseUrl}}"], "path": ["api", "Inbound", "items", "1", "process-barcode"]}, "description": "使用条码扫描处理单个入库项目"}, "response": []}, {"name": "更新入库订单状态", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/Inbound/{{inboundOrderId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "Inbound", "{{inboundOrderId}}", "status"]}, "description": "更新入库订单状态：0=待处理,1=进行中,2=已完成,3=已取消"}, "response": []}, {"name": "取消入库订单", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"测试取消功能\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Inbound/{{inboundOrderId}}/cancel", "host": ["{{baseUrl}}"], "path": ["api", "Inbound", "{{inboundOrderId}}", "cancel"]}, "description": "取消入库订单并记录取消原因"}, "response": []}, {"name": "生成入库订单号", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Inbound/generate-order-number", "host": ["{{baseUrl}}"], "path": ["api", "Inbound", "generate-order-number"]}, "description": "生成新的入库订单号（格式：IB+日期+序号）"}, "response": []}], "description": "入库管理相关API，包括订单创建、条码入库、状态管理等功能"}, {"name": "12 - 出库管理", "item": [{"name": "获取出库订单列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Outbound?status=0", "host": ["{{baseUrl}}"], "path": ["api", "Outbound"], "query": [{"key": "status", "value": "0", "description": "订单状态筛选：0=待处理,1=拣选中,2=已完成,3=已取消"}]}, "description": "获取出库订单列表，支持按状态筛选"}, "response": []}, {"name": "创建出库订单", "event": [{"listen": "test", "script": {"exec": ["// 自动保存订单ID到环境变量", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.id) {", "        pm.environment.set('outboundOrderId', response.id);", "        console.log('✅ 出库订单ID已保存:', response.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": 0,\n  \"customerName\": \"测试客户\",\n  \"customerCode\": \"CUS001\",\n  \"expectedDate\": \"2025-01-06T14:00:00Z\",\n  \"remarks\": \"测试出库订单\",\n  \"items\": [\n    {\n      \"materialId\": 1,\n      \"requiredQuantity\": 50,\n      \"batchNumber\": \"20250105\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/Outbound", "host": ["{{baseUrl}}"], "path": ["api", "Outbound"]}, "description": "创建新的出库订单，包含订单项目"}, "response": []}, {"name": "获取出库订单详情", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Outbound/{{outboundOrderId}}", "host": ["{{baseUrl}}"], "path": ["api", "Outbound", "{{outboundOrderId}}"]}, "description": "根据订单ID获取出库订单详细信息"}, "response": []}, {"name": "根据订单号查询出库订单", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Outbound/by-number/OB20250105xxxx", "host": ["{{baseUrl}}"], "path": ["api", "Outbound", "by-number", "OB20250105xxxx"]}, "description": "根据订单号查询出库订单"}, "response": []}, {"name": "向出库订单添加项目", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"materialId\": 1,\n  \"requiredQuantity\": 25,\n  \"batchNumber\": \"BATCH20250105\",\n  \"remarks\": \"补充出库项目\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Outbound/{{outboundOrderId}}/items", "host": ["{{baseUrl}}"], "path": ["api", "Outbound", "{{outboundOrderId}}", "items"]}, "description": "向现有出库订单添加新的项目"}, "response": []}, {"name": "开始拣选作业", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/Outbound/{{outboundOrderId}}/start-picking", "host": ["{{baseUrl}}"], "path": ["api", "Outbound", "{{outboundOrderId}}", "start-picking"]}, "description": "开始出库订单的拣选作业，生成拣选任务"}, "response": []}, {"name": "完成出库订单", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/Outbound/{{outboundOrderId}}/complete", "host": ["{{baseUrl}}"], "path": ["api", "Outbound", "{{outboundOrderId}}", "complete"]}, "description": "完成出库订单，前提是所有拣选任务已完成"}, "response": []}, {"name": "更新出库订单状态", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/Outbound/{{outboundOrderId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "Outbound", "{{outboundOrderId}}", "status"]}, "description": "更新出库订单状态：0=待处理,1=拣选中,2=已完成,3=已取消"}, "response": []}, {"name": "取消出库订单", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"测试取消功能\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Outbound/{{outboundOrderId}}/cancel", "host": ["{{baseUrl}}"], "path": ["api", "Outbound", "{{outboundOrderId}}", "cancel"]}, "description": "取消出库订单并记录取消原因"}, "response": []}, {"name": "生成出库订单号", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Outbound/generate-order-number", "host": ["{{baseUrl}}"], "path": ["api", "Outbound", "generate-order-number"]}, "description": "生成新的出库订单号（格式：OB+日期+序号）"}, "response": []}], "description": "出库管理相关API，包括订单创建、拣选作业、状态管理等功能"}, {"name": "13 - 拣选任务管理", "item": [{"name": "获取拣选任务列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Picking/tasks?orderId={{outboundOrderId}}&status=0", "host": ["{{baseUrl}}"], "path": ["api", "Picking", "tasks"], "query": [{"key": "orderId", "value": "{{outboundOrderId}}", "description": "出库订单ID筛选"}, {"key": "status", "value": "0", "description": "任务状态筛选：0=待处理,1=进行中,2=已完成,3=已取消"}, {"key": "assignedTo", "value": "", "description": "分配给谁的任务", "disabled": true}]}, "description": "获取拣选任务列表，支持多维度筛选"}, "response": []}, {"name": "为订单生成拣选任务", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/Picking/orders/{{outboundOrderId}}/generate-tasks", "host": ["{{baseUrl}}"], "path": ["api", "Picking", "orders", "{{outboundOrderId}}", "generate-tasks"]}, "description": "为指定出库订单生成拣选任务"}, "response": []}, {"name": "处理拣选任务", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"pickedQuantity\": 50,\n  \"batchNumber\": \"BATCH20250105\",\n  \"lpnCode\": \"LPN001\",\n  \"remarks\": \"正常拣选完成\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Picking/tasks/1/process", "host": ["{{baseUrl}}"], "path": ["api", "Picking", "tasks", "1", "process"]}, "description": "处理拣选任务，记录实际拣选数量"}, "response": []}, {"name": "条码拣选处理", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"barcodeData\": \"MATTEST01QTY100BATCH20250105UNIQUEIDENTITY01\",\n  \"pickedQuantity\": 50\n}"}, "url": {"raw": "{{baseUrl}}/api/Picking/tasks/1/process-barcode", "host": ["{{baseUrl}}"], "path": ["api", "Picking", "tasks", "1", "process-barcode"]}, "description": "使用条码扫描处理拣选任务"}, "response": []}], "description": "拣选任务管理相关API，包括任务生成、条码拣选、任务处理等功能"}, {"name": "14 - 库存事务管理", "item": [{"name": "获取库存事务列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/InventoryTransactions?type=0&status=1", "host": ["{{baseUrl}}"], "path": ["api", "InventoryTransactions"], "query": [{"key": "type", "value": "0", "description": "事务类型：0=入库,1=出库,2=调整,3=移库,4=冻结,5=解冻"}, {"key": "status", "value": "1", "description": "事务状态：0=待处理,1=已执行,2=已取消"}, {"key": "materialId", "value": "", "description": "物料ID筛选", "disabled": true}, {"key": "storageLocationId", "value": "", "description": "储位ID筛选", "disabled": true}]}, "description": "获取库存事务列表，支持多维度筛选"}, "response": []}, {"name": "创建库存事务", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": 2,\n  \"storageLocationId\": 1,\n  \"materialId\": 1,\n  \"quantity\": 10,\n  \"reason\": \"库存调整 - 盘点差异\",\n  \"batchNumber\": \"BATCH20250105\",\n  \"lpnCode\": \"LPN001\",\n  \"remarks\": \"年度盘点发现差异\"\n}"}, "url": {"raw": "{{baseUrl}}/api/InventoryTransactions", "host": ["{{baseUrl}}"], "path": ["api", "InventoryTransactions"]}, "description": "创建新的库存事务（调整、移库等）"}, "response": []}, {"name": "获取事务详情", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/InventoryTransactions/1", "host": ["{{baseUrl}}"], "path": ["api", "InventoryTransactions", "1"]}, "description": "根据事务ID获取详细信息"}, "response": []}, {"name": "执行库存事务", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/InventoryTransactions/1/execute", "host": ["{{baseUrl}}"], "path": ["api", "InventoryTransactions", "1", "execute"]}, "description": "执行待处理的库存事务"}, "response": []}, {"name": "取消库存事务", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"操作错误，需要取消\"\n}"}, "url": {"raw": "{{baseUrl}}/api/InventoryTransactions/1/cancel", "host": ["{{baseUrl}}"], "path": ["api", "InventoryTransactions", "1", "cancel"]}, "description": "取消库存事务并记录取消原因"}, "response": []}, {"name": "批量执行库存事务", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"transactionIds\": [1, 2, 3]\n}"}, "url": {"raw": "{{baseUrl}}/api/InventoryTransactions/batch-execute", "host": ["{{baseUrl}}"], "path": ["api", "InventoryTransactions", "batch-execute"]}, "description": "批量执行多个库存事务"}, "response": []}], "description": "库存事务管理相关API，包括事务创建、执行、取消等功能"}, {"name": "15 - 物料管理", "item": [{"name": "获取物料列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Materials", "host": ["{{baseUrl}}"], "path": ["api", "Materials"]}, "description": "获取所有物料列表"}, "response": []}, {"name": "创建测试物料", "event": [{"listen": "test", "script": {"exec": ["// 自动保存物料ID到环境变量", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.id) {", "        pm.environment.set('materialId', response.id);", "        console.log('✅ 物料ID已保存:', response.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sku\": \"TEST01\",\n  \"name\": \"测试商品A\",\n  \"description\": \"用于测试出入库的商品\",\n  \"category\": \"电子产品\",\n  \"unit\": \"个\",\n  \"weight\": 0.5,\n  \"dimensions\": \"10x10x5\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/Materials", "host": ["{{baseUrl}}"], "path": ["api", "Materials"]}, "description": "创建用于测试的物料"}, "response": []}, {"name": "获取物料详情", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Materials/{{materialId}}", "host": ["{{baseUrl}}"], "path": ["api", "Materials", "{{materialId}}"]}, "description": "根据物料ID获取详细信息"}, "response": []}], "description": "物料管理相关API，用于测试出入库功能的基础数据"}, {"name": "16 - 库存查询", "item": [{"name": "获取库存列表", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Inventories?materialId={{materialId}}", "host": ["{{baseUrl}}"], "path": ["api", "Inventories"], "query": [{"key": "materialId", "value": "{{materialId}}", "description": "物料ID筛选"}, {"key": "storageLocationId", "value": "", "description": "储位ID筛选", "disabled": true}, {"key": "status", "value": "", "description": "库存状态筛选", "disabled": true}]}, "description": "获取库存列表，支持多维度筛选"}, "response": []}, {"name": "根据储位查询库存", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Inventories/by-location/{{storageLocationId}}", "host": ["{{baseUrl}}"], "path": ["api", "Inventories", "by-location", "{{storageLocationId}}"]}, "description": "查询指定储位的所有库存"}, "response": []}, {"name": "根据物料查询库存", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Inventories/by-material/{{materialId}}", "host": ["{{baseUrl}}"], "path": ["api", "Inventories", "by-material", "{{materialId}}"]}, "description": "查询指定物料在所有储位的库存"}, "response": []}], "description": "库存查询相关API，用于验证出入库操作的结果"}, {"name": "99 - 测试和诊断", "item": [{"name": "测试用户查找", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Auth/test-user/<EMAIL>", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "test-user", "<EMAIL>"]}, "description": "测试系统是否能正确查找用户（诊断用途）"}, "response": []}, {"name": "测试密码验证", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin@123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/test-password", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "test-password"]}, "description": "测试密码验证功能（诊断用途）"}, "response": []}, {"name": "简化登录测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin@123456\",\n  \"rememberMe\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/login-simple", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "login-simple"]}, "description": "使用简化版登录接口（无RefreshToken）"}, "response": []}, {"name": "登录步骤诊断", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin@123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/test-login-steps", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "test-login-steps"]}, "description": "分步诊断登录流程，帮助定位问题"}, "response": []}], "description": "系统测试和诊断相关API，用于故障排查和系统状态检查"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 全局预请求脚本", "// 确保环境变量设置正确", "if (!pm.environment.get('baseUrl')) {", "    pm.environment.set('baseUrl', 'http://localhost:5000');", "    console.log('设置默认baseUrl: http://localhost:5000');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局响应测试脚本", "pm.test('Response status code should not be 500', function () {", "    pm.expect(pm.response.code).to.not.equal(500);", "});", "", "pm.test('Response should not contain error message', function () {", "    const responseText = pm.response.text();", "    pm.expect(responseText).to.not.include('Internal Server Error');", "});"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "controllerId", "value": "1", "type": "string"}, {"key": "warehouseId", "value": "1", "type": "string"}, {"key": "zoneId", "value": "1", "type": "string"}, {"key": "shelfId", "value": "1", "type": "string"}, {"key": "storageLocationId", "value": "1", "type": "string"}]}