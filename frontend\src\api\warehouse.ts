import { apiClient } from './client'
import type { Warehouse, Zone, Shelf, StorageLocation, PaginatedResponse } from '@/types'

export const warehouseApi = {
  // Warehouses
  getWarehouses: () => 
    apiClient.get<Warehouse[]>('/warehouses'),
  
  getWarehouse: (id: number) => 
    apiClient.get<Warehouse>(`/warehouses/${id}`),
  
  createWarehouse: (data: Partial<Warehouse>) => 
    apiClient.post<Warehouse>('/warehouses', data),
  
  updateWarehouse: (id: number, data: Partial<Warehouse>) => 
    apiClient.put<Warehouse>(`/warehouses/${id}`, data),
  
  deleteWarehouse: (id: number) => 
    apiClient.delete(`/warehouses/${id}`),

  // Zones
  getZones: () => 
    apiClient.get<Zone[]>('/zones'),
  
  getZonesByWarehouse: (warehouseId: number) => 
    apiClient.get<Zone[]>(`/zones/by-warehouse/${warehouseId}`),
  
  getZone: (id: number) => 
    apiClient.get<Zone>(`/zones/${id}`),
  
  createZone: (data: Partial<Zone>) => 
    apiClient.post<Zone>('/zones', data),
  
  updateZone: (id: number, data: Partial<Zone>) => 
    apiClient.put<Zone>(`/zones/${id}`, data),
  
  deleteZone: (id: number) => 
    apiClient.delete(`/zones/${id}`),

  // Shelves
  getShelves: () => 
    apiClient.get<Shelf[]>('/shelves'),
  
  getShelvesByZone: (zoneId: number) => 
    apiClient.get<Shelf[]>(`/shelves/by-zone/${zoneId}`),
  
  getShelf: (id: number) => 
    apiClient.get<Shelf>(`/shelves/${id}`),
  
  createShelf: (data: Partial<Shelf>) => 
    apiClient.post<Shelf>('/shelves', data),
  
  updateShelf: (id: number, data: Partial<Shelf>) => 
    apiClient.put<Shelf>(`/shelves/${id}`, data),
  
  deleteShelf: (id: number) => 
    apiClient.delete(`/shelves/${id}`),

  // Storage Locations
  getStorageLocations: (params?: { page?: number; size?: number; search?: string }) => 
    apiClient.get<PaginatedResponse<StorageLocation>>('/storage-locations', { params }),
  
  getStorageLocationsByShelf: (shelfId: number) => 
    apiClient.get<StorageLocation[]>(`/storage-locations/by-shelf/${shelfId}`),
  
  getStorageLocation: (id: number) => 
    apiClient.get<StorageLocation>(`/storage-locations/${id}`),
  
  createStorageLocation: (data: Partial<StorageLocation>) => 
    apiClient.post<StorageLocation>('/storage-locations', data),
  
  updateStorageLocation: (id: number, data: Partial<StorageLocation>) => 
    apiClient.put<StorageLocation>(`/storage-locations/${id}`, data),
  
  deleteStorageLocation: (id: number) => 
    apiClient.delete(`/storage-locations/${id}`),

  // Excel Import/Export
  downloadTemplate: () => 
    apiClient.get('/storage-locations/template', { responseType: 'blob' }),
  
  importStorageLocations: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return apiClient.post('/storage-locations/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  
  exportStorageLocations: () => 
    apiClient.get('/storage-locations/export', { responseType: 'blob' }),
}