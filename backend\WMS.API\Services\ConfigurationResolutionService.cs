using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Services
{
    /// <summary>
    /// 配置解析服务实现
    /// </summary>
    public class ConfigurationResolutionService : IConfigurationResolutionService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ConfigurationResolutionService> _logger;

        // 配置优先级映射（数字越小优先级越高）
        private readonly Dictionary<ConfigLevel, int> _configPriorityMap = new()
        {
            { ConfigLevel.MATERIAL_CATEGORY, 10 },
            { ConfigLevel.LOCATION, 20 },
            { ConfigLevel.SHELF, 30 },
            { ConfigLevel.ZONE, 40 },
            { ConfigLevel.CLIENT, 50 },
            { ConfigLevel.SYSTEM, 60 },
        };

        public ConfigurationResolutionService(
            ApplicationDbContext context,
            ILogger<ConfigurationResolutionService> logger
        )
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ConfigurationResolutionResult> ResolveConfigurationAsync(
            string configKey,
            ConfigurationContext context
        )
        {
            try
            {
                _logger.LogInformation(
                    "开始解析配置: {ConfigKey}, 客户: {ClientId}",
                    configKey,
                    context.ClientId
                );

                var result = new ConfigurationResolutionResult { ConfigKey = configKey };

                // 获取所有适用的配置
                var applicableConfigs = await GetApplicableConfigurationsAsync(context);

                // 筛选匹配的配置键
                var matchingConfigs = applicableConfigs
                    .Where(c => c.ConfigKey == configKey && c.IsActive)
                    .ToList();

                if (!matchingConfigs.Any())
                {
                    _logger.LogWarning("未找到配置: {ConfigKey}", configKey);
                    return result;
                }

                // 按配置层级优先级排序
                var sortedConfigs = matchingConfigs
                    .OrderBy(c => _configPriorityMap.GetValueOrDefault(c.ConfigLevel, 100))
                    .ThenBy(c => c.Priority)
                    .ToList();

                // 检查是否有冲突
                var conflictingConfigs = sortedConfigs
                    .GroupBy(c => _configPriorityMap.GetValueOrDefault(c.ConfigLevel, 100))
                    .Where(g => g.Count() > 1)
                    .ToList();

                if (conflictingConfigs.Any())
                {
                    result.HasConflicts = true;
                    await HandleConfigurationConflictAsync(
                        context,
                        configKey,
                        sortedConfigs,
                        result
                    );
                }

                // 应用最高优先级的配置
                var effectiveConfig = sortedConfigs.First();
                result.ConfigValue = effectiveConfig.ConfigValue;
                result.AppliedConfigLevel = effectiveConfig.ConfigLevel;
                result.AppliedConfigId = effectiveConfig.Id;
                result.ResolutionPath = BuildResolutionPath(sortedConfigs);

                _logger.LogInformation(
                    "配置解析完成: {ConfigKey}, 生效层级: {Level}",
                    configKey,
                    effectiveConfig.ConfigLevel
                );

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "配置解析时发生错误: {ConfigKey}", configKey);
                throw;
            }
        }

        public async Task<
            Dictionary<string, ConfigurationResolutionResult>
        > ResolveConfigurationsAsync(IEnumerable<string> configKeys, ConfigurationContext context)
        {
            var results = new Dictionary<string, ConfigurationResolutionResult>();

            foreach (var configKey in configKeys)
            {
                var result = await ResolveConfigurationAsync(configKey, context);
                results[configKey] = result;
            }

            return results;
        }

        public async Task<List<HierarchicalConfiguration>> GetClientConfigurationsAsync(
            string clientId
        )
        {
            return await _context
                .HierarchicalConfigurations.Where(c => c.ClientId == clientId && c.IsActive)
                .Where(c =>
                    c.EffectiveFrom <= DateTime.UtcNow
                    && (c.EffectiveTo == null || c.EffectiveTo > DateTime.UtcNow)
                )
                .OrderBy(c => c.Priority)
                .ToListAsync();
        }

        public async Task<List<HierarchicalConfiguration>> GetApplicableConfigurationsAsync(
            ConfigurationContext context
        )
        {
            var query = _context
                .HierarchicalConfigurations.Where(c => c.ClientId == context.ClientId && c.IsActive)
                .Where(c =>
                    c.EffectiveFrom <= DateTime.UtcNow
                    && (c.EffectiveTo == null || c.EffectiveTo > DateTime.UtcNow)
                );

            var configs = new List<HierarchicalConfiguration>();

            // 系统级配置
            var systemConfigs = await query
                .Where(c => c.ConfigLevel == ConfigLevel.SYSTEM)
                .ToListAsync();
            configs.AddRange(systemConfigs);

            // 客户级配置
            var clientConfigs = await query
                .Where(c => c.ConfigLevel == ConfigLevel.CLIENT)
                .ToListAsync();
            configs.AddRange(clientConfigs);

            // 区域级配置
            if (!string.IsNullOrEmpty(context.ZoneCode))
            {
                var zoneConfigs = await query
                    .Where(c =>
                        c.ConfigLevel == ConfigLevel.ZONE
                        && c.TargetType == "ZONE_CODE"
                        && c.TargetId == context.ZoneCode
                    )
                    .ToListAsync();
                configs.AddRange(zoneConfigs);
            }

            // 货架级配置
            if (!string.IsNullOrEmpty(context.ShelfCode))
            {
                var shelfConfigs = await query
                    .Where(c =>
                        c.ConfigLevel == ConfigLevel.SHELF
                        && c.TargetType == "SHELF_CODE"
                        && c.TargetId == context.ShelfCode
                    )
                    .ToListAsync();
                configs.AddRange(shelfConfigs);
            }

            // 储位级配置
            if (!string.IsNullOrEmpty(context.LocationCode))
            {
                var locationConfigs = await query
                    .Where(c =>
                        c.ConfigLevel == ConfigLevel.LOCATION
                        && c.TargetType == "LOCATION_CODE"
                        && c.TargetId == context.LocationCode
                    )
                    .ToListAsync();
                configs.AddRange(locationConfigs);
            }

            // 物料类别级配置
            if (!string.IsNullOrEmpty(context.MaterialCategory))
            {
                var materialConfigs = await query
                    .Where(c =>
                        c.ConfigLevel == ConfigLevel.MATERIAL_CATEGORY
                        && c.TargetType == "MATERIAL_CATEGORY"
                        && c.TargetId == context.MaterialCategory
                    )
                    .ToListAsync();
                configs.AddRange(materialConfigs);
            }

            return configs.Distinct().ToList();
        }

        public async Task<bool> LogConfigurationApplicationAsync(
            string operationId,
            ConfigurationContext context,
            Dictionary<string, ConfigurationResolutionResult> results
        )
        {
            try
            {
                var logs = new List<ConfigurationApplicationLog>();

                foreach (var result in results)
                {
                    var log = new ConfigurationApplicationLog
                    {
                        OperationId = operationId,
                        ClientId = context.ClientId,
                        ZoneCode = context.ZoneCode,
                        ShelfCode = context.ShelfCode,
                        LocationCode = context.LocationCode,
                        MaterialCategory = context.MaterialCategory,
                        ConfigKey = result.Key,
                        AppliedConfigLevel = result.Value.AppliedConfigLevel?.ToString(),
                        AppliedConfigId = result.Value.AppliedConfigId,
                        FinalConfigValue = result.Value.ConfigValue,
                        ConfigResolutionPath = result.Value.ResolutionPath,
                        UserId = context.UserId,
                    };
                    logs.Add(log);
                }

                _context.ConfigurationApplicationLogs.AddRange(logs);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "配置应用日志记录成功: {OperationId}, 配置数量: {Count}",
                    operationId,
                    logs.Count
                );
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录配置应用日志时发生错误: {OperationId}", operationId);
                return false;
            }
        }

        private async Task HandleConfigurationConflictAsync(
            ConfigurationContext context,
            string configKey,
            List<HierarchicalConfiguration> conflictingConfigs,
            ConfigurationResolutionResult result
        )
        {
            try
            {
                var conflictLog = new ConfigurationConflictLog
                {
                    ClientId = context.ClientId,
                    ConflictType = ConfigurationConflictType.RULE_CONFLICT,
                    TargetScope =
                        $"Zone:{context.ZoneCode}, Shelf:{context.ShelfCode}, Location:{context.LocationCode}, MaterialCategory:{context.MaterialCategory}",
                    ConflictingConfigs = JsonSerializer.Serialize(
                        conflictingConfigs.Select(c => new
                        {
                            c.Id,
                            c.ConfigLevel,
                            c.TargetType,
                            c.TargetId,
                            c.ConfigValue,
                            c.Priority,
                        })
                    ),
                    ResolutionStrategy = "PRIORITY_BASED",
                    ResolvedValue = conflictingConfigs.First().ConfigValue,
                    ResolvedBy = "SYSTEM",
                };

                _context.ConfigurationConflictLogs.Add(conflictLog);
                await _context.SaveChangesAsync();

                result.ConflictResolutionStrategy = "使用最高优先级配置";

                _logger.LogWarning(
                    "配置冲突已解决: {ConfigKey}, 冲突配置数量: {Count}",
                    configKey,
                    conflictingConfigs.Count
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理配置冲突时发生错误: {ConfigKey}", configKey);
            }
        }

        private string BuildResolutionPath(List<HierarchicalConfiguration> configs)
        {
            var pathElements = configs
                .Select(c => $"{c.ConfigLevel}({c.TargetType}:{c.TargetId})->Priority:{c.Priority}")
                .ToList();

            return string.Join(" | ", pathElements);
        }
    }
}
