namespace WMS.API.Constants
{
    /// <summary>
    /// 系统角色常量定义
    /// </summary>
    public static class RoleConstants
    {
        /// <summary>
        /// 超级管理员 - 拥有系统所有权限
        /// </summary>
        public const string SuperAdmin = "SuperAdmin";

        /// <summary>
        /// 管理员 - 拥有用户管理和系统配置权限
        /// </summary>
        public const string Admin = "Admin";

        /// <summary>
        /// 仓库管理员 - 拥有仓库运营管理权限
        /// </summary>
        public const string WarehouseManager = "WarehouseManager";

        /// <summary>
        /// 操作员 - 拥有基础操作权限
        /// </summary>
        public const string Operator = "Operator";

        /// <summary>
        /// 只读用户 - 只能查看数据
        /// </summary>
        public const string ReadOnly = "ReadOnly";

        /// <summary>
        /// 获取所有角色列表
        /// </summary>
        public static readonly string[] AllRoles = {
            SuperAdmin,
            Admin,
            WarehouseManager,
            Operator,
            ReadOnly
        };

        /// <summary>
        /// 管理级角色（可以管理用户）
        /// </summary>
        public static readonly string[] ManagementRoles = {
            SuperAdmin,
            Admin
        };

        /// <summary>
        /// 仓库运营角色（可以操作仓库数据）
        /// </summary>
        public static readonly string[] WarehouseRoles = {
            SuperAdmin,
            Admin,
            WarehouseManager,
            Operator
        };

        /// <summary>
        /// 只读角色
        /// </summary>
        public static readonly string[] ReadOnlyRoles = {
            ReadOnly
        };
    }
}