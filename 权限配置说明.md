# WMS-VGL 权限管理配置说明

## 修复的权限问题

### 1. ❌ StorageLocationsController 权限问题
**问题**: StorageLocationsController 没有配置任何权限注解，导致管理员无法访问储位列表
**修复**: 
- 添加了 `[Authorize(Policy = PolicyConstants.StorageLocationManagementPolicy)]` 到控制器类
- 为 GET 方法添加了 `[Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]` 注解

### 2. ❌ ReadOnlyPolicy 配置错误
**问题**: 在 Program.cs 中，ReadOnlyPolicy 使用了 `policy.RequireRole(RoleConstants.AllRoles)`，这要求用户同时拥有所有角色，而不是任何一个角色
**修复**: 改为明确列出每个角色：
```csharp
policy.RequireRole(
    RoleConstants.SuperAdmin,
    RoleConstants.Admin, 
    RoleConstants.WarehouseManager,
    RoleConstants.Operator,
    RoleConstants.ReadOnly
)
```

### 3. ❌ PostgreSQL DateTime 时区问题
**问题**: 创建用户时出现 `Cannot write DateTime with Kind=Local to PostgreSQL type 'timestamp with time zone'` 错误
**原因**: 代码中使用了 `DateTime.Now`（本地时间），但PostgreSQL的 `timestamptz` 字段只接受UTC时间
**修复**: 将所有控制器和服务中的 `DateTime.Now` 替换为 `DateTime.UtcNow`
- ✅ UsersController.cs - 用户创建和更新时间
- ✅ ESP32ControllersController.cs - 控制器创建、更新和心跳时间
- ✅ StorageLocationsController.cs - 储位创建和更新时间
- ✅ ESP32HealthCheckService.cs - 健康检查时间
- ✅ ESP32TestController.cs - 测试时间戳

### 4. ✅ JWT认证配置正确
- 中间件顺序正确：Authentication → Authorization
- JWT配置参数完整
- Token验证参数正确

## 权限策略配置

### 系统角色层次
```
SuperAdmin (超级管理员)
├── Admin (管理员)  
├── WarehouseManager (仓库管理员)
├── Operator (操作员)
└── ReadOnly (只读用户)
```

### 权限策略映射

| 策略 | 允许的角色 | 用途 |
|-----|-----------|------|
| SuperAdminPolicy | SuperAdmin | 系统配置、用户删除 |
| AdminPolicy | SuperAdmin, Admin | 用户管理 |
| WarehousePolicy | SuperAdmin, Admin, WarehouseManager | 仓库运营管理 |
| OperatorPolicy | SuperAdmin, Admin, WarehouseManager, Operator | 基础操作 |
| ReadOnlyPolicy | 所有角色 | 数据查看 |

### 功能模块策略

| 策略 | 允许的角色 | 控制器 |
|-----|-----------|--------|
| ESP32ManagementPolicy | SuperAdmin, Admin, WarehouseManager | ESP32ControllersController |
| StorageLocationManagementPolicy | SuperAdmin, Admin, WarehouseManager | StorageLocationsController |
| InventoryManagementPolicy | SuperAdmin, Admin, WarehouseManager, Operator | (未来的库存控制器) |
| UserManagementPolicy | SuperAdmin, Admin | UsersController |
| SystemConfigPolicy | SuperAdmin | 系统配置相关 |

## 控制器权限配置

### AuthController (认证控制器)
- 大部分端点无需认证 `[AllowAnonymous]`
- `/profile` 需要登录 `[Authorize]`

### UsersController (用户管理)
- 类级别: `[Authorize(Policy = PolicyConstants.UserManagementPolicy)]`
- 删除用户: `[Authorize(Policy = PolicyConstants.SuperAdminPolicy)]`

### ESP32ControllersController (ESP32控制器管理)
- 类级别: `[Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]`
- GET方法: `[Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]`

### StorageLocationsController (储位管理)
- 类级别: `[Authorize(Policy = PolicyConstants.StorageLocationManagementPolicy)]` ✅ **已修复**
- GET方法: `[Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]` ✅ **已修复**

## 默认用户凭据

```json
{
  "email": "<EMAIL>",
  "password": "Admin@123456",
  "roles": ["SuperAdmin"]
}
```

## Swagger JWT认证使用方法

### 1. 登录获取Token
```bash
POST /api/Auth/login
{
  "email": "<EMAIL>",
  "password": "Admin@123456"
}
```

### 2. 在Swagger中设置Token
1. 点击Swagger界面右上角的"Authorize"按钮
2. 在弹出窗口中输入: `Bearer your-access-token`
3. 点击"Authorize"确认

### 3. 测试权限
现在可以访问以下端点：
- ✅ `GET /api/StorageLocations` - 获取储位列表
- ✅ `GET /api/Users` - 获取用户列表  
- ✅ `POST /api/Users` - 创建用户
- ✅ `GET /api/ESP32Controllers` - 获取控制器列表

## 故障排查

### 1. 401 未授权错误
- 检查是否在Swagger中正确设置了Bearer Token
- 确认Token格式: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- 检查Token是否过期（默认1小时）

### 2. 403 禁止访问错误
- 检查用户角色是否有权限访问该端点
- 确认权限策略配置是否正确

### 3. Token格式检查
正确的Authorization header格式：
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.sample_signature
```

## 测试步骤

1. **重启应用** (确保权限配置生效)
2. **登录获取Token**
3. **在Swagger中设置Bearer Token** 
4. **测试各个端点访问权限**

所有权限问题现已修复，管理员用户应该能够正常访问StorageLocations列表和创建用户了。