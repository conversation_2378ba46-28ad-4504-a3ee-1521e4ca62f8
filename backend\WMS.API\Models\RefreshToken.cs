using System.ComponentModel.DataAnnotations;

namespace WMS.API.Models
{
    /// <summary>
    /// 刷新令牌实体类
    /// 用于存储用户的刷新令牌信息
    /// </summary>
    public class RefreshToken
    {
        /// <summary>
        /// 令牌ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 令牌值
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// 关联的用户ID
        /// </summary>
        [Required]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 关联的用户
        /// </summary>
        public virtual ApplicationUser User { get; set; } = null!;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// 是否已撤销
        /// </summary>
        public bool IsRevoked { get; set; } = false;

        /// <summary>
        /// 撤销时间
        /// </summary>
        public DateTime? RevokedAt { get; set; }

        /// <summary>
        /// 撤销原因
        /// </summary>
        [StringLength(200)]
        public string? RevokedReason { get; set; }

        /// <summary>
        /// 替换的新令牌ID（用于令牌轮换）
        /// </summary>
        public int? ReplacedByTokenId { get; set; }

        /// <summary>
        /// 替换的新令牌
        /// </summary>
        public virtual RefreshToken? ReplacedByToken { get; set; }

        /// <summary>
        /// 客户端IP地址
        /// </summary>
        [StringLength(50)]
        public string? ClientIpAddress { get; set; }

        /// <summary>
        /// 用户代理信息
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// 检查令牌是否有效
        /// </summary>
        public bool IsActive => !IsRevoked && DateTime.UtcNow < ExpiresAt;

        /// <summary>
        /// 检查令牌是否已过期
        /// </summary>
        public bool IsExpired => DateTime.UtcNow >= ExpiresAt;
    }
}