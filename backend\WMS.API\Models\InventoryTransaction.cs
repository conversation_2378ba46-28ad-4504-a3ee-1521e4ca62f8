using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    public enum TransactionType
    {
        Inbound = 0, // 入库
        Outbound = 1, // 出库
        Adjustment = 2, // 调整
        Transfer = 3, // 移库
        Freeze = 4, // 冻结
        Unfreeze = 5, // 解冻
    }

    public enum TransactionStatus
    {
        Pending = 0, // 待处理
        Completed = 1, // 已完成
        Cancelled = 2, // 已取消
        Failed = 3, // 处理失败
    }

    public class InventoryTransaction
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string TransactionNumber { get; set; } = string.Empty;

        public TransactionType Type { get; set; }

        public TransactionStatus Status { get; set; } = TransactionStatus.Pending;

        public int StorageLocationId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual StorageLocation? StorageLocation { get; set; }

        public int MaterialId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual Material? Material { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? BeforeQuantity { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal? AfterQuantity { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? LpnCode { get; set; }

        [StringLength(100)]
        public string? UniqueCode { get; set; }

        public DateTime? ExpiryDate { get; set; }

        // 关联业务单据
        public int? InboundOrderId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual InboundOrder? InboundOrder { get; set; }

        public int? OutboundOrderId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual OutboundOrder? OutboundOrder { get; set; }

        // 移库相关 - 目标储位
        public int? TargetStorageLocationId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual StorageLocation? TargetStorageLocation { get; set; }

        [StringLength(500)]
        public string? Reason { get; set; }

        [StringLength(200)]
        public string? Remarks { get; set; }

        [StringLength(100)]
        public string? OperatedBy { get; set; }

        public DateTime? OperatedAt { get; set; }

        [StringLength(100)]
        public string? ApprovedBy { get; set; }

        public DateTime? ApprovedAt { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // 条码解析相关
        [StringLength(500)]
        public string? BarcodeData { get; set; }

        [StringLength(100)]
        public string? ParsedBy { get; set; }

        public DateTime? ParsedAt { get; set; }

        // LED控制相关
        public bool LedActivated { get; set; } = false;

        public DateTime? LedActivatedAt { get; set; }

        [StringLength(20)]
        public string? LedColor { get; set; }
    }
}
