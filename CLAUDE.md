# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Git Commit Attribution

**重要规则：**
- 在进行Git提交时，**绝对不要**使用"Claude Code"或任何AI工具署名
- 提交信息应该以实际项目内容为主，不包含任何AI工具的署名信息
- 不要在提交信息中包含以下内容：
  - `🤖 Generated with [Claude Code](https://claude.ai/code)`
  - `Co-Authored-By: Claude <<EMAIL>>`
  - 任何其他AI工具相关的署名或标识
- 提交信息应该专注于描述代码变更的内容和目的

## Project Overview

WMS-VGL is a Visual Guided Logistics (VGL) warehouse management system backend API built with .NET 9 and Entity Framework Core. The system integrates with ESP32 controllers to manage WS2812 LED strips for visual storage location guidance.

## Architecture

### Core Components
- **WMS.API**: ASP.NET Core Web API (backend/WMS.API/)
- **Database**: PostgreSQL 15 with Entity Framework Core migrations
- **ESP32 Integration**: HTTP-based communication with ESP32 controllers
- **Background Services**: Health monitoring for ESP32 devices

### Data Models

#### Core Hierarchical Structure
- **Warehouse**: Top-level warehouse management with configuration inheritance
- **Zone**: Warehouse zones with temperature control, security levels, and access restrictions
- **Shelf**: Physical storage shelves with detailed specifications and safety certifications
- **StorageLocation**: Individual storage positions with LED strip position mapping

#### Supporting Models
- **ESP32Controller**: Hardware controller management with IP/port configuration
- **Material**: Product/material catalog
- **Inventory**: Stock tracking with batch and LPN support

#### Capability & Classification System
- **LocationCapability**: Flexible capability system for storage locations (temperature control, hazmat storage, etc.)
- **LocationClassification**: Multi-dimensional classification for business rules and material categorization

### Key Services
- **ESP32CommunicationService**: HTTP client for ESP32 LED control
- **ESP32HealthCheckService**: Device status monitoring
- **ESP32HealthCheckBackgroundService**: Automated health checks every 5 minutes

## Development Commands

**Important**: This Claude Code instance runs in WSL environment. The .NET SDK and Docker are only available on the Windows host machine. All `dotnet` and `docker` commands need to be executed on the host system, not within WSL.

### Running Commands on Windows Host from WSL

You can execute Windows PowerShell commands directly from WSL using the following methods:

```bash
# Method 1: Direct PowerShell execution
powershell.exe -Command "dotnet --version"

# Method 2: Running .NET commands with PowerShell
powershell.exe -Command "cd 'D:\\ProjestFiles\\ESP\\WMS-VGL\\backend\\WMS.API'; dotnet run --urls http://0.0.0.0:5000"

# Method 3: Running Docker commands
powershell.exe -Command "docker-compose up -d wms-postgres"

# Method 4: Checking Windows processes
powershell.exe -Command "Get-Process -Name dotnet"
powershell.exe -Command "Stop-Process -Name dotnet -Force"

# Method 5: Network troubleshooting
powershell.exe -Command "Get-NetTCPConnection -LocalPort 5000"
```

**Important Notes:**
- Use Windows-style paths with double backslashes: `D:\\ProjestFiles\\ESP\\WMS-VGL`
- When binding .NET applications, use `0.0.0.0` instead of `localhost` to make them accessible from WSL
- WSL can access Windows host services via the gateway IP (usually 172.x.x.1)
- Get WSL gateway IP: `ip route show | grep default`

### Database Management
```bash
# Navigate to API project (run on Windows host)
cd backend/WMS.API

# Create new migration (run on Windows host)
dotnet ef migrations add [MigrationName]

# Apply migrations (run on Windows host)
dotnet ef database update

# Remove last migration (run on Windows host)
dotnet ef migrations remove
```

### Running the Application
```bash
# Start database only (run via PowerShell from WSL)
powershell.exe -Command "cd 'D:\\ProjestFiles\\ESP\\WMS-VGL'; docker-compose up -d wms-postgres"

# Run API locally (requires .NET 9 SDK on Windows host)
# IMPORTANT: Use 0.0.0.0 to make it accessible from WSL
powershell.exe -Command "cd 'D:\\ProjestFiles\\ESP\\WMS-VGL\\backend\\WMS.API'; dotnet run --urls http://0.0.0.0:5000"

# Run full stack with Docker (run via PowerShell from WSL)
powershell.exe -Command "cd 'D:\\ProjestFiles\\ESP\\WMS-VGL'; docker-compose up -d"

# Access from WSL using gateway IP
# First get gateway IP: ip route show | grep default
# Then access: http://************:5000 (replace with your gateway IP)
```

### Docker Operations
```bash
# Build API image (run on Windows host)
docker-compose build wms-api

# View logs (run on Windows host)
docker-compose logs -f wms-api
docker-compose logs -f wms-postgres

# Reset database (run on Windows host)
docker-compose down -v
docker-compose up -d wms-postgres
```

### Testing & Debugging
```bash
# Run comprehensive API tests (requires running API server)
./test_apis.sh

# Debug shelf inspection functionality  
./debug_shelf_inspection.sh
```

### Deployment
```bash
# Prepare offline deployment package
./deploy/prepare-offline.sh      # Linux/macOS
./deploy/prepare-offline-windows.bat  # Windows

# Deploy to offline environment
./deploy/deploy-offline.sh       # Linux/macOS
./deploy/deploy-offline-windows.bat   # Windows

# Test deployment
./deploy/test-deployment.sh      # Linux/macOS
./deploy/test-deployment-windows.bat  # Windows
```

### API Testing with Postman

#### Collection Files
- **API Collection**: `WMS-VGL-API.postman_collection.json`
- **Environment**: `WMS-VGL-Environment.postman_environment.json`

#### Quick Setup
1. Import both files into Postman
2. Select "WMS-VGL Development Environment"
3. Run "01 - 用户认证 > 管理员登录" to get authentication token
4. Test hierarchical APIs: Warehouses → Zones → Shelves → Storage Locations

#### Environment Variables
- `baseUrl`: http://localhost:5000 (local) or http://************:5000 (WSL)
- `baseUrlWSL`: Pre-configured WSL gateway URL
- `accessToken`: Auto-set after login
- `adminEmail`: <EMAIL>
- `adminPassword`: Admin@123456

### Checkpoint Management (Version Control)

Claude Code doesn't have built-in checkpoints, but you can use Git Stash for quick checkpoint functionality:

```bash
# Create a checkpoint (save current work state)
git stash push -m "Checkpoint: 描述当前工作状态"

# Create checkpoint with timestamp
git stash push -m "Checkpoint: $(date '+%Y-%m-%d %H:%M:%S') - 功能描述"

# List all checkpoints
git stash list

# Apply checkpoint (restore without removing from stash)
git stash apply stash@{0}

# Restore checkpoint (apply and remove from stash)
git stash pop stash@{0}

# View checkpoint details
git stash show stash@{0}
git stash show -p stash@{0}  # Show full diff

# Delete specific checkpoint
git stash drop stash@{0}

# Clear all checkpoints
git stash clear
```

**Checkpoint Workflow:**
1. Before making significant changes: `git stash push -m "Checkpoint: 修改前的稳定状态"`
2. Make your changes and test
3. If changes work well: commit normally with `git add . && git commit -m "描述"`
4. If changes have issues: `git stash pop` to restore previous state
5. Regular cleanup: `git stash clear` to remove old checkpoints

**Note**: Git stash is perfect for temporary checkpoints during development. For permanent version control, use regular Git commits and tags.

## Configuration

### Database Connection
- **Host**: localhost:5434 (development), wms-postgres:5432 (Docker)
- **Database**: wms_vgl_db
- **User**: wms_admin
- **Connection String**: Configured in appsettings.json

### ESP32 Communication
- **Protocols**: HTTP REST API
- **Endpoints**: `/controlLEDsV2`, `/setAllLeds`, `/getDeviceParams`
- **Timeout**: 10 seconds
- **Health Check Interval**: 5 minutes

### Application URLs
- **API**: http://localhost:5000 (development), http://localhost:8080 (Docker)
- **Swagger**: /swagger endpoint
- **Database**: localhost:5434

### Launch Profiles & URLs

#### Available Profiles (launchSettings.json)
- **http**: `http://localhost:5000` (Development)
- **https**: `https://localhost:7279` + `http://localhost:5246`
- **WSL**: `https://localhost:7279` + `http://localhost:5246` (WSL2 environment)

#### Environment URLs
- **Local Development**: http://localhost:5000
- **WSL Development**: http://************:5000 (via gateway)
- **Docker**: http://localhost:8080
- **Database**: localhost:5434 (PostgreSQL)

## Project Structure Notes

### Controllers
- **ESP32ControllersController**: Device CRUD and connection testing
- **StorageLocationsController**: Storage location management with Excel import/export
- **WarehousesController**: Warehouse hierarchy management (if implemented)
- **ZonesController**: Zone management with environmental controls (if implemented)
- **ShelvesController**: Shelf management with safety specifications (if implemented)
- **ESP32TestController**: Hardware testing endpoints
- **TestController**: Development testing utilities
- **BarcodeTestController**: Barcode parsing testing

### Test Controllers
- **TestController**: `/api/Test` - Test data creation and cleanup
- **ESP32TestController**: `/api/ESP32Test` - ESP32 device testing
- **BarcodeTestController**: `/api/BarcodeTest` - Barcode parsing testing

### Key Features
- UTF-8 encoding support for Chinese characters
- Excel import/export with EPPlus (non-commercial license)
- **Hierarchical Configuration System**: Warehouse → Zone → Shelf → StorageLocation inheritance
- **Hybrid Capability Model**: Direct foreign keys + flexible capability system for multi-industry support
- **Multi-dimensional Classification**: Industry-specific categorization (medical, e-commerce, manufacturing, food)
- **Backward Compatibility**: Legacy field support for existing systems
- Background health monitoring
- CORS enabled for all origins
- Automatic database migration on startup
- JSON serialization with camelCase naming
- Role-based access control (RBAC) with ASP.NET Core Identity
- Multi-level permission system (SuperAdmin, Admin, WarehouseManager, Operator, ReadOnly)
- User management and authentication system

### ESP32 Hardware Integration
- **Device Type**: ESP32 with WS2812 LED strips
- **Communication**: HTTP REST API
- **LED Control**: Position-based control with color/brightness
- **Health Check**: Automatic device status monitoring
- **Supported Colors**: red, green, blue, yellow, white, black, cyan, magenta, custom hex

## Authentication & Authorization System

### JWT Authentication Implementation
- **Framework**: ASP.NET Core Identity + JWT Bearer Token
- **Database**: PostgreSQL with Identity tables and custom RefreshToken table
- **Token Types**: 
  - Access Token (1 hour validity)
  - Refresh Token (7 days validity with rotation)
- **Security Features**: Role-based access control, token expiration, automatic token refresh

### System Roles
- **SuperAdmin**: System administrator with full access
- **Admin**: User management and system configuration
- **WarehouseManager**: Warehouse operations management
- **Operator**: Basic warehouse operations
- **ReadOnly**: View-only access to all data

### Default Credentials
- **Email**: <EMAIL>
- **Password**: Admin@123456
- **⚠️ Change default password immediately after first login!**

### JWT Authentication Endpoints
```bash
# Login (get JWT tokens)
POST /api/Auth/login
{
  "email": "<EMAIL>",
  "password": "Admin@123456",
  "rememberMe": true
}

# Refresh tokens
POST /api/Auth/refresh-token
{
  "refreshToken": "your-refresh-token"
}

# Logout (revoke tokens)
POST /api/Auth/logout
{
  "refreshToken": "your-refresh-token"
}

# Get current user profile
GET /api/Auth/profile
Authorization: Bearer your-access-token
```

### Using JWT Tokens
```bash
# Include Bearer token in Authorization header for all protected endpoints
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
     http://localhost:5000/api/ESP32Controllers
```

### Permission Policies
- **ESP32ManagementPolicy**: WarehouseManager and above
- **StorageLocationManagementPolicy**: WarehouseManager and above
- **InventoryManagementPolicy**: Operator and above
- **UserManagementPolicy**: Admin and above
- **SystemConfigPolicy**: SuperAdmin only
- **ReadOnlyPolicy**: All authenticated users

### API Endpoints
- **Authentication**: `/api/Auth/login`, `/api/Auth/logout`, `/api/Auth/profile`
- **User Management**: `/api/Users` (CRUD operations)
- **Password Management**: `/api/Auth/change-password`, `/api/Users/<USER>/reset-password`

## Development Notes

### Entity Framework & Database
- Uses PostgreSQL 15 with Entity Framework Core 9
- **DateTime Configuration**: Uses `timestamptz` (timestamp with time zone) for all DateTime fields
- **CRITICAL**: Always use `DateTime.UtcNow` instead of `DateTime.Now` to avoid timezone conflicts
- Automatic migrations on application startup
- Decimal precision for inventory quantities (18,3)
- Foreign key constraints with RESTRICT delete behavior
- Identity tables for user management and JWT refresh token storage

### PostgreSQL DateTime Best Practices

**⚠️ IMPORTANT: PostgreSQL DateTime Timezone Issue**

PostgreSQL has strict rules about DateTime types that can cause runtime errors:

#### The Problem
```csharp
// ❌ WRONG - This will cause runtime exception:
entity.Property(e => e.CreatedAt).HasColumnType("timestamp");  // timestamp without time zone
someEntity.CreatedAt = DateTime.UtcNow;  // Kind = UTC

// Error: Cannot write DateTime with Kind=UTC to PostgreSQL type 'timestamp without time zone'
```

#### The Solution
```csharp
// ✅ CORRECT - Use timestamptz for UTC DateTime:
entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");  // timestamp with time zone
someEntity.CreatedAt = DateTime.UtcNow;  // Kind = UTC - Works perfectly!
```

#### DateTime Type Mapping Rules
| PostgreSQL Type | .NET DateTime.Kind | Usage |
|----------------|-------------------|-------|
| `timestamp` | `Local` or `Unspecified` | Single timezone applications |
| `timestamptz` | `UTC` | Multi-timezone applications (recommended) |

#### Code Standards
```csharp
// ✅ Always use UTC in models and services
public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

// ✅ Database configuration
entity.Property(e => e.CreatedAt).HasColumnType("timestamptz");
entity.Property(e => e.UpdatedAt).HasColumnType("timestamptz");

// ❌ Never use local time in multi-user applications
public DateTime CreatedAt { get; set; } = DateTime.Now;  // Will cause errors!
```

#### Migration Commands for DateTime Fixes
```bash
# If you encounter DateTime timezone errors:
dotnet ef migrations add FixDateTimeTimezone
dotnet ef database update

# This will convert all timestamp columns to timestamptz
```

**Why This Matters:**
- Prevents runtime exceptions during entity saves
- Ensures consistent time handling across different timezones
- Supports global applications with users in multiple timezones
- Follows PostgreSQL and .NET best practices

### Background Services
- ESP32HealthCheckBackgroundService runs health checks every 5 minutes
- Logs device status changes
- Updates LastHeartbeat timestamps
- DataSeedService initializes default roles and admin user

### Logging
- Structured logging with Microsoft.Extensions.Logging
- ESP32 communication requests/responses logged at Information level
- Error logging for failed device communications
- User authentication and authorization events logged

### Additional Documentation

#### Dev Resources Directory
- **ESP32 API接口文档.md**: Complete ESP32 device API reference
- **系统需求说明文档.md**: System requirements and specifications
- **权限系统配置说明.md**: Permission system configuration guide
- **条码解析系统测试文档.md**: Barcode parsing system testing
- **仓库层级结构系统测试文档.md**: Hierarchical structure testing
- **WMS条码解析业务流程设计.md**: Barcode parsing business process design

## Hierarchical Data Architecture

### Overview
The system implements a hybrid capability model combining traditional hierarchical relationships with flexible capability and classification systems. This design supports multi-industry requirements (medical, e-commerce, manufacturing, food) while maintaining performance and simplicity.

### Hierarchy Structure
```
Warehouse (仓库)
└── Zone (区域)
    └── Shelf (货架)
        └── StorageLocation (储位)
            ├── LocationCapability (能力)
            └── LocationClassification (分类)
```

### Data Models Detail

#### Warehouse Model
- **Purpose**: Top-level container for entire warehouse facility
- **Key Fields**: Code, Name, Address, WarehouseType, Status, Configuration
- **Features**: Global configuration inheritance, multi-tenant support
- **JSON Configuration**: Industry-specific rules and policies

#### Zone Model  
- **Purpose**: Logical/physical areas within warehouse with specific characteristics
- **Key Fields**: Code, Name, ZoneType, Status, TemperatureRange, SecurityLevel
- **Features**: Environmental controls, access restrictions, capacity management
- **Types**: Ambient, Refrigerated, Frozen, Hazmat, HighSecurity, Receiving, Shipping

#### Shelf Model
- **Purpose**: Physical storage infrastructure with detailed specifications
- **Key Fields**: Code, Name, ShelfType, Dimensions, MaxWeight, SafetyCertifications
- **Features**: Safety compliance, maintenance tracking, capacity planning
- **Types**: Standard, Heavy, Cantilever, Flow, Drive-in, Push-back

#### StorageLocation Model (Enhanced)
- **Purpose**: Individual storage positions with LED control and flexible attributes
- **Core Fields**: Code, Name, ShelfId, ESP32ControllerId, LED positions
- **Enhanced Fields**: Capacity, MaxWeight, Dimensions, Configuration, Properties
- **Compatibility Fields**: Zone, ShelfCode (for backward compatibility)
- **Features**: LED position mapping, capability assignments, multi-dimensional classification

#### LocationCapability Model
- **Purpose**: Define what a storage location can do (capabilities)
- **Key Fields**: CapabilityType, CapabilityLevel, Parameters, ValidationRules
- **Capability Types**: 
  - TemperatureControl: Specific temperature requirements
  - HazmatStorage: Hazardous material certifications
  - PreciousItems: High-value item security
  - FragileGoods: Special handling requirements
  - BiologicalSamples: Medical/lab storage
  - FoodGrade: Food safety compliance
- **Features**: Time-based validity, certification tracking, parameter validation

#### LocationClassification Model
- **Purpose**: Multi-dimensional categorization for business rules
- **Key Fields**: Dimension, Category, Value, Properties, BusinessRules
- **Classification Dimensions**:
  - Industry: Medical, Ecommerce, Manufacturing, Food
  - MaterialType: Raw, Finished, Packaging, Tools
  - Security: Standard, Restricted, Controlled, Classified
  - Handling: Normal, Fragile, Heavy, Hazmat
  - Temperature: Ambient, Cold, Frozen, Heated
- **Features**: Inheritance support, rule-based assignment, external system integration

### Configuration Inheritance

#### Hierarchy Levels (Priority Order)
1. **StorageLocation Level**: Most specific, highest priority
2. **Shelf Level**: Applied to all locations on the shelf
3. **Zone Level**: Applied to all shelves in the zone  
4. **Warehouse Level**: Global defaults, lowest priority

#### Configuration Resolution Process
1. Check StorageLocation.Configuration for specific setting
2. If not found, check parent Shelf.Configuration
3. If not found, check parent Zone.Configuration
4. If not found, use Warehouse.Configuration default
5. Log configuration resolution path for audit

#### Industry-Specific Configuration Examples

**Medical Industry Configuration:**
```json
{
  "temperatureMonitoring": true,
  "temperatureRange": {"min": 2, "max": 8, "unit": "celsius"},
  "auditTrail": "full",
  "accessControl": "biometric",
  "regulatoryCompliance": ["FDA", "GMP", "ISO13485"],
  "alerting": {
    "temperatureDeviation": "immediate",
    "accessViolation": "immediate",
    "inventoryDiscrepancy": "daily"
  }
}
```

**E-commerce Configuration:**
```json
{
  "pickingOptimization": true,
  "batchPicking": true,
  "autoReplenishment": true,
  "seasonalAdjustment": true,
  "kitting": ["enabled", "rules"],
  "shippingPriority": ["same_day", "next_day", "standard"],
  "returnProcessing": "streamlined"
}
```

### Migration Strategy

#### Default Hierarchy Creation
- Migration creates default Warehouse (ID: 1, Code: "DEFAULT")
- Creates default Zone (ID: 1, Code: "DEFAULT") 
- Creates default Shelf (ID: 1, Code: "DEFAULT")
- Links existing StorageLocations to default Shelf
- Fills compatibility fields for backward compatibility

#### Backward Compatibility
- Existing controllers continue to work unchanged
- Legacy fields (Zone, ShelfCode) maintained
- API endpoints preserve existing functionality
- Excel import/export supports both old and new fields

### Database Design Principles

#### Performance Optimizations
- Direct foreign key relationships for fast joins
- Indexed unique constraints on hierarchical codes
- JSON columns for flexible configuration storage
- Optimized query patterns for configuration resolution

#### Data Integrity
- Foreign key constraints with RESTRICT delete behavior
- Unique constraints on hierarchical code combinations
- DateTime fields use timestamptz for timezone consistency
- Proper null handling for optional relationships

#### Extensibility Features
- JSON configuration fields for custom properties
- Capability system for new functionality
- Classification system for business rules
- Audit trail support for compliance requirements

## API Endpoints (Enhanced)

### Hierarchical Management Endpoints
```bash
# Warehouse Management
GET /api/Warehouses
POST /api/Warehouses
GET /api/Warehouses/{id}
PUT /api/Warehouses/{id}
DELETE /api/Warehouses/{id}

# Zone Management  
GET /api/Zones
GET /api/Zones/by-warehouse/{warehouseId}
POST /api/Zones
PUT /api/Zones/{id}
DELETE /api/Zones/{id}

# Shelf Management
GET /api/Shelves
GET /api/Shelves/by-zone/{zoneId}
POST /api/Shelves
PUT /api/Shelves/{id}
DELETE /api/Shelves/{id}

# Enhanced StorageLocation Management
GET /api/StorageLocations/by-shelf/{shelfId}
GET /api/StorageLocations/by-capability/{capabilityType}
GET /api/StorageLocations/by-classification/{dimension}/{category}
POST /api/StorageLocations/{id}/capabilities
POST /api/StorageLocations/{id}/classifications
```

### Configuration Management Endpoints
```bash
# Configuration Resolution
GET /api/Configuration/resolve/{storageLocationId}
GET /api/Configuration/hierarchy/{storageLocationId}

# Bulk Configuration Operations
POST /api/Configuration/apply-to-zone/{zoneId}
POST /api/Configuration/apply-to-shelf/{shelfId}
PUT /api/Configuration/warehouse/{warehouseId}
```

# Frontend Development Plan

## Overview
WMS-VGL 网页端基于原型图开发，使用 Vue 3 + TypeScript + Vite 构建现代化仓库管理系统前端界面。

## Technology Stack
- **Frontend Framework**: Vue 3 + TypeScript
- **Build Tool**: Vite
- **UI Library**: Ant Design Vue
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **HTTP Client**: Axios
- **Styling**: Less/SCSS + Ant Design Theme

## Project Structure
```
frontend/
├── src/
│   ├── components/          # 通用组件
│   │   ├── common/         # 基础组件
│   │   ├── forms/          # 表单组件
│   │   └── charts/         # 图表组件
│   ├── views/              # 页面组件
│   │   ├── auth/           # 认证相关
│   │   ├── dashboard/      # 首页仪表板
│   │   ├── warehouse/      # 仓库管理
│   │   ├── inventory/      # 库存管理
│   │   ├── operations/     # 作业管理
│   │   ├── hardware/       # 硬件管理
│   │   └── users/          # 用户管理
│   ├── api/                # API接口
│   ├── stores/             # Pinia状态管理
│   ├── router/             # 路由配置
│   ├── utils/              # 工具函数
│   ├── types/              # TypeScript类型定义
│   └── assets/             # 静态资源
├── public/
└── package.json
```

## Core Modules

### 1. Authentication System
- JWT login/logout
- Role-based access control (5-level permission system)
- Route guards
- Automatic token refresh

### 2. Warehouse Management (4-level hierarchy)
- **Warehouse List**: Warehouse CRUD, status management
- **Zone Management**: Temperature control, security level configuration
- **Shelf Management**: Specifications, weight capacity management
- **Storage Location Management**: LED mapping, capability classification, Excel import/export

### 3. Inventory Management
- **Real-time Inventory Query**: Barcode scanning, multi-dimensional search
- **Inventory Statistics**: Chart visualization, category statistics
- **Inventory Alerts**: Exception monitoring, automatic reminders

### 4. Operations Management
- **Inbound Operations**: PDA-optimized interface, storage recommendation, LED guidance
- **Outbound Operations**: Batch management, FIFO/LIFO strategy
- **Transfer Operations**: Dual storage location LED synchronization
- **Picking Operations**: Path optimization, batch picking
- **Stocktaking Operations**: Discrepancy handling, report generation

### 5. Hardware Management
- **ESP32 Devices**: Device list, connection testing, health monitoring
- **LED Control**: Real-time control, color/brightness adjustment, preview function
- **Device Monitoring**: Status dashboard, alarm management

### 6. User Management
- **User Accounts**: CRUD operations, role assignment
- **Permission Management**: Role permission matrix
- **Operation Logs**: Audit trail, log queries

## Responsive Design
- **Desktop**: Full-featured interface
- **Mobile/PDA**: Optimized operation interface
- **Touch-friendly**: Compatible with barcode scanners and touch operations

## Development Phases

### Phase 1: Basic Framework
1. ✅ Project structure creation
2. ✅ Authentication system implementation
3. ✅ Routing and permission control
4. ✅ API interface encapsulation

### Phase 2: Core Module Development
5. 🔄 Warehouse management module (4-level hierarchy)
6. 🔄 Inventory management module
7. 🔄 Operations management module

### Phase 3: Hardware Integration
8. 🔄 ESP32 device management
9. 🔄 LED control interface
10. 🔄 Real-time monitoring features

### Phase 4: User Experience Optimization
11. 🔄 User management enhancement
12. 🔄 Performance optimization and testing
13. 🔄 Mobile adaptation

## UI/UX Design Principles
- Follow Ant Design specifications
- Unified color system and component style
- Intuitive breadcrumb navigation
- Real-time status feedback
- Smooth page transition animations
- Accessibility support

## Development Commands

### Frontend Setup
```bash
# Navigate to project root
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Type checking
npm run type-check

# Lint and fix
npm run lint

# Format code
npm run format
```

### Environment Configuration
```bash
# Development (connects to local API)
VITE_API_BASE_URL=http://localhost:5000/api
VITE_API_WSL_URL=http://************:5000/api

# Production
VITE_API_BASE_URL=/api
```

### Development URLs
- **Frontend Dev Server**: http://localhost:3000
- **API Backend**: http://localhost:5000 (Windows) or http://************:5000 (WSL)
- **Swagger UI**: http://localhost:5000/swagger

## Backend Integration Notes
- Uses JWT authentication with automatic token refresh
- Follows RESTful API design patterns
- Implements proper error handling and loading states
- Supports file upload/download for Excel operations
- Real-time updates for ESP32 device status