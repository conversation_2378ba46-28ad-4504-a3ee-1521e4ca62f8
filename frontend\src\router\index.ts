import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { RouteRecordRaw } from 'vue-router'
import AppLayout from '@/components/common/AppLayout.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/',
    component: AppLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/DashboardView.vue'),
      },
      {
        path: 'warehouse',
        name: 'Warehouse',
        meta: { permission: 'warehouse_management' },
        children: [
          {
            path: '',
            name: 'WarehouseList',
            component: () => import('@/views/warehouse/WarehouseList.vue'),
          },
          {
            path: 'zones',
            name: 'ZoneList',
            component: () => import('@/views/warehouse/ZoneList.vue'),
          },
          {
            path: 'shelves',
            name: 'ShelfList',
            component: () => import('@/views/warehouse/ShelfList.vue'),
          },
          {
            path: 'storage-locations',
            name: 'StorageLocationList',
            component: () => import('@/views/warehouse/StorageLocationList.vue'),
          },
        ],
      },
      {
        path: 'inventory',
        name: 'Inventory',
        meta: { permission: 'inventory_management' },
        children: [
          {
            path: '',
            name: 'InventoryList',
            component: () => import('@/views/inventory/InventoryList.vue'),
          },
          {
            path: 'statistics',
            name: 'InventoryStatistics',
            component: () => import('@/views/inventory/InventoryStatistics.vue'),
          },
          {
            path: 'alerts',
            name: 'InventoryAlerts',
            component: () => import('@/views/inventory/InventoryAlerts.vue'),
          },
        ],
      },
      {
        path: 'operations',
        name: 'Operations',
        meta: { permission: 'operations_management' },
        children: [
          {
            path: '',
            name: 'OperationsList',
            component: () => import('@/views/operations/OperationsList.vue'),
          },
          {
            path: 'inbound',
            name: 'InboundOperations',
            component: () => import('@/views/operations/InboundOperations.vue'),
          },
          {
            path: 'outbound',
            name: 'OutboundOperations',
            component: () => import('@/views/operations/OutboundOperations.vue'),
          },
          {
            path: 'transfer',
            name: 'TransferOperations',
            component: () => import('@/views/operations/TransferOperations.vue'),
          },
          {
            path: 'picking',
            name: 'PickingOperations',
            component: () => import('@/views/operations/PickingOperations.vue'),
          },
          {
            path: 'stocktaking',
            name: 'StocktakingOperations',
            component: () => import('@/views/operations/StocktakingOperations.vue'),
          },
        ],
      },
      {
        path: 'hardware',
        name: 'Hardware',
        meta: { permission: 'esp32_management' },
        children: [
          {
            path: '',
            name: 'ESP32List',
            component: () => import('@/views/hardware/ESP32List.vue'),
          },
          {
            path: 'monitoring',
            name: 'DeviceMonitoring',
            component: () => import('@/views/hardware/DeviceMonitoring.vue'),
          },
          {
            path: 'led-control',
            name: 'LEDControl',
            component: () => import('@/views/hardware/LEDControl.vue'),
          },
        ],
      },
      {
        path: 'users',
        name: 'Users',
        meta: { permission: 'user_management' },
        children: [
          {
            path: '',
            name: 'UserList',
            component: () => import('@/views/users/UserList.vue'),
          },
          {
            path: 'roles',
            name: 'RoleList',
            component: () => import('@/views/users/RoleList.vue'),
          },
          {
            path: 'logs',
            name: 'OperationLogs',
            component: () => import('@/views/users/OperationLogs.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/common/NotFound.vue'),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Check if route requires authentication
  if (to.meta.requiresAuth === false) {
    // If user is already authenticated and trying to access login, redirect to dashboard
    if (to.name === 'Login' && authStore.isAuthenticated) {
      return next({ name: 'Dashboard' })
    }
    return next()
  }
  
  // Check authentication
  if (!authStore.isAuthenticated) {
    return next({ 
      name: 'Login', 
      query: { redirect: to.fullPath } 
    })
  }
  
  // If user info is not loaded, fetch it
  if (!authStore.user) {
    try {
      await authStore.fetchProfile()
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
      authStore.clearAuth()
      return next({ name: 'Login' })
    }
  }
  
  // Check permissions
  if (to.meta.permission && !authStore.hasPermission(to.meta.permission as string)) {
    return next({ name: 'Dashboard' })
  }
  
  next()
})

export default router