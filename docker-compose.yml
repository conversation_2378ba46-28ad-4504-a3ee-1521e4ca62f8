version: '3.8'

services:
  # PostgreSQL 数据库服务 - 使用非标准端口避免冲突
  wms-postgres:
    image: postgres:15-alpine
    container_name: wms-postgres
    environment:
      POSTGRES_DB: wms_vgl_db
      POSTGRES_USER: wms_admin
      POSTGRES_PASSWORD: WMS@2025!Secure
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C.UTF-8 --lc-ctype=C.UTF-8"
      LC_ALL: C.UTF-8
      LANG: C.UTF-8
    ports:
      - "5434:5432"  # 映射到5434端口避免与本机PG冲突
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - wms-network
    restart: unless-stopped

  # WMS API 服务
  wms-api:
    build:
      context: ./backend/WMS.API
      dockerfile: Dockerfile
    container_name: wms-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=wms-postgres;Port=5432;Database=wms_vgl_db;Username=wms_admin;Password=WMS@2025!Secure
    ports:
      - "8080:8080"
    depends_on:
      - wms-postgres
    networks:
      - wms-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  wms-network:
    driver: bridge