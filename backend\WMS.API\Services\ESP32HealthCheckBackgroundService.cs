namespace WMS.API.Services
{
    public class ESP32HealthCheckBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ESP32HealthCheckBackgroundService> _logger;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5); // 每5分钟检查一次

        public ESP32HealthCheckBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<ESP32HealthCheckBackgroundService> logger
        )
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("ESP32 Health Check Background Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var healthCheckService =
                        scope.ServiceProvider.GetRequiredService<IESP32HealthCheckService>();

                    await healthCheckService.CheckAllControllersHealthAsync();

                    _logger.LogDebug("ESP32 controllers health check completed");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during ESP32 health check");
                }

                await Task.Delay(_checkInterval, stoppingToken);
            }

            _logger.LogInformation("ESP32 Health Check Background Service stopped");
        }
    }
}
