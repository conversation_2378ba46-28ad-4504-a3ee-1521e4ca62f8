// @import '~ant-design-vue/dist/reset.css';

:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --text-color: #000000d9;
  --text-color-secondary: #00000073;
  --background-color: #f0f2f5;
  --component-background: #ffffff;
  --border-color: #d9d9d9;
  --border-radius: 6px;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5715;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: var(--component-background);
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  color: var(--text-color);
}

.page-content {
  flex: 1;
  padding: 0 24px 24px;
}

.card-container {
  background: var(--component-background);
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
}

.table-container {
  background: var(--component-background);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.search-form {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-tag.success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-tag.warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.status-tag.error {
  background: #fff2f0;
  color: #f5222d;
  border: 1px solid #ffccc7;
}

.status-tag.info {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

@media (max-width: 768px) {
  .page-content {
    padding: 0 16px 16px;
  }
  
  .card-container {
    padding: 16px;
  }
  
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
}