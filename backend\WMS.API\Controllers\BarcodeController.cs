using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WMS.API.DTOs.Barcode;
using WMS.API.Services;

namespace WMS.API.Controllers
{
    /// <summary>
    /// 条码解析控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class BarcodeController : ControllerBase
    {
        private readonly IBarcodeParsingService _barcodeParsingService;
        private readonly ILogger<BarcodeController> _logger;

        public BarcodeController(
            IBarcodeParsingService barcodeParsingService,
            ILogger<BarcodeController> logger
        )
        {
            _barcodeParsingService = barcodeParsingService;
            _logger = logger;
        }

        /// <summary>
        /// 解析条码
        /// </summary>
        /// <param name="request">条码解析请求</param>
        /// <returns>解析结果</returns>
        [HttpPost("parse")]
        public async Task<ActionResult<BarcodeParsingResult>> ParseBarcode(
            [FromBody] BarcodeParsingRequest request
        )
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Barcode))
                {
                    return BadRequest("条码不能为空");
                }

                // 如果没有提供客户ID，从用户信息中获取
                if (string.IsNullOrEmpty(request.Context.ClientId))
                {
                    // TODO: 从JWT token或用户信息中获取客户ID
                    request.Context.ClientId = "DEFAULT_CLIENT";
                }

                // 如果没有提供用户ID，从当前用户获取
                if (string.IsNullOrEmpty(request.Context.UserId))
                {
                    request.Context.UserId = User.Identity?.Name;
                }

                var result = await _barcodeParsingService.ParseBarcodeAsync(
                    request.Barcode,
                    request.Context
                );

                if (!result.IsValid)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "条码解析时发生错误: {Barcode}", request.Barcode);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 批量解析条码
        /// </summary>
        /// <param name="request">批量条码解析请求</param>
        /// <returns>批量解析结果</returns>
        [HttpPost("parse-batch")]
        public async Task<ActionResult<BatchBarcodeParsingResult>> ParseBarcodesBatch(
            [FromBody] BatchBarcodeParsingRequest request
        )
        {
            try
            {
                if (request.Barcodes == null || !request.Barcodes.Any())
                {
                    return BadRequest("条码列表不能为空");
                }

                if (request.Barcodes.Count > 1000)
                {
                    return BadRequest("单次批量处理条码数量不能超过1000个");
                }

                // 如果没有提供客户ID，从用户信息中获取
                if (string.IsNullOrEmpty(request.Context.ClientId))
                {
                    request.Context.ClientId = "DEFAULT_CLIENT";
                }

                // 如果没有提供用户ID，从当前用户获取
                if (string.IsNullOrEmpty(request.Context.UserId))
                {
                    request.Context.UserId = User.Identity?.Name;
                }

                var result = await _barcodeParsingService.ParseBarcodesAsync(
                    request.Barcodes,
                    request.Context,
                    request.ContinueOnError
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量条码解析时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 解析条码并进行验证
        /// </summary>
        /// <param name="request">条码解析请求</param>
        /// <returns>解析和验证结果</returns>
        [HttpPost("parse-and-validate")]
        public async Task<ActionResult<BarcodeParsingResult>> ParseAndValidateBarcode(
            [FromBody] BarcodeParsingRequest request
        )
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Barcode))
                {
                    return BadRequest("条码不能为空");
                }

                // 如果没有提供客户ID，从用户信息中获取
                if (string.IsNullOrEmpty(request.Context.ClientId))
                {
                    request.Context.ClientId = "DEFAULT_CLIENT";
                }

                if (string.IsNullOrEmpty(request.Context.UserId))
                {
                    request.Context.UserId = User.Identity?.Name;
                }

                var result = await _barcodeParsingService.ParseAndValidateAsync(
                    request.Barcode,
                    request.Context
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "条码解析和验证时发生错误: {Barcode}", request.Barcode);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 验证唯一码
        /// </summary>
        /// <param name="uniqueCode">唯一码</param>
        /// <param name="materialId">物料ID</param>
        /// <returns>验证结果</returns>
        [HttpGet("validate-unique-code")]
        public async Task<ActionResult<bool>> ValidateUniqueCode(
            [FromQuery] string uniqueCode,
            [FromQuery] int materialId
        )
        {
            try
            {
                if (string.IsNullOrWhiteSpace(uniqueCode))
                {
                    return BadRequest("唯一码不能为空");
                }

                if (materialId <= 0)
                {
                    return BadRequest("无效的物料ID");
                }

                var isValid = await _barcodeParsingService.ValidateUniqueCodeAsync(
                    uniqueCode,
                    materialId
                );
                return Ok(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "验证唯一码时发生错误: {UniqueCode}, MaterialId: {MaterialId}",
                    uniqueCode,
                    materialId
                );
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 注册唯一码
        /// </summary>
        /// <param name="request">注册请求</param>
        /// <returns>注册结果</returns>
        [HttpPost("register-unique-code")]
        public async Task<ActionResult<bool>> RegisterUniqueCode(
            [FromBody] RegisterUniqueCodeRequest request
        )
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.UniqueCode))
                {
                    return BadRequest("唯一码不能为空");
                }

                if (request.MaterialId <= 0)
                {
                    return BadRequest("无效的物料ID");
                }

                var userId = User.Identity?.Name;
                var success = await _barcodeParsingService.RegisterUniqueCodeAsync(
                    request.UniqueCode,
                    request.MaterialId,
                    request.StorageLocationId,
                    userId
                );

                if (!success)
                {
                    return BadRequest("唯一码注册失败，可能已存在或被其他物料使用");
                }

                return Ok(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册唯一码时发生错误: {UniqueCode}", request.UniqueCode);
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 获取活跃的解析规则
        /// </summary>
        /// <param name="clientId">客户ID（可选）</param>
        /// <returns>解析规则列表</returns>
        [HttpGet("rules")]
        public async Task<ActionResult> GetActiveRules([FromQuery] string? clientId = null)
        {
            try
            {
                var rules = await _barcodeParsingService.GetActiveRulesAsync(clientId);
                return Ok(rules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取解析规则时发生错误");
                return StatusCode(500, "服务器内部错误");
            }
        }

        /// <summary>
        /// 按分类获取解析规则
        /// </summary>
        /// <param name="categoryCode">分类代码</param>
        /// <param name="clientId">客户ID（可选）</param>
        /// <returns>解析规则列表</returns>
        [HttpGet("rules/category/{categoryCode}")]
        public async Task<ActionResult> GetRulesByCategory(
            string categoryCode,
            [FromQuery] string? clientId = null
        )
        {
            try
            {
                if (string.IsNullOrWhiteSpace(categoryCode))
                {
                    return BadRequest("分类代码不能为空");
                }

                var rules = await _barcodeParsingService.GetRulesByCategoryAsync(
                    categoryCode,
                    clientId
                );
                return Ok(rules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按分类获取解析规则时发生错误: {CategoryCode}", categoryCode);
                return StatusCode(500, "服务器内部错误");
            }
        }
    }

    /// <summary>
    /// 注册唯一码请求
    /// </summary>
    public class RegisterUniqueCodeRequest
    {
        /// <summary>
        /// 唯一码
        /// </summary>
        public string UniqueCode { get; set; } = string.Empty;

        /// <summary>
        /// 物料ID
        /// </summary>
        public int MaterialId { get; set; }

        /// <summary>
        /// 储位ID（可选）
        /// </summary>
        public int? StorageLocationId { get; set; }
    }
}
