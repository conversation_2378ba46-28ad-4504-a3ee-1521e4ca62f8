// API Response Types
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// Authentication Types
export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface LoginResponse {
  accessToken: string
  refreshToken: string
  expiresIn: number
  tokenType: string
}

export interface UserInfo {
  id: string
  email: string
  userName: string
  firstName: string
  lastName: string
  employeeId: string
  department: string
  position: string
  phoneNumber: string
  roles: string[]
  createdAt: string
  updatedAt: string
}

// Warehouse Types
export interface Warehouse {
  id: number
  code: string
  name: string
  warehouseType: string
  address: string
  contactPerson: string
  contactPhone: string
  status: 'Active' | 'Inactive' | 'Maintenance'
  configuration: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface Zone {
  id: number
  code: string
  name: string
  warehouseId: number
  zoneType: 'Ambient' | 'Refrigerated' | 'Frozen' | 'Hazmat' | 'HighSecurity' | 'Receiving' | 'Shipping'
  temperatureRange: string
  humidityRange: string
  securityLevel: 'Standard' | 'Medium' | 'High' | 'Critical'
  accessRestrictions: string
  maxCapacity: number
  currentCapacity: number
  status: 'Active' | 'Inactive' | 'Maintenance'
  configuration: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface Shelf {
  id: number
  code: string
  name: string
  zoneId: number
  shelfType: 'Standard' | 'Heavy' | 'Cantilever' | 'Flow' | 'DriveIn' | 'PushBack'
  dimensions: string
  maxWeight: number
  maxVolume: number
  material: string
  safetyCertifications: string
  maintenanceSchedule: string
  installationDate: string
  status: 'Active' | 'Inactive' | 'Maintenance'
  configuration: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface StorageLocation {
  id: number
  code: string
  name: string
  shelfId: number
  esp32ControllerId: number
  ledChannelNumber: number
  ledStartPosition: number
  ledEndPosition: number
  capacity: number
  maxWeight: number
  dimensions: string
  zone: string
  shelfCode: string
  status: 'Available' | 'Occupied' | 'Reserved' | 'Maintenance'
  configuration: Record<string, any>
  properties: Record<string, any>
  createdAt: string
  updatedAt: string
}

// ESP32 Types
export interface ESP32Controller {
  id: number
  code: string
  name: string
  ipAddress: string
  port: number
  firmwareVersion: string
  hardwareVersion: string
  status: 'Online' | 'Offline' | 'Error'
  lastHeartbeat: string
  createdAt: string
  updatedAt: string
}

export interface LEDControlRequest {
  esp32Id: number
  channelNumber: number
  startPosition: number
  endPosition: number
  color: string
  brightness: number
  duration: number
}

// Inventory Types
export interface Material {
  id: number
  code: string
  name: string
  description: string
  category: string
  unitOfMeasure: string
  weight: number
  volume: number
  status: 'Active' | 'Inactive'
  createdAt: string
  updatedAt: string
}

export interface Inventory {
  id: number
  materialId: number
  storageLocationId: number
  quantity: number
  reservedQuantity: number
  availableQuantity: number
  batchNumber: string
  expiryDate: string
  manufacturingDate: string
  lpn: string
  status: 'Available' | 'Reserved' | 'Quarantine' | 'Damaged'
  createdAt: string
  updatedAt: string
}

// Operations Types
export interface InboundOrder {
  id: number
  orderNumber: string
  supplierName: string
  expectedDate: string
  actualDate: string
  status: 'Planned' | 'InProgress' | 'Completed' | 'Cancelled'
  totalItems: number
  completedItems: number
  createdAt: string
  updatedAt: string
}

export interface OutboundOrder {
  id: number
  orderNumber: string
  customerName: string
  expectedDate: string
  actualDate: string
  status: 'Planned' | 'InProgress' | 'Completed' | 'Cancelled'
  totalItems: number
  completedItems: number
  createdAt: string
  updatedAt: string
}

// Common Types
export interface PaginatedResponse<T> {
  items: T[]
  totalCount: number
  pageIndex: number
  pageSize: number
  totalPages: number
}

export interface TableColumn {
  key: string
  title: string
  dataIndex: string
  width?: number
  sorter?: boolean
  filterable?: boolean
  render?: (value: any, record: any) => any
}

export interface MenuItem {
  key: string
  label: string
  icon?: string
  children?: MenuItem[]
  path?: string
  permission?: string
}

export interface BreadcrumbItem {
  title: string
  path?: string
}