using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    public enum InventoryStatus
    {
        Available = 0,
        Pending = 1,
        Frozen = 2,
    }

    public class Inventory
    {
        public int Id { get; set; }

        public int StorageLocationId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual StorageLocation? StorageLocation { get; set; }

        public int MaterialId { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual Material? Material { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? LpnCode { get; set; }

        public InventoryStatus Status { get; set; } = InventoryStatus.Available;

        public DateTime? ExpiryDate { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
