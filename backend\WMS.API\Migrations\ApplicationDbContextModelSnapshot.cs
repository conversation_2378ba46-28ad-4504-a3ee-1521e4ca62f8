﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using WMS.API.Data;

#nullable disable

namespace WMS.API.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("WMS.API.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedById")
                        .HasColumnType("text");

                    b.Property<string>("Department")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("EmployeeId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamptz");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("Position")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EmployeeId")
                        .IsUnique()
                        .HasFilter("\"EmployeeId\" IS NOT NULL");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("WMS.API.Models.BarcodeParsingLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClientId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("boolean");

                    b.Property<int?>("MatchedRuleId")
                        .HasColumnType("integer");

                    b.Property<string>("OperationType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OriginalBarcode")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("ParsedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("ParsedResult")
                        .HasColumnType("jsonb");

                    b.Property<int>("ProcessingTimeMs")
                        .HasColumnType("integer");

                    b.Property<string>("StorageLocationCode")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("IsSuccess");

                    b.HasIndex("MatchedRuleId");

                    b.HasIndex("ParsedAt");

                    b.HasIndex("UserId");

                    b.ToTable("BarcodeParsingLogs");
                });

            modelBuilder.Entity("WMS.API.Models.BarcodeParsingRule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<string>("ClientId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FieldMappings")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("RegexPattern")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RuleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("ClientId");

                    b.HasIndex("Priority");

                    b.ToTable("BarcodeParsingRules");
                });

            modelBuilder.Entity("WMS.API.Models.BarcodeRuleCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CategoryCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CategoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("CategoryCode")
                        .IsUnique();

                    b.ToTable("BarcodeRuleCategories");
                });

            modelBuilder.Entity("WMS.API.Models.BarcodeValidationRule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClientId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Configuration")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("RuleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("RuleType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("RuleType");

                    b.ToTable("BarcodeValidationRules");
                });

            modelBuilder.Entity("WMS.API.Models.ConfigurationApplicationLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("AppliedAt")
                        .HasColumnType("timestamptz");

                    b.Property<int?>("AppliedConfigId")
                        .HasColumnType("integer");

                    b.Property<string>("AppliedConfigLevel")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ConfigKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ConfigResolutionPath")
                        .HasColumnType("text");

                    b.Property<string>("FinalConfigValue")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("LocationCode")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("MaterialCategory")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OperationId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ShelfCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UserId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ZoneCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("AppliedAt");

                    b.HasIndex("AppliedConfigId");

                    b.HasIndex("OperationId");

                    b.ToTable("ConfigurationApplicationLogs");
                });

            modelBuilder.Entity("WMS.API.Models.ConfigurationConflictLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("ConflictType")
                        .HasColumnType("integer");

                    b.Property<string>("ConflictingConfigs")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ResolutionStrategy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ResolvedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ResolvedValue")
                        .HasColumnType("jsonb");

                    b.Property<string>("TargetScope")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("ConflictType");

                    b.HasIndex("CreatedAt");

                    b.ToTable("ConfigurationConflictLogs");
                });

            modelBuilder.Entity("WMS.API.Models.ESP32Controller", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsOnline")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastHeartbeat")
                        .HasColumnType("timestamptz");

                    b.Property<string>("MdnsName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Port")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.ToTable("ESP32Controllers");
                });

            modelBuilder.Entity("WMS.API.Models.HierarchicalConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ConfigKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("ConfigLevel")
                        .HasColumnType("integer");

                    b.Property<string>("ConfigValue")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("EffectiveFrom")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamptz");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("TargetId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TargetType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("ConfigKey");

                    b.HasIndex("Priority");

                    b.HasIndex("ClientId", "ConfigLevel");

                    b.HasIndex("TargetType", "TargetId");

                    b.ToTable("HierarchicalConfigurations");
                });

            modelBuilder.Entity("WMS.API.Models.InboundOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualDate")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ExpectedDate")
                        .HasColumnType("timestamptz");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ProcessedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("SupplierCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SupplierName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("OrderNumber")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("SupplierCode");

                    b.ToTable("InboundOrders");
                });

            modelBuilder.Entity("WMS.API.Models.InboundOrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("ActualQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<decimal>("ExpectedQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamptz");

                    b.Property<int>("InboundOrderId")
                        .HasColumnType("integer");

                    b.Property<string>("LpnCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("MaterialId")
                        .HasColumnType("integer");

                    b.Property<string>("Remarks")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("InboundOrderId");

                    b.HasIndex("MaterialId");

                    b.ToTable("InboundOrderItems");
                });

            modelBuilder.Entity("WMS.API.Models.Inventory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamptz");

                    b.Property<string>("LpnCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("MaterialId")
                        .HasColumnType("integer");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("StorageLocationId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("MaterialId");

                    b.HasIndex("StorageLocationId");

                    b.ToTable("Inventories");
                });

            modelBuilder.Entity("WMS.API.Models.InventoryTransaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("AfterQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("ApprovedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("BarcodeData")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("BeforeQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamptz");

                    b.Property<int?>("InboundOrderId")
                        .HasColumnType("integer");

                    b.Property<bool>("LedActivated")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LedActivatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("LedColor")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("LpnCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("MaterialId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("OperatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("OperatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("OutboundOrderId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ParsedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("ParsedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("StorageLocationId")
                        .HasColumnType("integer");

                    b.Property<int?>("TargetStorageLocationId")
                        .HasColumnType("integer");

                    b.Property<string>("TransactionNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("UniqueCode")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("InboundOrderId");

                    b.HasIndex("MaterialId");

                    b.HasIndex("OperatedBy");

                    b.HasIndex("OutboundOrderId");

                    b.HasIndex("Status");

                    b.HasIndex("StorageLocationId");

                    b.HasIndex("TargetStorageLocationId");

                    b.HasIndex("TransactionNumber")
                        .IsUnique();

                    b.HasIndex("Type");

                    b.HasIndex("UniqueCode");

                    b.ToTable("InventoryTransactions");
                });

            modelBuilder.Entity("WMS.API.Models.LocationCapability", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CapabilityLevel")
                        .HasColumnType("integer");

                    b.Property<string>("CapabilityName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("CapabilityType")
                        .HasColumnType("integer");

                    b.Property<string>("Certifications")
                        .HasColumnType("jsonb");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamptz");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastVerificationDate")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime?>("NextVerificationDate")
                        .HasColumnType("timestamptz");

                    b.Property<string>("Parameters")
                        .HasColumnType("jsonb");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("StorageLocationId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ValidationRules")
                        .HasColumnType("jsonb");

                    b.HasKey("Id");

                    b.HasIndex("StorageLocationId", "CapabilityType")
                        .IsUnique();

                    b.ToTable("LocationCapabilities");
                });

            modelBuilder.Entity("WMS.API.Models.LocationClassification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BusinessRules")
                        .HasColumnType("jsonb");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Dimension")
                        .HasColumnType("integer");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamptz");

                    b.Property<string>("ExternalSystemRef")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("InheritedFrom")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsAutoAssigned")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInherited")
                        .HasColumnType("boolean");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("Properties")
                        .HasColumnType("jsonb");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("StorageLocationId")
                        .HasColumnType("integer");

                    b.Property<string>("Tags")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ValidationRules")
                        .HasColumnType("jsonb");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("StorageLocationId", "Dimension", "Category")
                        .IsUnique();

                    b.ToTable("LocationClassifications");
                });

            modelBuilder.Entity("WMS.API.Models.Material", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Unit")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("Sku")
                        .IsUnique();

                    b.ToTable("Materials");
                });

            modelBuilder.Entity("WMS.API.Models.OutboundOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualDate")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomerCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ExpectedDate")
                        .HasColumnType("timestamptz");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ProcessedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CustomerCode");

                    b.HasIndex("OrderNumber")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.ToTable("OutboundOrders");
                });

            modelBuilder.Entity("WMS.API.Models.OutboundOrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("LpnCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("MaterialId")
                        .HasColumnType("integer");

                    b.Property<int>("OutboundOrderId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("PickedQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal>("RequiredQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("MaterialId");

                    b.HasIndex("OutboundOrderId");

                    b.ToTable("OutboundOrderItems");
                });

            modelBuilder.Entity("WMS.API.Models.PickingTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedTo")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CompletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<int>("OutboundOrderItemId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("PickedQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal>("RequiredQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamptz");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("StorageLocationId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("OutboundOrderItemId");

                    b.HasIndex("Status");

                    b.HasIndex("StorageLocationId");

                    b.ToTable("PickingTasks");
                });

            modelBuilder.Entity("WMS.API.Models.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamptz");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("boolean");

                    b.Property<int?>("ReplacedByTokenId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("RevokedReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ReplacedByTokenId");

                    b.HasIndex("Token")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.HasIndex("UserId", "IsRevoked");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("WMS.API.Models.Shelf", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AccessRestrictions")
                        .HasColumnType("jsonb");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("Height")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("InstallationDate")
                        .HasColumnType("timestamptz");

                    b.Property<DateTime?>("LastInspectionDate")
                        .HasColumnType("timestamptz");

                    b.Property<decimal?>("Length")
                        .HasColumnType("numeric");

                    b.Property<int?>("Levels")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ManufactureDate")
                        .HasColumnType("timestamptz");

                    b.Property<string>("Manufacturer")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Material")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWeightPerPosition")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("NextInspectionDate")
                        .HasColumnType("timestamptz");

                    b.Property<int?>("PositionsPerLevel")
                        .HasColumnType("integer");

                    b.Property<string>("Remarks")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ResponsiblePerson")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SafetyCertifications")
                        .HasColumnType("jsonb");

                    b.Property<int>("ShelfType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("StoragePolicy")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("Width")
                        .HasColumnType("numeric");

                    b.Property<int>("ZoneId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ZoneId", "Code")
                        .IsUnique();

                    b.ToTable("Shelves");
                });

            modelBuilder.Entity("WMS.API.Models.StorageLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Aisle")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal?>("Capacity")
                        .HasColumnType("numeric");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<string>("Coordinates")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Dimensions")
                        .HasColumnType("jsonb");

                    b.Property<int>("ESP32ControllerId")
                        .HasColumnType("integer");

                    b.Property<int>("EndLedPosition")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastInventoryDate")
                        .HasColumnType("timestamptz");

                    b.Property<int>("LedChannel")
                        .HasColumnType("integer");

                    b.Property<string>("Level")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("NextInventoryDate")
                        .HasColumnType("timestamptz");

                    b.Property<string>("Position")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Properties")
                        .HasColumnType("jsonb");

                    b.Property<string>("Remarks")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ResponsiblePerson")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ShelfCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("ShelfId")
                        .HasColumnType("integer");

                    b.Property<int>("StartLedPosition")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Zone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("ESP32ControllerId");

                    b.HasIndex("ShelfId");

                    b.ToTable("StorageLocations");
                });

            modelBuilder.Entity("WMS.API.Models.UniqueCodeRegistry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("LastOperationDate")
                        .HasColumnType("timestamptz");

                    b.Property<int>("MaterialId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("RegistrationDate")
                        .HasColumnType("timestamptz");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int?>("StorageLocationId")
                        .HasColumnType("integer");

                    b.Property<string>("UniqueCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.HasKey("Id");

                    b.HasIndex("MaterialId");

                    b.HasIndex("Status");

                    b.HasIndex("StorageLocationId");

                    b.HasIndex("UniqueCode")
                        .IsUnique();

                    b.ToTable("UniqueCodeRegistries");
                });

            modelBuilder.Entity("WMS.API.Models.Warehouse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<decimal?>("TotalArea")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TotalVolume")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("WarehouseType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Warehouses");
                });

            modelBuilder.Entity("WMS.API.Models.Zone", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AccessControl")
                        .HasColumnType("jsonb");

                    b.Property<decimal?>("Area")
                        .HasColumnType("numeric");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Configuration")
                        .HasColumnType("jsonb");

                    b.Property<string>("ContactInfo")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("HumidityRange")
                        .HasColumnType("jsonb");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ResponsiblePerson")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("SecurityLevel")
                        .HasColumnType("integer");

                    b.Property<string>("SpecialRequirements")
                        .HasColumnType("jsonb");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TemperatureRange")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamptz");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("Volume")
                        .HasColumnType("numeric");

                    b.Property<int>("WarehouseId")
                        .HasColumnType("integer");

                    b.Property<int>("ZoneType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("WarehouseId", "Code")
                        .IsUnique();

                    b.ToTable("Zones");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("WMS.API.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("WMS.API.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("WMS.API.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WMS.API.Models.ApplicationUser", b =>
                {
                    b.HasOne("WMS.API.Models.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("WMS.API.Models.BarcodeParsingLog", b =>
                {
                    b.HasOne("WMS.API.Models.BarcodeParsingRule", "MatchedRule")
                        .WithMany()
                        .HasForeignKey("MatchedRuleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("MatchedRule");
                });

            modelBuilder.Entity("WMS.API.Models.BarcodeParsingRule", b =>
                {
                    b.HasOne("WMS.API.Models.BarcodeRuleCategory", "Category")
                        .WithMany("BarcodeParsingRules")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("WMS.API.Models.ConfigurationApplicationLog", b =>
                {
                    b.HasOne("WMS.API.Models.HierarchicalConfiguration", "AppliedConfig")
                        .WithMany()
                        .HasForeignKey("AppliedConfigId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AppliedConfig");
                });

            modelBuilder.Entity("WMS.API.Models.InboundOrderItem", b =>
                {
                    b.HasOne("WMS.API.Models.InboundOrder", "InboundOrder")
                        .WithMany("Items")
                        .HasForeignKey("InboundOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.Material", "Material")
                        .WithMany()
                        .HasForeignKey("MaterialId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("InboundOrder");

                    b.Navigation("Material");
                });

            modelBuilder.Entity("WMS.API.Models.Inventory", b =>
                {
                    b.HasOne("WMS.API.Models.Material", "Material")
                        .WithMany("Inventories")
                        .HasForeignKey("MaterialId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.StorageLocation", "StorageLocation")
                        .WithMany("Inventories")
                        .HasForeignKey("StorageLocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Material");

                    b.Navigation("StorageLocation");
                });

            modelBuilder.Entity("WMS.API.Models.InventoryTransaction", b =>
                {
                    b.HasOne("WMS.API.Models.InboundOrder", "InboundOrder")
                        .WithMany("Transactions")
                        .HasForeignKey("InboundOrderId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WMS.API.Models.Material", "Material")
                        .WithMany()
                        .HasForeignKey("MaterialId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.OutboundOrder", "OutboundOrder")
                        .WithMany("Transactions")
                        .HasForeignKey("OutboundOrderId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WMS.API.Models.StorageLocation", "StorageLocation")
                        .WithMany()
                        .HasForeignKey("StorageLocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.StorageLocation", "TargetStorageLocation")
                        .WithMany()
                        .HasForeignKey("TargetStorageLocationId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("InboundOrder");

                    b.Navigation("Material");

                    b.Navigation("OutboundOrder");

                    b.Navigation("StorageLocation");

                    b.Navigation("TargetStorageLocation");
                });

            modelBuilder.Entity("WMS.API.Models.LocationCapability", b =>
                {
                    b.HasOne("WMS.API.Models.StorageLocation", "StorageLocation")
                        .WithMany("Capabilities")
                        .HasForeignKey("StorageLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StorageLocation");
                });

            modelBuilder.Entity("WMS.API.Models.LocationClassification", b =>
                {
                    b.HasOne("WMS.API.Models.StorageLocation", "StorageLocation")
                        .WithMany("Classifications")
                        .HasForeignKey("StorageLocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StorageLocation");
                });

            modelBuilder.Entity("WMS.API.Models.OutboundOrderItem", b =>
                {
                    b.HasOne("WMS.API.Models.Material", "Material")
                        .WithMany()
                        .HasForeignKey("MaterialId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.OutboundOrder", "OutboundOrder")
                        .WithMany("Items")
                        .HasForeignKey("OutboundOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Material");

                    b.Navigation("OutboundOrder");
                });

            modelBuilder.Entity("WMS.API.Models.PickingTask", b =>
                {
                    b.HasOne("WMS.API.Models.OutboundOrderItem", "OutboundOrderItem")
                        .WithMany("PickingTasks")
                        .HasForeignKey("OutboundOrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.StorageLocation", "StorageLocation")
                        .WithMany()
                        .HasForeignKey("StorageLocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("OutboundOrderItem");

                    b.Navigation("StorageLocation");
                });

            modelBuilder.Entity("WMS.API.Models.RefreshToken", b =>
                {
                    b.HasOne("WMS.API.Models.RefreshToken", "ReplacedByToken")
                        .WithMany()
                        .HasForeignKey("ReplacedByTokenId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WMS.API.Models.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ReplacedByToken");

                    b.Navigation("User");
                });

            modelBuilder.Entity("WMS.API.Models.Shelf", b =>
                {
                    b.HasOne("WMS.API.Models.Zone", "Zone")
                        .WithMany("Shelves")
                        .HasForeignKey("ZoneId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("WMS.API.Models.StorageLocation", b =>
                {
                    b.HasOne("WMS.API.Models.ESP32Controller", "ESP32Controller")
                        .WithMany("StorageLocations")
                        .HasForeignKey("ESP32ControllerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.Shelf", "Shelf")
                        .WithMany("StorageLocations")
                        .HasForeignKey("ShelfId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ESP32Controller");

                    b.Navigation("Shelf");
                });

            modelBuilder.Entity("WMS.API.Models.UniqueCodeRegistry", b =>
                {
                    b.HasOne("WMS.API.Models.Material", "Material")
                        .WithMany()
                        .HasForeignKey("MaterialId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WMS.API.Models.StorageLocation", "StorageLocation")
                        .WithMany()
                        .HasForeignKey("StorageLocationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Material");

                    b.Navigation("StorageLocation");
                });

            modelBuilder.Entity("WMS.API.Models.Zone", b =>
                {
                    b.HasOne("WMS.API.Models.Warehouse", "Warehouse")
                        .WithMany("Zones")
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("WMS.API.Models.BarcodeRuleCategory", b =>
                {
                    b.Navigation("BarcodeParsingRules");
                });

            modelBuilder.Entity("WMS.API.Models.ESP32Controller", b =>
                {
                    b.Navigation("StorageLocations");
                });

            modelBuilder.Entity("WMS.API.Models.InboundOrder", b =>
                {
                    b.Navigation("Items");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("WMS.API.Models.Material", b =>
                {
                    b.Navigation("Inventories");
                });

            modelBuilder.Entity("WMS.API.Models.OutboundOrder", b =>
                {
                    b.Navigation("Items");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("WMS.API.Models.OutboundOrderItem", b =>
                {
                    b.Navigation("PickingTasks");
                });

            modelBuilder.Entity("WMS.API.Models.Shelf", b =>
                {
                    b.Navigation("StorageLocations");
                });

            modelBuilder.Entity("WMS.API.Models.StorageLocation", b =>
                {
                    b.Navigation("Capabilities");

                    b.Navigation("Classifications");

                    b.Navigation("Inventories");
                });

            modelBuilder.Entity("WMS.API.Models.Warehouse", b =>
                {
                    b.Navigation("Zones");
                });

            modelBuilder.Entity("WMS.API.Models.Zone", b =>
                {
                    b.Navigation("Shelves");
                });
#pragma warning restore 612, 618
        }
    }
}
