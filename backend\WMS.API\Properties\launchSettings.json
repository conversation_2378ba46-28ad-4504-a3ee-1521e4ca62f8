{"profiles": {"http": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5000"}, "https": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7279;http://localhost:5246"}, "WSL": {"commandName": "WSL2", "launchUrl": "https://localhost:7279", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7279;http://localhost:5246"}, "distributionName": ""}}, "$schema": "https://json.schemastore.org/launchsettings.json"}