using System.ComponentModel.DataAnnotations;

namespace WMS.API.Models
{
    /// <summary>
    /// 储位推荐请求参数
    /// </summary>
    public class StorageLocationRecommendationRequest
    {
        /// <summary>
        /// 物料类型（可选）
        /// </summary>
        public string? MaterialType { get; set; }

        /// <summary>
        /// 温度要求（可选）
        /// </summary>
        public string? TemperatureRequirement { get; set; }

        /// <summary>
        /// 危险品等级（可选）
        /// </summary>
        public string? HazmatClass { get; set; }

        /// <summary>
        /// 特殊要求列表（可选）
        /// </summary>
        public List<string>? SpecialRequirements { get; set; }

        /// <summary>
        /// 物料数量（可选）
        /// </summary>
        public int? Quantity { get; set; }

        /// <summary>
        /// 物料重量（可选）
        /// </summary>
        public decimal? Weight { get; set; }

        /// <summary>
        /// 物料尺寸（可选）
        /// </summary>
        public MaterialDimensions? Dimensions { get; set; }

        /// <summary>
        /// 优选区域（可选）
        /// </summary>
        public string? PreferredZone { get; set; }

        /// <summary>
        /// 位置偏好（可选）
        /// </summary>
        public string? ProximityPreference { get; set; }
    }

    /// <summary>
    /// 物料尺寸信息
    /// </summary>
    public class MaterialDimensions
    {
        /// <summary>
        /// 长度（米）
        /// </summary>
        public decimal Length { get; set; }

        /// <summary>
        /// 宽度（米）
        /// </summary>
        public decimal Width { get; set; }

        /// <summary>
        /// 高度（米）
        /// </summary>
        public decimal Height { get; set; }
    }
}