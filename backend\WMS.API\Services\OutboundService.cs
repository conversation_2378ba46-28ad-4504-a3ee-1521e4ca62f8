using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;
using WMS.API.Services;

namespace WMS.API.Services
{
    public interface IOutboundService
    {
        Task<OutboundOrder> CreateOutboundOrderAsync(
            OutboundOrderCreateRequest request,
            string createdBy
        );
        Task<OutboundOrderItem> AddItemToOrderAsync(int orderId, OutboundOrderItemRequest request);
        Task<bool> StartPickingAsync(int orderId, string processedBy);
        Task<IEnumerable<PickingTask>> GeneratePickingTasksAsync(int orderId);
        Task<bool> ProcessPickingTaskAsync(
            int taskId,
            PickingTaskProcessRequest request,
            string completedBy
        );
        Task<bool> ProcessPickingWithBarcodeAsync(
            int taskId,
            BarcodePickingRequest request,
            string completedBy
        );
        Task<bool> CompleteOutboundOrderAsync(int orderId, string completedBy);
        Task<IEnumerable<OutboundOrder>> GetOutboundOrdersAsync(OutboundOrderStatus? status = null);
        Task<OutboundOrder?> GetOutboundOrderByIdAsync(int id);
        Task<OutboundOrder?> GetOutboundOrderByNumberAsync(string orderNumber);
        Task<IEnumerable<PickingTask>> GetPickingTasksAsync(
            int? orderId = null,
            PickingTaskStatus? status = null,
            string? assignedTo = null
        );
        Task<bool> UpdateOutboundOrderStatusAsync(
            int orderId,
            OutboundOrderStatus status,
            string? processedBy = null
        );
        Task<bool> CancelOutboundOrderAsync(int orderId, string reason, string cancelledBy);
        Task<string> GenerateOutboundOrderNumberAsync();
    }

    public class OutboundService : IOutboundService
    {
        private readonly ApplicationDbContext _context;
        private readonly IInventoryTransactionService _transactionService;
        private readonly IBarcodeParsingService _barcodeService;
        private readonly ILogger<OutboundService> _logger;

        public OutboundService(
            ApplicationDbContext context,
            IInventoryTransactionService transactionService,
            IBarcodeParsingService barcodeService,
            ILogger<OutboundService> logger
        )
        {
            _context = context;
            _transactionService = transactionService;
            _barcodeService = barcodeService;
            _logger = logger;
        }

        public async Task<OutboundOrder> CreateOutboundOrderAsync(
            OutboundOrderCreateRequest request,
            string createdBy
        )
        {
            try
            {
                var orderNumber = await GenerateOutboundOrderNumberAsync();

                var order = new OutboundOrder
                {
                    OrderNumber = orderNumber,
                    Type = request.Type,
                    CustomerName = request.CustomerName,
                    CustomerCode = request.CustomerCode,
                    ExpectedDate = request.ExpectedDate,
                    Remarks = request.Remarks,
                    CreatedBy = createdBy,
                    Status = OutboundOrderStatus.Pending,
                };

                _context.OutboundOrders.Add(order);
                await _context.SaveChangesAsync();

                // 添加订单项目
                if (request.Items != null && request.Items.Any())
                {
                    foreach (var itemRequest in request.Items)
                    {
                        await AddItemToOrderAsync(order.Id, itemRequest);
                    }
                }

                _logger.LogInformation(
                    "Created outbound order {OrderNumber} with {ItemCount} items",
                    orderNumber,
                    request.Items?.Count() ?? 0
                );

                return order;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating outbound order");
                throw;
            }
        }

        public async Task<OutboundOrderItem> AddItemToOrderAsync(
            int orderId,
            OutboundOrderItemRequest request
        )
        {
            try
            {
                var order = await _context.OutboundOrders.FindAsync(orderId);
                if (order == null)
                {
                    throw new ArgumentException("Outbound order not found", nameof(orderId));
                }

                if (order.Status != OutboundOrderStatus.Pending)
                {
                    throw new InvalidOperationException("Cannot add items to non-pending order");
                }

                var material = await _context.Materials.FindAsync(request.MaterialId);
                if (material == null)
                {
                    throw new ArgumentException("Material not found", nameof(request.MaterialId));
                }

                var item = new OutboundOrderItem
                {
                    OutboundOrderId = orderId,
                    MaterialId = request.MaterialId,
                    RequiredQuantity = request.RequiredQuantity,
                    BatchNumber = request.BatchNumber,
                    LpnCode = request.LpnCode,
                    Remarks = request.Remarks,
                };

                _context.OutboundOrderItems.Add(item);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Added item to outbound order {OrderId}: Material {MaterialId}, Quantity {Quantity}",
                    orderId,
                    request.MaterialId,
                    request.RequiredQuantity
                );

                return item;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to outbound order {OrderId}", orderId);
                throw;
            }
        }

        public async Task<bool> StartPickingAsync(int orderId, string processedBy)
        {
            using var dbTransaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var order = await _context
                    .OutboundOrders.Include(o => o.Items)
                    .FirstOrDefaultAsync(o => o.Id == orderId);

                if (order == null || order.Status != OutboundOrderStatus.Pending)
                {
                    return false;
                }

                // 生成拣选任务
                var pickingTasks = await GeneratePickingTasksAsync(orderId);
                if (!pickingTasks.Any())
                {
                    _logger.LogWarning("No picking tasks generated for order {OrderId}", orderId);
                    return false;
                }

                // 更新订单状态
                order.Status = OutboundOrderStatus.InProgress;
                order.ProcessedBy = processedBy;
                order.ActualDate = DateTime.UtcNow;
                order.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                await dbTransaction.CommitAsync();

                _logger.LogInformation(
                    "Started picking for outbound order {OrderNumber}, generated {TaskCount} picking tasks",
                    order.OrderNumber,
                    pickingTasks.Count()
                );

                return true;
            }
            catch (Exception ex)
            {
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error starting picking for order {OrderId}", orderId);
                throw;
            }
        }

        public async Task<IEnumerable<PickingTask>> GeneratePickingTasksAsync(int orderId)
        {
            try
            {
                var order = await _context
                    .OutboundOrders.Include(o => o.Items)
                    .ThenInclude(i => i.Material)
                    .FirstOrDefaultAsync(o => o.Id == orderId);

                if (order == null || order.Items == null)
                {
                    return new List<PickingTask>();
                }

                var pickingTasks = new List<PickingTask>();

                foreach (var item in order.Items)
                {
                    var remainingQuantity = item.RequiredQuantity;

                    // 查找可用库存
                    var availableInventories = await _context
                        .Inventories.Include(i => i.StorageLocation)
                        .Where(i =>
                            i.MaterialId == item.MaterialId
                            && i.Status == InventoryStatus.Available
                            && i.Quantity > 0
                            && (item.BatchNumber == null || i.BatchNumber == item.BatchNumber)
                            && (item.LpnCode == null || i.LpnCode == item.LpnCode)
                        )
                        .OrderBy(i => i.ExpiryDate ?? DateTime.MaxValue) // FIFO原则
                        .ThenBy(i => i.CreatedAt)
                        .ToListAsync();

                    foreach (var inventory in availableInventories)
                    {
                        if (remainingQuantity <= 0)
                            break;

                        var pickQuantity = Math.Min(remainingQuantity, inventory.Quantity);

                        var pickingTask = new PickingTask
                        {
                            OutboundOrderItemId = item.Id,
                            StorageLocationId = inventory.StorageLocationId,
                            RequiredQuantity = pickQuantity,
                            Status = PickingTaskStatus.Pending,
                            Remarks =
                                $"从{inventory.StorageLocation?.Code}拣选{pickQuantity}{item.Material?.Unit}",
                        };

                        _context.PickingTasks.Add(pickingTask);
                        pickingTasks.Add(pickingTask);

                        remainingQuantity -= pickQuantity;
                    }

                    // 如果库存不足，记录警告
                    if (remainingQuantity > 0)
                    {
                        _logger.LogWarning(
                            "Insufficient inventory for material {MaterialId} in order {OrderId}, short by {ShortQuantity}",
                            item.MaterialId,
                            orderId,
                            remainingQuantity
                        );
                    }
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Generated {TaskCount} picking tasks for order {OrderId}",
                    pickingTasks.Count,
                    orderId
                );

                return pickingTasks;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating picking tasks for order {OrderId}", orderId);
                throw;
            }
        }

        public async Task<bool> ProcessPickingTaskAsync(
            int taskId,
            PickingTaskProcessRequest request,
            string completedBy
        )
        {
            using var dbTransaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var task = await _context
                    .PickingTasks.Include(t => t.OutboundOrderItem)
                    .ThenInclude(i => i.OutboundOrder)
                    .Include(t => t.StorageLocation)
                    .FirstOrDefaultAsync(t => t.Id == taskId);

                if (task == null || task.Status != PickingTaskStatus.Pending)
                {
                    return false;
                }

                // 验证库存充足性
                var inventory = await _context.Inventories.FirstOrDefaultAsync(i =>
                    i.StorageLocationId == task.StorageLocationId
                    && i.MaterialId == task.OutboundOrderItem!.MaterialId
                    && i.Status == InventoryStatus.Available
                );

                if (inventory == null || inventory.Quantity < request.PickedQuantity)
                {
                    _logger.LogWarning(
                        "Insufficient inventory for picking task {TaskId}, available: {Available}, required: {Required}",
                        taskId,
                        inventory?.Quantity ?? 0,
                        request.PickedQuantity
                    );
                    return false;
                }

                // 更新拣选任务
                task.PickedQuantity = request.PickedQuantity;
                task.Status = PickingTaskStatus.Completed;
                task.CompletedBy = completedBy;
                task.CompletedAt = DateTime.UtcNow;
                task.Remarks = request.Remarks ?? task.Remarks;
                task.UpdatedAt = DateTime.UtcNow;

                // 创建出库事务
                if (request.PickedQuantity > 0)
                {
                    var transaction = await _transactionService.CreateTransactionAsync(
                        TransactionType.Outbound,
                        task.StorageLocationId,
                        task.OutboundOrderItem.MaterialId,
                        request.PickedQuantity,
                        completedBy,
                        outboundOrderId: task.OutboundOrderItem.OutboundOrderId,
                        batchNumber: inventory.BatchNumber,
                        lpnCode: inventory.LpnCode,
                        uniqueCode: request.UniqueCode,
                        expiryDate: inventory.ExpiryDate,
                        remarks: $"拣选任务 {taskId} 出库"
                    );

                    // 执行库存事务
                    await _transactionService.ExecuteTransactionAsync(transaction.Id, completedBy);
                }

                // 更新订单项目的已拣选数量
                var orderItem = task.OutboundOrderItem;
                var totalPicked = await _context
                    .PickingTasks.Where(t =>
                        t.OutboundOrderItemId == orderItem!.Id
                        && t.Status == PickingTaskStatus.Completed
                    )
                    .SumAsync(t => t.PickedQuantity ?? 0);

                orderItem!.PickedQuantity = totalPicked;

                // 检查订单是否可以完成
                await CheckAndUpdateOrderStatusAsync(task.OutboundOrderItem.OutboundOrderId);

                await _context.SaveChangesAsync();
                await dbTransaction.CommitAsync();

                _logger.LogInformation(
                    "Successfully processed picking task {TaskId}, picked quantity: {PickedQuantity}",
                    taskId,
                    request.PickedQuantity
                );

                return true;
            }
            catch (Exception ex)
            {
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error processing picking task {TaskId}", taskId);
                throw;
            }
        }

        public async Task<bool> ProcessPickingWithBarcodeAsync(
            int taskId,
            BarcodePickingRequest request,
            string completedBy
        )
        {
            using var dbTransaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var task = await _context
                    .PickingTasks.Include(t => t.OutboundOrderItem)
                    .ThenInclude(i => i.Material)
                    .Include(t => t.StorageLocation)
                    .FirstOrDefaultAsync(t => t.Id == taskId);

                if (task == null || task.Status != PickingTaskStatus.Pending)
                {
                    return false;
                }

                // 解析条码
                var barcodeContext = new DTOs.Barcode.BarcodeContext
                {
                    OperationType = "OUTBOUND",
                    StorageLocationCode = task.StorageLocation?.Code,
                    UserId = completedBy,
                };

                var barcodeResult = await _barcodeService.ParseBarcodeAsync(
                    request.BarcodeData,
                    barcodeContext
                );

                if (!barcodeResult.IsValid)
                {
                    _logger.LogWarning(
                        "Barcode parsing failed for picking task {TaskId}: {Errors}",
                        taskId,
                        string.Join(", ", barcodeResult.ValidationErrors)
                    );
                    return false;
                }

                // 验证物料匹配
                if (
                    !string.IsNullOrEmpty(barcodeResult.MaterialSku)
                    && barcodeResult.MaterialSku != task.OutboundOrderItem!.Material!.Sku
                )
                {
                    _logger.LogWarning(
                        "Material mismatch for picking task {TaskId}: expected {ExpectedSku}, got {ActualSku}",
                        taskId,
                        task.OutboundOrderItem.Material.Sku,
                        barcodeResult.MaterialSku
                    );
                    return false;
                }

                // 验证储位匹配
                if (
                    !string.IsNullOrEmpty(barcodeResult.StorageLocationCode)
                    && barcodeResult.StorageLocationCode != task.StorageLocation?.Code
                )
                {
                    _logger.LogWarning(
                        "Storage location mismatch for picking task {TaskId}: expected {ExpectedLocation}, got {ActualLocation}",
                        taskId,
                        task.StorageLocation?.Code,
                        barcodeResult.StorageLocationCode
                    );
                    return false;
                }

                var pickedQuantity = barcodeResult.Quantity ?? request.PickedQuantity;

                // 验证库存充足性
                var inventory = await _context.Inventories.FirstOrDefaultAsync(i =>
                    i.StorageLocationId == task.StorageLocationId
                    && i.MaterialId == task.OutboundOrderItem.MaterialId
                    && i.Status == InventoryStatus.Available
                );

                if (inventory == null || inventory.Quantity < pickedQuantity)
                {
                    _logger.LogWarning(
                        "Insufficient inventory for barcode picking task {TaskId}, available: {Available}, required: {Required}",
                        taskId,
                        inventory?.Quantity ?? 0,
                        pickedQuantity
                    );
                    return false;
                }

                // 更新拣选任务
                task.PickedQuantity = pickedQuantity;
                task.Status = PickingTaskStatus.Completed;
                task.CompletedBy = completedBy;
                task.CompletedAt = DateTime.UtcNow;
                task.UpdatedAt = DateTime.UtcNow;

                // 创建出库事务
                if (pickedQuantity > 0)
                {
                    var transaction = await _transactionService.CreateTransactionAsync(
                        TransactionType.Outbound,
                        task.StorageLocationId,
                        task.OutboundOrderItem.MaterialId,
                        pickedQuantity,
                        completedBy,
                        outboundOrderId: task.OutboundOrderItem.OutboundOrderId,
                        batchNumber: barcodeResult.BatchNumber ?? inventory.BatchNumber,
                        lpnCode: barcodeResult.LpnCode ?? inventory.LpnCode,
                        uniqueCode: barcodeResult.UniqueCode,
                        expiryDate: inventory.ExpiryDate,
                        remarks: $"条码拣选: {request.BarcodeData}"
                    );

                    transaction.BarcodeData = request.BarcodeData;
                    transaction.ParsedBy = completedBy;
                    transaction.ParsedAt = DateTime.UtcNow;

                    // 执行库存事务
                    await _transactionService.ExecuteTransactionAsync(transaction.Id, completedBy);
                }

                // 更新订单项目的已拣选数量
                var orderItem = task.OutboundOrderItem;
                var totalPicked = await _context
                    .PickingTasks.Where(t =>
                        t.OutboundOrderItemId == orderItem.Id
                        && t.Status == PickingTaskStatus.Completed
                    )
                    .SumAsync(t => t.PickedQuantity ?? 0);

                orderItem.PickedQuantity = totalPicked;

                // 检查订单是否可以完成
                await CheckAndUpdateOrderStatusAsync(task.OutboundOrderItem.OutboundOrderId);

                await _context.SaveChangesAsync();
                await dbTransaction.CommitAsync();

                _logger.LogInformation(
                    "Successfully processed barcode picking task {TaskId}, barcode: {BarcodeData}, picked: {PickedQuantity}",
                    taskId,
                    request.BarcodeData,
                    pickedQuantity
                );

                return true;
            }
            catch (Exception ex)
            {
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error processing barcode picking task {TaskId}", taskId);
                throw;
            }
        }

        public async Task<bool> CompleteOutboundOrderAsync(int orderId, string completedBy)
        {
            try
            {
                var order = await _context
                    .OutboundOrders.Include(o => o.Items)
                    .ThenInclude(i => i.PickingTasks)
                    .FirstOrDefaultAsync(o => o.Id == orderId);

                if (order == null)
                {
                    return false;
                }

                if (
                    order.Status != OutboundOrderStatus.InProgress
                    && order.Status != OutboundOrderStatus.Picked
                )
                {
                    return false;
                }

                // 检查所有拣选任务是否完成
                var allTasksCompleted = order
                    .Items!.SelectMany(i => i.PickingTasks!)
                    .All(t => t.Status == PickingTaskStatus.Completed);

                if (!allTasksCompleted)
                {
                    _logger.LogWarning(
                        "Cannot complete order {OrderId}: not all picking tasks are completed",
                        orderId
                    );
                    return false;
                }

                order.Status = OutboundOrderStatus.Completed;
                order.ProcessedBy = completedBy;
                order.ActualDate = DateTime.UtcNow;
                order.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Successfully completed outbound order {OrderNumber}",
                    order.OrderNumber
                );

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing outbound order {OrderId}", orderId);
                throw;
            }
        }

        public async Task<IEnumerable<OutboundOrder>> GetOutboundOrdersAsync(
            OutboundOrderStatus? status = null
        )
        {
            var query = _context
                .OutboundOrders.Include(o => o.Items)
                .ThenInclude(i => i.Material)
                .AsQueryable();

            if (status.HasValue)
            {
                query = query.Where(o => o.Status == status.Value);
            }

            return await query.OrderByDescending(o => o.CreatedAt).ToListAsync();
        }

        public async Task<OutboundOrder?> GetOutboundOrderByIdAsync(int id)
        {
            return await _context
                .OutboundOrders.Include(o => o.Items)
                .ThenInclude(i => i.Material)
                .Include(o => o.Items)
                .ThenInclude(i => i.PickingTasks)
                .ThenInclude(t => t.StorageLocation)
                .Include(o => o.Transactions)
                .FirstOrDefaultAsync(o => o.Id == id);
        }

        public async Task<OutboundOrder?> GetOutboundOrderByNumberAsync(string orderNumber)
        {
            return await _context
                .OutboundOrders.Include(o => o.Items)
                .ThenInclude(i => i.Material)
                .Include(o => o.Items)
                .ThenInclude(i => i.PickingTasks)
                .ThenInclude(t => t.StorageLocation)
                .Include(o => o.Transactions)
                .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber);
        }

        public async Task<IEnumerable<PickingTask>> GetPickingTasksAsync(
            int? orderId = null,
            PickingTaskStatus? status = null,
            string? assignedTo = null
        )
        {
            var query = _context
                .PickingTasks.Include(t => t.OutboundOrderItem)
                .ThenInclude(i => i.OutboundOrder)
                .Include(t => t.OutboundOrderItem)
                .ThenInclude(i => i.Material)
                .Include(t => t.StorageLocation)
                .AsQueryable();

            if (orderId.HasValue)
            {
                query = query.Where(t => t.OutboundOrderItem!.OutboundOrderId == orderId.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(t => t.Status == status.Value);
            }

            if (!string.IsNullOrEmpty(assignedTo))
            {
                query = query.Where(t => t.AssignedTo == assignedTo);
            }

            return await query.OrderBy(t => t.CreatedAt).ToListAsync();
        }

        public async Task<bool> UpdateOutboundOrderStatusAsync(
            int orderId,
            OutboundOrderStatus status,
            string? processedBy = null
        )
        {
            try
            {
                var order = await _context.OutboundOrders.FindAsync(orderId);
                if (order == null)
                {
                    return false;
                }

                order.Status = status;
                order.UpdatedAt = DateTime.UtcNow;

                if (!string.IsNullOrEmpty(processedBy))
                {
                    order.ProcessedBy = processedBy;
                }

                if (status == OutboundOrderStatus.InProgress && order.ActualDate == null)
                {
                    order.ActualDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Updated outbound order {OrderNumber} status to {Status}",
                    order.OrderNumber,
                    status
                );

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating outbound order {OrderId} status", orderId);
                throw;
            }
        }

        public async Task<bool> CancelOutboundOrderAsync(
            int orderId,
            string reason,
            string cancelledBy
        )
        {
            using var dbTransaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var order = await _context
                    .OutboundOrders.Include(o => o.Items)
                    .ThenInclude(i => i.PickingTasks)
                    .Include(o => o.Transactions)
                    .FirstOrDefaultAsync(o => o.Id == orderId);

                if (order == null)
                {
                    return false;
                }

                if (order.Status == OutboundOrderStatus.Completed)
                {
                    throw new InvalidOperationException("Cannot cancel completed order");
                }

                // 取消相关的拣选任务
                if (order.Items != null)
                {
                    foreach (var item in order.Items)
                    {
                        if (item.PickingTasks != null)
                        {
                            foreach (
                                var task in item.PickingTasks.Where(t =>
                                    t.Status == PickingTaskStatus.Pending
                                )
                            )
                            {
                                task.Status = PickingTaskStatus.Cancelled;
                                task.Remarks = $"订单取消: {reason}";
                                task.UpdatedAt = DateTime.UtcNow;
                            }
                        }
                    }
                }

                // 取消相关的事务
                if (order.Transactions != null)
                {
                    foreach (
                        var transaction in order.Transactions.Where(t =>
                            t.Status == TransactionStatus.Pending
                        )
                    )
                    {
                        await _transactionService.CancelTransactionAsync(transaction.Id, reason);
                    }
                }

                order.Status = OutboundOrderStatus.Cancelled;
                order.Remarks = string.IsNullOrEmpty(order.Remarks)
                    ? $"取消原因: {reason}"
                    : $"{order.Remarks}\n取消原因: {reason}";
                order.ProcessedBy = cancelledBy;
                order.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                await dbTransaction.CommitAsync();

                _logger.LogInformation(
                    "Cancelled outbound order {OrderNumber}, reason: {Reason}",
                    order.OrderNumber,
                    reason
                );

                return true;
            }
            catch (Exception ex)
            {
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error cancelling outbound order {OrderId}", orderId);
                throw;
            }
        }

        public async Task<string> GenerateOutboundOrderNumberAsync()
        {
            var prefix = "OB";
            var date = DateTime.UtcNow.ToString("yyyyMMdd");
            var sequence = await GetNextSequenceAsync(prefix, date);

            return $"{prefix}{date}{sequence:D4}";
        }

        private async Task<bool> CheckAndUpdateOrderStatusAsync(int orderId)
        {
            var order = await _context
                .OutboundOrders.Include(o => o.Items)
                .ThenInclude(i => i.PickingTasks)
                .FirstOrDefaultAsync(o => o.Id == orderId);

            if (order?.Items == null)
                return false;

            var allTasksCompleted = order
                .Items.SelectMany(i => i.PickingTasks!)
                .All(t =>
                    t.Status == PickingTaskStatus.Completed
                    || t.Status == PickingTaskStatus.Cancelled
                );

            if (allTasksCompleted && order.Status == OutboundOrderStatus.InProgress)
            {
                order.Status = OutboundOrderStatus.Picked;
                order.UpdatedAt = DateTime.UtcNow;
                return true;
            }

            return false;
        }

        private async Task<int> GetNextSequenceAsync(string prefix, string date)
        {
            var pattern = $"{prefix}{date}%";
            var lastOrder = await _context
                .OutboundOrders.Where(o => EF.Functions.Like(o.OrderNumber, pattern))
                .OrderByDescending(o => o.OrderNumber)
                .FirstOrDefaultAsync();

            if (lastOrder == null)
            {
                return 1;
            }

            var lastSequence = lastOrder.OrderNumber.Substring(prefix.Length + date.Length);
            if (int.TryParse(lastSequence, out var sequence))
            {
                return sequence + 1;
            }

            return 1;
        }
    }

    // DTOs
    public class OutboundOrderCreateRequest
    {
        public OutboundOrderType Type { get; set; } = OutboundOrderType.Sale;
        public string? CustomerName { get; set; }
        public string? CustomerCode { get; set; }
        public DateTime? ExpectedDate { get; set; }
        public string? Remarks { get; set; }
        public IEnumerable<OutboundOrderItemRequest>? Items { get; set; }
    }

    public class OutboundOrderItemRequest
    {
        public int MaterialId { get; set; }
        public decimal RequiredQuantity { get; set; }
        public string? BatchNumber { get; set; }
        public string? LpnCode { get; set; }
        public string? Remarks { get; set; }
    }

    public class PickingTaskProcessRequest
    {
        public decimal PickedQuantity { get; set; }
        public string? UniqueCode { get; set; }
        public string? Remarks { get; set; }
    }

    public class BarcodePickingRequest
    {
        public string BarcodeData { get; set; } = string.Empty;
        public decimal PickedQuantity { get; set; }
    }
}
