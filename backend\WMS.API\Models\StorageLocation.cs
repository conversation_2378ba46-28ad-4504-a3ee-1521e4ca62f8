using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 储位状态枚举
    /// </summary>
    public enum StorageLocationStatus
    {
        /// <summary>
        /// 可用状态 - 储位可以进行入库操作
        /// </summary>
        Available = 0,

        /// <summary>
        /// 锁定状态 - 储位被锁定，不可用于新的入库
        /// </summary>
        Locked = 1,

        /// <summary>
        /// 维护状态 - 储位正在维护中
        /// </summary>
        Maintenance = 2,

        /// <summary>
        /// 已占用 - 储位已被完全占用
        /// </summary>
        Occupied = 3,

        /// <summary>
        /// 部分占用 - 储位部分被占用，还有剩余空间
        /// </summary>
        PartiallyOccupied = 4,

        /// <summary>
        /// 损坏状态 - 储位损坏无法使用
        /// </summary>
        Damaged = 5,

        /// <summary>
        /// 隔离状态 - 储位被隔离（如安全检查、清洁等）
        /// </summary>
        Quarantine = 6
    }

    /// <summary>
    /// 储位实体类
    /// 用于管理仓库中的储存位置及其关联的LED灯条
    /// </summary>
    public class StorageLocation
    {
        /// <summary>
        /// 储位唯一标识ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 储位编码，如：A01-01-01
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 储位名称（可读性更好的名称）
        /// </summary>
        [StringLength(100)]
        public string? Name { get; set; }

        /// <summary>
        /// 储位描述信息
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 关联的货架ID（外键关系）
        /// </summary>
        public int? ShelfId { get; set; }

        /// <summary>
        /// 关联的货架实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual Shelf? Shelf { get; set; }

        /// <summary>
        /// 关联的ESP32控制器ID
        /// </summary>
        public int ESP32ControllerId { get; set; }

        /// <summary>
        /// 关联的ESP32控制器实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ESP32Controller? ESP32Controller { get; set; }

        /// <summary>
        /// LED灯条起始位置
        /// </summary>
        public int StartLedPosition { get; set; }

        /// <summary>
        /// LED灯条结束位置
        /// </summary>
        public int EndLedPosition { get; set; }

        /// <summary>
        /// LED通道号（0-3）
        /// ESP32支持4个LED通道
        /// </summary>
        public int LedChannel { get; set; } = 0;

        /// <summary>
        /// 储位当前状态
        /// </summary>
        public StorageLocationStatus Status { get; set; } = StorageLocationStatus.Available;

        /// <summary>
        /// 储位容量（立方米）
        /// </summary>
        public decimal? Capacity { get; set; }

        /// <summary>
        /// 最大载重（公斤）
        /// </summary>
        public decimal? MaxWeight { get; set; }

        /// <summary>
        /// 储位尺寸（JSON格式）
        /// 例如：{"length": 1.2, "width": 0.8, "height": 2.0, "unit": "m"}
        /// </summary>
        public string? Dimensions { get; set; }

        /// <summary>
        /// 层级编号（在货架中的层级位置）
        /// </summary>
        [StringLength(20)]
        public string? Level { get; set; }

        /// <summary>
        /// 位置编号（在层级中的位置）
        /// </summary>
        [StringLength(20)]
        public string? Position { get; set; }

        /// <summary>
        /// 坐标信息（JSON格式）
        /// 例如：{"x": 10.5, "y": 20.3, "z": 1.5, "unit": "m"}
        /// </summary>
        public string? Coordinates { get; set; }

        /// <summary>
        /// 特殊属性（JSON格式）
        /// 存储储位的特殊属性和扩展信息
        /// </summary>
        public string? Properties { get; set; }

        /// <summary>
        /// 配置信息（JSON格式）
        /// 存储储位级别的配置策略
        /// </summary>
        public string? Configuration { get; set; }

        /// <summary>
        /// 上次盘点时间
        /// </summary>
        public DateTime? LastInventoryDate { get; set; }

        /// <summary>
        /// 下次盘点时间
        /// </summary>
        public DateTime? NextInventoryDate { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        [StringLength(50)]
        public string? ResponsiblePerson { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 关联的库存集合
        /// 一个储位可以存放多种物料（不同批次等）
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<Inventory> Inventories { get; set; } = new List<Inventory>();

        /// <summary>
        /// 关联的储位能力集合
        /// 一个储位可以具备多种能力
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<LocationCapability> Capabilities { get; set; } = new List<LocationCapability>();

        /// <summary>
        /// 关联的储位分类集合
        /// 一个储位可以有多个分类维度
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<LocationClassification> Classifications { get; set; } = new List<LocationClassification>();

        // 以下字段保留用于兼容性和快速查询，从关联的Shelf中自动填充
        
        /// <summary>
        /// 区域代码（从Shelf.Zone.Code自动填充）
        /// 保留用于向后兼容和快速查询
        /// </summary>
        [StringLength(20)]
        public string? Zone { get; set; }

        /// <summary>
        /// 巷道编号（从Shelf获取或手工填写）
        /// 保留用于向后兼容和快速查询
        /// </summary>
        [StringLength(20)]
        public string? Aisle { get; set; }

        /// <summary>
        /// 货架编号（从Shelf.Code自动填充）
        /// 保留用于向后兼容和快速查询
        /// </summary>
        [StringLength(20)]
        public string? ShelfCode { get; set; }
    }
}
