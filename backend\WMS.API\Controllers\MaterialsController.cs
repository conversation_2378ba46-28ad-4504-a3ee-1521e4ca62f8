using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MaterialsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<MaterialsController> _logger;

        public MaterialsController(ApplicationDbContext context, ILogger<MaterialsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有物料
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Material>>> GetMaterials()
        {
            try
            {
                var materials = await _context.Materials
                    .OrderBy(m => m.Sku)
                    .ToListAsync();

                return Ok(materials);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取物料列表失败");
                return StatusCode(500, "获取物料列表失败");
            }
        }

        /// <summary>
        /// 根据ID获取物料
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<Material>> GetMaterial(int id)
        {
            try
            {
                var material = await _context.Materials.FindAsync(id);

                if (material == null)
                {
                    return NotFound("物料不存在");
                }

                return Ok(material);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取物料详情失败，ID: {Id}", id);
                return StatusCode(500, "获取物料详情失败");
            }
        }

        /// <summary>
        /// 根据SKU获取物料
        /// </summary>
        [HttpGet("by-sku/{sku}")]
        public async Task<ActionResult<Material>> GetMaterialBySku(string sku)
        {
            try
            {
                var material = await _context.Materials
                    .FirstOrDefaultAsync(m => m.Sku == sku);

                if (material == null)
                {
                    return NotFound($"物料SKU '{sku}' 不存在");
                }

                return Ok(material);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据SKU获取物料失败，SKU: {Sku}", sku);
                return StatusCode(500, "获取物料失败");
            }
        }

        /// <summary>
        /// 创建物料
        /// </summary>
        [HttpPost]
        [Authorize(Policy = "StorageLocationManagementPolicy")]
        public async Task<ActionResult<Material>> CreateMaterial(Material material)
        {
            try
            {
                // 检查SKU是否已存在
                var existingMaterial = await _context.Materials
                    .FirstOrDefaultAsync(m => m.Sku == material.Sku);

                if (existingMaterial != null)
                {
                    return BadRequest($"物料SKU '{material.Sku}' 已存在");
                }

                material.CreatedAt = DateTime.UtcNow;
                material.UpdatedAt = DateTime.UtcNow;

                _context.Materials.Add(material);
                await _context.SaveChangesAsync();

                _logger.LogInformation("物料创建成功，ID: {Id}, SKU: {Sku}", material.Id, material.Sku);
                return CreatedAtAction(nameof(GetMaterial), new { id = material.Id }, material);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建物料失败，SKU: {Sku}", material.Sku);
                return StatusCode(500, "创建物料失败");
            }
        }

        /// <summary>
        /// 更新物料
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Policy = "StorageLocationManagementPolicy")]
        public async Task<IActionResult> UpdateMaterial(int id, Material material)
        {
            if (id != material.Id)
            {
                return BadRequest("物料ID不匹配");
            }

            try
            {
                var existingMaterial = await _context.Materials.FindAsync(id);
                if (existingMaterial == null)
                {
                    return NotFound("物料不存在");
                }

                // 检查SKU是否与其他物料冲突
                var conflictMaterial = await _context.Materials
                    .FirstOrDefaultAsync(m => m.Sku == material.Sku && m.Id != id);

                if (conflictMaterial != null)
                {
                    return BadRequest($"物料SKU '{material.Sku}' 已被其他物料使用");
                }

                existingMaterial.Sku = material.Sku;
                existingMaterial.Name = material.Name;
                existingMaterial.Description = material.Description;
                existingMaterial.Unit = material.Unit;
                existingMaterial.Category = material.Category;
                existingMaterial.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("物料更新成功，ID: {Id}, SKU: {Sku}", id, material.Sku);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新物料失败，ID: {Id}", id);
                return StatusCode(500, "更新物料失败");
            }
        }

        /// <summary>
        /// 删除物料
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Policy = "StorageLocationManagementPolicy")]
        public async Task<IActionResult> DeleteMaterial(int id)
        {
            try
            {
                var material = await _context.Materials.FindAsync(id);
                if (material == null)
                {
                    return NotFound("物料不存在");
                }

                // 检查是否有关联的库存
                var hasInventory = await _context.Inventories
                    .AnyAsync(i => i.MaterialId == id);

                if (hasInventory)
                {
                    return BadRequest("无法删除物料，存在相关库存记录");
                }

                _context.Materials.Remove(material);
                await _context.SaveChangesAsync();

                _logger.LogInformation("物料删除成功，ID: {Id}, SKU: {Sku}", id, material.Sku);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除物料失败，ID: {Id}", id);
                return StatusCode(500, "删除物料失败");
            }
        }

        /// <summary>
        /// 按类别获取物料
        /// </summary>
        [HttpGet("by-category/{category}")]
        public async Task<ActionResult<IEnumerable<Material>>> GetMaterialsByCategory(string category)
        {
            try
            {
                var materials = await _context.Materials
                    .Where(m => m.Category == category)
                    .OrderBy(m => m.Sku)
                    .ToListAsync();

                return Ok(materials);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按类别获取物料失败，类别: {Category}", category);
                return StatusCode(500, "获取物料失败");
            }
        }

        /// <summary>
        /// 搜索物料
        /// </summary>
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<Material>>> SearchMaterials(string? keyword = null)
        {
            try
            {
                var query = _context.Materials.AsQueryable();

                if (!string.IsNullOrWhiteSpace(keyword))
                {
                    query = query.Where(m => 
                        m.Sku.Contains(keyword) || 
                        m.Name.Contains(keyword) || 
                        (m.Description != null && m.Description.Contains(keyword)));
                }

                var materials = await query
                    .OrderBy(m => m.Sku)
                    .ToListAsync();

                return Ok(materials);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索物料失败，关键词: {Keyword}", keyword);
                return StatusCode(500, "搜索物料失败");
            }
        }
    }
}