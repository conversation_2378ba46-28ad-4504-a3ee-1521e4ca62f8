using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 仓库状态枚举
    /// </summary>
    public enum WarehouseStatus
    {
        /// <summary>
        /// 运营中 - 仓库正常运营
        /// </summary>
        Active = 0,

        /// <summary>
        /// 维护中 - 仓库维护暂停运营
        /// </summary>
        Maintenance = 1,

        /// <summary>
        /// 已停用 - 仓库已停用
        /// </summary>
        Inactive = 2,
    }

    /// <summary>
    /// 仓库实体类
    /// 用于管理不同的仓库设施
    /// </summary>
    public class Warehouse
    {
        /// <summary>
        /// 仓库唯一标识ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 仓库编码，如：WH001, WH002
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 仓库描述信息
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 仓库地址
        /// </summary>
        [StringLength(300)]
        public string? Address { get; set; }

        /// <summary>
        /// 仓库类型（如：冷链仓、常温仓、危险品仓）
        /// </summary>
        [StringLength(50)]
        public string? WarehouseType { get; set; }

        /// <summary>
        /// 仓库状态
        /// </summary>
        public WarehouseStatus Status { get; set; } = WarehouseStatus.Active;

        /// <summary>
        /// 仓库总面积（平方米）
        /// </summary>
        public decimal? TotalArea { get; set; }

        /// <summary>
        /// 仓库容量（立方米）
        /// </summary>
        public decimal? TotalVolume { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 配置信息（JSON格式）
        /// 存储仓库级别的配置策略
        /// </summary>
        public string? Configuration { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 关联的区域集合
        /// 一个仓库可以包含多个区域
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<Zone> Zones { get; set; } = new List<Zone>();
    }
}
