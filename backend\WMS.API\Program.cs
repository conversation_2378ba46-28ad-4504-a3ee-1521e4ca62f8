using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using OfficeOpenXml;
using WMS.API.Constants;
using WMS.API.Data;
using WMS.API.Models;
using WMS.API.Services;

// 设置控制台编码为UTF-8
Console.OutputEncoding = Encoding.UTF8;
Console.InputEncoding = Encoding.UTF8;

// 设置EPPlus许可证为非商业用途
ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder
    .Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Encoder = System
            .Text
            .Encodings
            .Web
            .JavaScriptEncoder
            .UnsafeRelaxedJsonEscaping;

        // 处理循环引用
        options.JsonSerializerOptions.ReferenceHandler = System
            .Text
            .Json
            .Serialization
            .ReferenceHandler
            .IgnoreCycles;

        // 忽略null值
        options.JsonSerializerOptions.DefaultIgnoreCondition = System
            .Text
            .Json
            .Serialization
            .JsonIgnoreCondition
            .WhenWritingNull;

        // 使用驼峰命名
        options.JsonSerializerOptions.PropertyNamingPolicy = System
            .Text
            .Json
            .JsonNamingPolicy
            .CamelCase;
    });
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc(
        "v1",
        new OpenApiInfo
        {
            Title = "WMS-VGL API",
            Version = "v1",
            Description = "Visual Guided Logistics Warehouse Management System API",
        }
    );

    // 添加JWT认证配置到Swagger
    options.AddSecurityDefinition(
        "Bearer",
        new OpenApiSecurityScheme
        {
            Description =
                "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer",
        }
    );

    options.AddSecurityRequirement(
        new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer",
                    },
                },
                Array.Empty<string>()
            },
        }
    );
});

// Database configuration
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        npgsqlOptions => npgsqlOptions.CommandTimeout(60)
    )
);

// Identity configuration
builder
    .Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
    {
        // 密码设置
        options.Password.RequireDigit = true;
        options.Password.RequireLowercase = true;
        options.Password.RequireUppercase = true;
        options.Password.RequireNonAlphanumeric = true;
        options.Password.RequiredLength = 8;
        options.Password.RequiredUniqueChars = 1;

        // 锁定设置
        options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
        options.Lockout.MaxFailedAccessAttempts = 5;
        options.Lockout.AllowedForNewUsers = true;

        // 用户设置
        options.User.AllowedUserNameCharacters =
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
        options.User.RequireUniqueEmail = true;

        // 登录设置
        options.SignIn.RequireConfirmedEmail = false;
        options.SignIn.RequireConfirmedPhoneNumber = false;
    })
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddDefaultTokenProviders();

// JWT Authentication configuration
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey =
    jwtSettings["SecretKey"] ?? "WMS-VGL-SuperSecretKey-2024-MinimumLength32Characters!";
var issuer = jwtSettings["Issuer"] ?? "WMS-VGL";
var audience = jwtSettings["Audience"] ?? "WMS-VGL-Client";

builder
    .Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = issuer,
            ValidAudience = audience,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
            ClockSkew = TimeSpan.Zero,
        };

        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<
                    ILogger<Program>
                >();
                logger.LogError("JWT Authentication failed: {Error}", context.Exception?.Message);
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<
                    ILogger<Program>
                >();
                logger.LogDebug(
                    "JWT Token validated for user: {UserId}",
                    context.Principal?.Identity?.Name
                );
                return Task.CompletedTask;
            },
        };
    });

// Authorization policies
builder.Services.AddAuthorization(options =>
{
    // 超级管理员策略
    options.AddPolicy(
        PolicyConstants.SuperAdminPolicy,
        policy => policy.RequireRole(RoleConstants.SuperAdmin)
    );

    // 管理员策略
    options.AddPolicy(
        PolicyConstants.AdminPolicy,
        policy => policy.RequireRole(RoleConstants.SuperAdmin, RoleConstants.Admin)
    );

    // 仓库管理策略
    options.AddPolicy(
        PolicyConstants.WarehousePolicy,
        policy =>
            policy.RequireRole(
                RoleConstants.SuperAdmin,
                RoleConstants.Admin,
                RoleConstants.WarehouseManager
            )
    );

    // 操作员策略
    options.AddPolicy(
        PolicyConstants.OperatorPolicy,
        policy =>
            policy.RequireRole(
                RoleConstants.SuperAdmin,
                RoleConstants.Admin,
                RoleConstants.WarehouseManager,
                RoleConstants.Operator
            )
    );

    // 只读策略 - 需要用户属于任何一个系统角色
    options.AddPolicy(
        PolicyConstants.ReadOnlyPolicy,
        policy =>
            policy.RequireRole(
                RoleConstants.SuperAdmin,
                RoleConstants.Admin,
                RoleConstants.WarehouseManager,
                RoleConstants.Operator,
                RoleConstants.ReadOnly
            )
    );
    // ESP32设备管理策略
    options.AddPolicy(
        PolicyConstants.ESP32ManagementPolicy,
        policy =>
            policy.RequireRole(
                RoleConstants.SuperAdmin,
                RoleConstants.Admin,
                RoleConstants.WarehouseManager
            )
    );

    // 储位管理策略
    options.AddPolicy(
        PolicyConstants.StorageLocationManagementPolicy,
        policy =>
            policy.RequireRole(
                RoleConstants.SuperAdmin,
                RoleConstants.Admin,
                RoleConstants.WarehouseManager
            )
    );
    // 库存管理策略
    options.AddPolicy(
        PolicyConstants.InventoryManagementPolicy,
        policy =>
            policy.RequireRole(
                RoleConstants.SuperAdmin,
                RoleConstants.Admin,
                RoleConstants.WarehouseManager,
                RoleConstants.Operator
            )
    );
    // 用户管理策略
    options.AddPolicy(
        PolicyConstants.UserManagementPolicy,
        policy => policy.RequireRole(RoleConstants.SuperAdmin, RoleConstants.Admin)
    );
    // 系统配置策略
    options.AddPolicy(
        PolicyConstants.SystemConfigPolicy,
        policy => policy.RequireRole(RoleConstants.SuperAdmin)
    );
    // 数据导入导出策略
    options.AddPolicy(
        PolicyConstants.DataImportExportPolicy,
        policy =>
            policy.RequireRole(
                RoleConstants.SuperAdmin,
                RoleConstants.Admin,
                RoleConstants.WarehouseManager
            )
    );
});

// HTTP Client for ESP32 communication
builder.Services.AddHttpClient();

// Custom services
builder.Services.AddScoped<IESP32CommunicationService, ESP32CommunicationService>();
builder.Services.AddScoped<IESP32HealthCheckService, ESP32HealthCheckService>();
builder.Services.AddScoped<IDataSeedService, DataSeedService>();
builder.Services.AddScoped<IJwtTokenService, JwtTokenService>();

// Barcode parsing services
builder.Services.AddScoped<IBarcodeParsingService, BarcodeParsingService>();
builder.Services.AddScoped<IConfigurationResolutionService, ConfigurationResolutionService>();

// Warehouse operations services
builder.Services.AddScoped<IInventoryTransactionService, InventoryTransactionService>();
builder.Services.AddScoped<IInboundService, InboundService>();
builder.Services.AddScoped<IOutboundService, OutboundService>();

// Background service for health checks
builder.Services.AddHostedService<ESP32HealthCheckBackgroundService>();

// CORS policy
builder.Services.AddCors(options =>
{
    options.AddPolicy(
        "AllowAll",
        policy =>
        {
            policy.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader();
        }
    );
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowAll");

// Identity middleware
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Auto-migrate database and seed data on startup
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    context.Database.Migrate();

    // Initialize seed data
    var dataSeedService = scope.ServiceProvider.GetRequiredService<IDataSeedService>();
    await dataSeedService.SeedAsync();
}

app.Run();
