using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 区域状态枚举
    /// </summary>
    public enum ZoneStatus
    {
        /// <summary>
        /// 运营中 - 区域正常运营
        /// </summary>
        Active = 0,

        /// <summary>
        /// 维护中 - 区域维护暂停运营
        /// </summary>
        Maintenance = 1,

        /// <summary>
        /// 已停用 - 区域已停用
        /// </summary>
        Inactive = 2,

        /// <summary>
        /// 锁定中 - 区域被锁定（如安全检查、清点等）
        /// </summary>
        Locked = 3
    }

    /// <summary>
    /// 区域类型枚举
    /// </summary>
    public enum ZoneType
    {
        /// <summary>
        /// 常温区域
        /// </summary>
        Ambient = 0,

        /// <summary>
        /// 冷藏区域 (0-8°C)
        /// </summary>
        Refrigerated = 1,

        /// <summary>
        /// 冷冻区域 (-18°C以下)
        /// </summary>
        Frozen = 2,

        /// <summary>
        /// 危险品区域
        /// </summary>
        Hazardous = 3,

        /// <summary>
        /// 管制品区域（如药品管制区）
        /// </summary>
        Controlled = 4,

        /// <summary>
        /// 隔离区域（如退货、损坏品）
        /// </summary>
        Quarantine = 5,

        /// <summary>
        /// 高价值区域（需要特殊安全措施）
        /// </summary>
        HighValue = 6
    }

    /// <summary>
    /// 区域实体类
    /// 用于管理仓库内的不同功能区域
    /// </summary>
    public class Zone
    {
        /// <summary>
        /// 区域唯一标识ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 区域编码，如：A, B, C, COLD-01, HAZ-01
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 区域名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 区域描述信息
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 关联的仓库ID
        /// </summary>
        public int WarehouseId { get; set; }

        /// <summary>
        /// 关联的仓库实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual Warehouse? Warehouse { get; set; }

        /// <summary>
        /// 区域类型
        /// </summary>
        public ZoneType ZoneType { get; set; } = ZoneType.Ambient;

        /// <summary>
        /// 区域状态
        /// </summary>
        public ZoneStatus Status { get; set; } = ZoneStatus.Active;

        /// <summary>
        /// 区域面积（平方米）
        /// </summary>
        public decimal? Area { get; set; }

        /// <summary>
        /// 区域容量（立方米）
        /// </summary>
        public decimal? Volume { get; set; }

        /// <summary>
        /// 最大载重（公斤）
        /// </summary>
        public decimal? MaxWeight { get; set; }

        /// <summary>
        /// 温度范围（JSON格式存储最低温度和最高温度）
        /// 例如：{"min": 2, "max": 8, "unit": "°C"}
        /// </summary>
        public string? TemperatureRange { get; set; }

        /// <summary>
        /// 湿度范围（JSON格式存储最低湿度和最高湿度）
        /// 例如：{"min": 45, "max": 75, "unit": "%"}
        /// </summary>
        public string? HumidityRange { get; set; }

        /// <summary>
        /// 安全等级（1-5级，5级最高）
        /// </summary>
        public int SecurityLevel { get; set; } = 1;

        /// <summary>
        /// 访问控制要求（JSON格式）
        /// 例如：{"authMethod": "biometric", "approvalRequired": true}
        /// </summary>
        public string? AccessControl { get; set; }

        /// <summary>
        /// 特殊要求（JSON格式）
        /// 例如防火、防爆、通风等特殊要求
        /// </summary>
        public string? SpecialRequirements { get; set; }

        /// <summary>
        /// 配置信息（JSON格式）
        /// 存储区域级别的配置策略
        /// </summary>
        public string? Configuration { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        [StringLength(50)]
        public string? ResponsiblePerson { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [StringLength(50)]
        public string? ContactInfo { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 关联的货架集合
        /// 一个区域可以包含多个货架
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<Shelf> Shelves { get; set; } = new List<Shelf>();
    }
}