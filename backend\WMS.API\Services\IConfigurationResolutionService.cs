using WMS.API.Models;

namespace WMS.API.Services
{
    /// <summary>
    /// 配置解析上下文
    /// </summary>
    public class ConfigurationContext
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 区域代码
        /// </summary>
        public string? ZoneCode { get; set; }

        /// <summary>
        /// 货架代码
        /// </summary>
        public string? ShelfCode { get; set; }

        /// <summary>
        /// 储位代码
        /// </summary>
        public string? LocationCode { get; set; }

        /// <summary>
        /// 物料类别
        /// </summary>
        public string? MaterialCategory { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string? OperationType { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }
    }

    /// <summary>
    /// 配置解析结果
    /// </summary>
    public class ConfigurationResolutionResult
    {
        /// <summary>
        /// 配置键
        /// </summary>
        public string ConfigKey { get; set; } = string.Empty;

        /// <summary>
        /// 最终配置值
        /// </summary>
        public string ConfigValue { get; set; } = "{}";

        /// <summary>
        /// 生效的配置层级
        /// </summary>
        public ConfigLevel? AppliedConfigLevel { get; set; }

        /// <summary>
        /// 生效的配置ID
        /// </summary>
        public int? AppliedConfigId { get; set; }

        /// <summary>
        /// 配置解析路径
        /// </summary>
        public string? ResolutionPath { get; set; }

        /// <summary>
        /// 是否有冲突
        /// </summary>
        public bool HasConflicts { get; set; }

        /// <summary>
        /// 冲突解决策略
        /// </summary>
        public string? ConflictResolutionStrategy { get; set; }
    }

    /// <summary>
    /// 配置解析服务接口
    /// </summary>
    public interface IConfigurationResolutionService
    {
        /// <summary>
        /// 解析单个配置项
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <param name="context">配置上下文</param>
        /// <returns>配置解析结果</returns>
        Task<ConfigurationResolutionResult> ResolveConfigurationAsync(
            string configKey,
            ConfigurationContext context
        );

        /// <summary>
        /// 批量解析配置项
        /// </summary>
        /// <param name="configKeys">配置键列表</param>
        /// <param name="context">配置上下文</param>
        /// <returns>配置解析结果字典</returns>
        Task<Dictionary<string, ConfigurationResolutionResult>> ResolveConfigurationsAsync(
            IEnumerable<string> configKeys,
            ConfigurationContext context
        );

        /// <summary>
        /// 获取客户的所有有效配置
        /// </summary>
        /// <param name="clientId">客户ID</param>
        /// <returns>配置列表</returns>
        Task<List<HierarchicalConfiguration>> GetClientConfigurationsAsync(string clientId);

        /// <summary>
        /// 获取特定目标的配置
        /// </summary>
        /// <param name="context">配置上下文</param>
        /// <returns>适用的配置列表</returns>
        Task<List<HierarchicalConfiguration>> GetApplicableConfigurationsAsync(
            ConfigurationContext context
        );

        /// <summary>
        /// 记录配置应用日志
        /// </summary>
        /// <param name="operationId">操作ID</param>
        /// <param name="context">配置上下文</param>
        /// <param name="results">配置解析结果</param>
        /// <returns>是否成功</returns>
        Task<bool> LogConfigurationApplicationAsync(
            string operationId,
            ConfigurationContext context,
            Dictionary<string, ConfigurationResolutionResult> results
        );
    }
}
