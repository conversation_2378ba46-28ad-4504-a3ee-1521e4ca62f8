# 仓库层级结构系统测试文档

## 📋 目录
1. [系统概述](#系统概述)
2. [新手入门指南](#新手入门指南)
3. [层级结构详解](#层级结构详解)
4. [数据模型说明](#数据模型说明)
5. [测试环境准备](#测试环境准备)
6. [基础数据创建测试](#基础数据创建测试)
7. [层级关系测试](#层级关系测试)
8. [能力系统测试](#能力系统测试)
9. [分类系统测试](#分类系统测试)
10. [配置继承测试](#配置继承测试)
11. [业务场景测试](#业务场景测试)
12. [常见问题解答](#常见问题解答)

---

## 新手入门指南

### 🚀 系统刚上线？从零开始配置完整指南

**假设你是第一次使用WMS-VGL系统，这里是完整的配置步骤：**

### 第一步：启动系统

**1.1 启动数据库**：
```bash
# 在WSL中执行
powershell.exe -Command "cd 'D:\\ProjestFiles\\ESP\\WMS-VGL'; docker-compose up -d wms-postgres"
```

**1.2 启动API服务**：
```bash
# 启动后端API
powershell.exe -Command "cd 'D:\\ProjestFiles\\ESP\\WMS-VGL\\backend\\WMS.API'; dotnet run --urls http://0.0.0.0:5000"
```

**1.3 验证服务启动**：
```bash
# 检查API是否可访问
curl http://************:5000/api/health
```

### 第二步：首次登录

**2.1 使用默认管理员账号登录**：
```bash
# 获取JWT Token
TOKEN_RESPONSE=$(curl -s -X POST "http://************:5000/api/Auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin@123456",
    "rememberMe": true
  }')

# 提取token
TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"accessToken":"[^"]*"' | sed 's/"accessToken":\"\(.*\)\"/\1/')
echo "认证Token: $TOKEN"
```

⚠️ **安全提醒**：请在首次登录后立即修改默认密码！

### 第三步：添加ESP32控制器

**为什么需要ESP32？**
WMS-VGL是基于LED灯光指引的仓库管理系统，ESP32控制器负责控制LED灯带。

**3.1 添加第一个ESP32控制器**：
```bash
curl -X POST "http://************:5000/api/ESP32Controllers" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "仓库主控制器",
    "description": "负责A区LED灯带控制",
    "ipAddress": "*************",
    "port": 80,
    "mdnsName": "wms-esp32-main"
  }'
```

**3.2 测试ESP32连接**：
```bash
# 测试控制器连接状态
curl -X POST "http://************:5000/api/ESP32Controllers/1/test-connection" \
  -H "Authorization: Bearer $TOKEN"
```

### 第四步：创建基础仓库结构

**4.1 创建你的第一个仓库**：
```bash
curl -X POST "http://************:5000/api/Warehouses" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "WH001",
    "name": "主仓库",
    "description": "公司主要仓储设施",
    "address": "北京市朝阳区科技园路123号",
    "warehouseType": "配送中心",
    "status": 1,
    "totalArea": 2000.0,
    "totalVolume": 6000.0,
    "contactPerson": "张经理",
    "contactPhone": "13800138000",
    "configuration": "{\"operatingHours\":\"08:00-20:00\",\"temperatureMonitoring\":true,\"securityLevel\":2}"
  }'
```

**4.2 创建功能区域**：

*常温存储区*：
```bash
curl -X POST "http://************:5000/api/Zones" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "A-ZONE",
    "name": "A区-常温存储",
    "description": "常温货物存储区域",
    "warehouseId": 2,
    "zoneType": 1,
    "status": 1,
    "area": 800.0,
    "volume": 2400.0,
    "maxWeight": 50000.0,
    "securityLevel": 2,
    "configuration": "{\"pickingOptimization\":true,\"autoReplenishment\":true}"
  }'
```

*收发货区*：
```bash
curl -X POST "http://************:5000/api/Zones" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "DOCK",
    "name": "收发货区",
    "description": "货物收发作业区域",
    "warehouseId": 2,
    "zoneType": 6,
    "status": 1,
    "area": 200.0,
    "volume": 600.0,
    "maxWeight": 20000.0,
    "securityLevel": 1,
    "configuration": "{\"fastTurnover\":true,\"temporaryStorage\":true}"
  }'
```

**4.3 添加货架**：

*标准货架*：
```bash
curl -X POST "http://************:5000/api/Shelves" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "A-RACK-001",
    "name": "A区标准货架001",
    "description": "五层标准货架，适用于中小件货物",
    "zoneId": 3,
    "shelfType": 1,
    "status": 1,
    "length": 2.4,
    "width": 1.2,
    "height": 2.5,
    "levels": 5,
    "positionsPerLevel": 6,
    "maxWeight": 3000.0,
    "maxWeightPerPosition": 100.0,
    "material": "钢制",
    "manufacturer": "华北货架",
    "configuration": "{\"allowMixedSKU\":true,\"fifoRequired\":false}"
  }'
```

*收货暂存架*：
```bash
curl -X POST "http://************:5000/api/Shelves" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "DOCK-TEMP-001",
    "name": "收货暂存架001",
    "description": "收货临时存放货架",
    "zoneId": 4,
    "shelfType": 5,
    "status": 1,
    "length": 3.0,
    "width": 1.5,
    "height": 2.0,
    "levels": 3,
    "positionsPerLevel": 8,
    "maxWeight": 2000.0,
    "maxWeightPerPosition": 80.0,
    "material": "钢制",
    "configuration": "{\"temporaryStorage\":true,\"fastTurnover\":true}"
  }'
```

### 第五步：创建储位并配置LED

**5.1 了解LED配置参数**：

| 参数 | 说明 | 示例 | 备注 |
|------|------|------|------|
| `esp32ControllerId` | ESP32控制器ID | 1 | 必须是已存在的控制器 |
| `startLedPosition` | LED起始位置 | 0 | 从0开始计数 |
| `endLedPosition` | LED结束位置 | 9 | 表示控制10个LED灯 |
| `ledChannel` | LED输出通道 | 0 | ESP32支持0-3四个通道 |

**LED位置规划建议**：
- 每个储位分配10-20个LED灯
- 不同货架使用不同通道
- 预留扩展空间

**5.2 创建储位（单个）**：
```bash
curl -X POST "http://************:5000/api/StorageLocations" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "A001-L01-P01",
    "description": "A区货架001第1层第1位",
    "shelfId": 5,
    "esp32ControllerId": 1,
    "startLedPosition": 0,
    "endLedPosition": 9,
    "ledChannel": 0,
    "status": 0,
    "level": "L01",
    "zone": "A",
    "aisle": "001",
    "shelfCode": "RACK-001"
  }'
```

**5.3 批量创建储位**：
```bash
# 为标准货架创建30个储位（5层×6位）
SHELF_ID=5
ESP32_ID=1
CHANNEL=0
LED_START=0

for level in {1..5}; do
  for position in {1..6}; do
    STORAGE_CODE="A001-L$(printf %02d $level)-P$(printf %02d $position)"
    POSITION_NAME="A区货架001第${level}层第${position}位"
    
    curl -X POST "http://************:5000/api/StorageLocations" \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d "{
        \"code\": \"$STORAGE_CODE\",
        \"description\": \"$POSITION_NAME\",
        \"shelfId\": $SHELF_ID,
        \"esp32ControllerId\": $ESP32_ID,
        \"startLedPosition\": $LED_START,
        \"endLedPosition\": $((LED_START + 9)),
        \"ledChannel\": $CHANNEL,
        \"status\": 0,
        \"level\": \"L$(printf %02d $level)\",
        \"zone\": \"A\",
        \"aisle\": \"001\",
        \"shelfCode\": \"RACK-001\"
      }"
    
    LED_START=$((LED_START + 10))
    echo "创建储位: $STORAGE_CODE (LED: $((LED_START-10))-$((LED_START-1)))"
    sleep 0.5
  done
done
```

### 第六步：测试LED控制

**6.1 测试单个储位LED**：
```bash
# 点亮第一个储位（绿色）
curl -X POST "http://************:5000/api/StorageLocations/1/test-light" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "color": "green",
    "brightness": 200
  }'

# 等待3秒后关闭
sleep 3
curl -X POST "http://************:5000/api/StorageLocations/1/turn-off-light" \
  -H "Authorization: Bearer $TOKEN"
```

**6.2 测试批量LED控制**：
```bash
# 点亮一行储位（模拟拣货路径）
for i in {1..6}; do
  curl -X POST "http://************:5000/api/StorageLocations/$i/test-light" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "color": "blue",
      "brightness": 255
    }'
  sleep 0.5
done

# 等待5秒后全部关闭
sleep 5
for i in {1..6}; do
  curl -X POST "http://************:5000/api/StorageLocations/$i/turn-off-light" \
    -H "Authorization: Bearer $TOKEN"
  sleep 0.2
done
```

### 第七步：验证系统配置

**7.1 查看完整层级结构**：
```bash
# 查看所有仓库
echo "=== 仓库列表 ==="
curl -s -X GET "http://************:5000/api/Warehouses" \
  -H "Authorization: Bearer $TOKEN" | jq '.[] | {id, code, name}'

# 查看区域
echo "\n=== 区域列表 ==="
curl -s -X GET "http://************:5000/api/Zones" \
  -H "Authorization: Bearer $TOKEN" | jq '.[] | {id, code, name, warehouseId}'

# 查看货架
echo "\n=== 货架列表 ==="
curl -s -X GET "http://************:5000/api/Shelves" \
  -H "Authorization: Bearer $TOKEN" | jq '.[] | {id, code, name, zoneId}'

# 查看储位（前10个）
echo "\n=== 储位列表（前10个） ==="
curl -s -X GET "http://************:5000/api/StorageLocations" \
  -H "Authorization: Bearer $TOKEN" | jq '.[0:10] | .[] | {id, code, shelfId, esp32ControllerId, startLedPosition, endLedPosition}'
```

**7.2 验证ESP32控制器状态**：
```bash
echo "=== ESP32控制器状态 ==="
curl -s -X GET "http://************:5000/api/ESP32Controllers" \
  -H "Authorization: Bearer $TOKEN" | jq '.[] | {id, name, ipAddress, isOnline, lastHeartbeat}'
```

### 第八步：创建快速配置脚本

**8.1 保存配置脚本**：
```bash
# 创建一键配置脚本
cat > setup_wms_system.sh << 'EOF'
#!/bin/bash

# WMS-VGL 系统一键配置脚本
echo "🚀 开始配置WMS-VGL系统..."

# 获取认证token
echo "📝 正在登录系统..."
TOKEN=$(curl -s -X POST "http://************:5000/api/Auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "Admin@123456", "rememberMe": true}' \
  | grep -o '"accessToken":"[^"]*"' | sed 's/"accessToken":\"\(.*\)\"/\1/')

if [ -z "$TOKEN" ]; then
  echo "❌ 登录失败，请检查系统是否正常运行"
  exit 1
fi

echo "✅ 登录成功"

# 添加ESP32控制器
echo "🔌 正在添加ESP32控制器..."
curl -s -X POST "http://************:5000/api/ESP32Controllers" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "主控制器",
    "description": "仓库主LED控制器",
    "ipAddress": "*************",
    "port": 80,
    "mdnsName": "wms-main"
  }' > /dev/null

echo "✅ ESP32控制器添加完成"

# 创建基础结构
echo "🏭 正在创建基础仓库结构..."

# 创建仓库、区域、货架、储位的完整配置...
# （这里可以放置完整的配置命令）

echo "🎉 WMS-VGL系统配置完成！"
echo "📊 系统概览："
echo "   - 仓库：1个"
echo "   - 区域：2个"
echo "   - 货架：2个"
echo "   - 储位：48个"
echo "   - ESP32控制器：1个"
EOF

chmod +x setup_wms_system.sh
```

### 🎯 新手配置检查清单

配置完成后，请检查以下项目：

- [ ] ✅ API服务正常运行 (http://************:5000)
- [ ] ✅ 数据库连接正常
- [ ] ✅ 管理员账号可以登录
- [ ] ✅ ESP32控制器添加成功
- [ ] ✅ 仓库结构创建完整（仓库→区域→货架→储位）
- [ ] ✅ LED灯带可以正常控制
- [ ] ✅ 储位数据查询正常
- [ ] ✅ 系统日志无错误信息

### 📞 遇到问题怎么办？

1. **API无法访问**：检查防火墙和网络配置
2. **ESP32连接失败**：确认IP地址和网络连通性
3. **LED不亮**：检查硬件连接和GPIO配置
4. **数据库错误**：检查PostgreSQL服务状态
5. **权限问题**：确认使用正确的JWT Token

### 🔄 定期维护建议

- **每日**：检查ESP32控制器在线状态
- **每周**：备份数据库，检查系统日志
- **每月**：更新系统配置，优化储位布局
- **季度**：硬件维护，LED灯带检查

---

## 系统概述

### 🎯 什么是仓库层级结构系统？

WMS-VGL（Visual Guided Logistics）仓库管理系统采用四级层级结构来组织和管理仓库空间：

```
🏭 Warehouse (仓库) 
└── 🏢 Zone (区域)
    └── 📚 Shelf (货架)
        └── 📦 StorageLocation (储位)
            ├── ⚡ LocationCapability (储位能力)
            └── 🏷️ LocationClassification (储位分类)
```

### 🤔 为什么需要层级结构？

**简单来说**：就像组织电脑文件夹一样，我们需要把仓库空间按照逻辑关系组织起来。

**实际好处**：
- 📍 **空间定位**：快速找到货物存放位置
- 🔧 **配置管理**：不同区域有不同的管理规则
- 🏭 **多行业支持**：医疗、电商、制造业有不同需求
- 📊 **数据分析**：按区域、货架进行统计分析
- 🚦 **LED控制**：精确控制指示灯显示

### 🏢 现实世界类比

想象一个大型超市：
- **仓库** = 整个超市
- **区域** = 生鲜区、服装区、电器区
- **货架** = 每排具体的货架
- **储位** = 货架上的具体位置

---

## 层级结构详解

### 🏭 第一层：Warehouse (仓库)

**作用**：整个仓库设施的最高层容器

**现实例子**：
- 北京仓库、上海仓库、广州仓库
- 主仓库、备用仓库、临时仓库

**关键属性**：
- `Code`: 仓库编码 (如: "BJ001", "SH001")
- `Name`: 仓库名称 (如: "北京主仓库")
- `Address`: 地址
- `WarehouseType`: 类型 (如: "配送中心", "中转仓")
- `Configuration`: 全局配置（JSON格式）

### 🏢 第二层：Zone (区域)

**作用**：仓库内的功能区域划分

**现实例子**：
- 收货区、存储区、拣货区、发货区
- 常温区、冷藏区、冷冻区
- 普通区、贵重品区、危险品区

**关键属性**：
- `Code`: 区域编码 (如: "A01", "COLD", "VIP")
- `ZoneType`: 区域类型 (常温/冷藏/冷冻/危险品等)
- `TemperatureRange`: 温度范围 (如: 2-8°C)
- `SecurityLevel`: 安全等级 (1-5级)
- `AccessControl`: 访问控制规则

### 📚 第三层：Shelf (货架)

**作用**：具体的存储设施

**现实例子**：
- 标准货架、重型货架、悬臂货架
- 流利式货架、穿梭式货架
- 自动化立体货架

**关键属性**：
- `Code`: 货架编码 (如: "A01-S01", "RACK-001")
- `ShelfType`: 货架类型 (标准/重型/悬臂等)
- `Dimensions`: 尺寸 (长x宽x高)
- `MaxWeight`: 最大承重
- `SafetyCertifications`: 安全认证

### 📦 第四层：StorageLocation (储位)

**作用**：具体的存放位置，可以控制LED灯

**现实例子**：
- A区01货架第2层第3位
- 冷藏区货架顶层左侧
- 重型货架底层中间位置

**关键属性**：
- `Code`: 储位编码 (如: "A01-S01-L02-P03")
- `ESP32ControllerId`: 控制器ID
- `StartLedPosition`: LED起始位置
- `EndLedPosition`: LED结束位置
- `LedChannel`: LED通道号

---

## 数据模型说明

### 🔗 关系图解

```mermaid
erDiagram
    Warehouse ||--o{ Zone : contains
    Zone ||--o{ Shelf : contains
    Shelf ||--o{ StorageLocation : contains
    StorageLocation ||--o{ LocationCapability : has
    StorageLocation ||--o{ LocationClassification : has
    StorageLocation ||--o{ ESP32Controller : controlled_by
```

### 📊 核心字段说明

#### Warehouse 表
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| Id | int | 主键 | 1 |
| Code | string(20) | 仓库编码 | "BJ001" |
| Name | string(100) | 仓库名称 | "北京主仓库" |
| Status | enum | 状态 | Active/Inactive |
| Configuration | jsonb | 配置信息 | {"temperatureControl": true} |

#### Zone 表
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| Id | int | 主键 | 1 |
| Code | string(20) | 区域编码 | "COLD-A" |
| Name | string(100) | 区域名称 | "冷藏区A" |
| WarehouseId | int | 所属仓库ID | 1 |
| ZoneType | enum | 区域类型 | Refrigerated |
| TemperatureRange | jsonb | 温度范围 | {"min": 2, "max": 8} |

#### Shelf 表
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| Id | int | 主键 | 1 |
| Code | string(20) | 货架编码 | "RACK-001" |
| Name | string(100) | 货架名称 | "标准货架001" |
| ZoneId | int | 所属区域ID | 1 |
| ShelfType | enum | 货架类型 | Standard |
| MaxWeight | decimal | 最大承重(kg) | 1000.00 |

#### StorageLocation 表
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| Id | int | 主键 | 1 |
| Code | string(50) | 储位编码 | "A01-S01-L02-P03" |
| ShelfId | int | 所属货架ID | 1 |
| ESP32ControllerId | int | 控制器ID | 1 |
| StartLedPosition | int | LED起始位置 | 0 |
| EndLedPosition | int | LED结束位置 | 10 |
| LedChannel | int | LED通道号 | 0 |

---

## 测试环境准备

### 🛠️ 1. 数据库准备

**检查迁移状态**：
```bash
# 在WSL中执行
powershell.exe -Command "cd 'D:\\ProjestFiles\\ESP\\WMS-VGL\\backend\\WMS.API'; dotnet ef database update"
```

**验证表结构**：
```sql
-- 连接数据库后执行
\dt  -- 列出所有表
\d "Warehouses"  -- 查看Warehouses表结构
\d "Zones"       -- 查看Zones表结构
\d "Shelves"     -- 查看Shelves表结构
\d "StorageLocations"  -- 查看StorageLocations表结构
```

### 🔐 2. 认证准备

**获取JWT Token**：
```bash
curl -X POST "http://localhost:5000/api/Auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin@123456",
    "rememberMe": true
  }'
```

**保存Token**：
```bash
# 响应示例
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "abc123...",
  "expiration": "2025-07-04T12:00:00Z"
}

# 后续请求中使用
export JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

---

## 基础数据创建测试

### 🏭 1. 创建仓库

**目标**：创建一个名为"测试仓库"的仓库

**API调用**：
```bash
curl -X POST "http://localhost:5000/api/Warehouses" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "TEST001",
    "name": "测试仓库",
    "description": "用于系统测试的仓库",
    "address": "北京市海淀区测试路123号",
    "warehouseType": "测试中心",
    "status": 1,
    "totalArea": 5000.0,
    "totalVolume": 15000.0,
    "contactPerson": "张测试",
    "contactPhone": "13800138000",
    "configuration": {
      "operatingHours": "24/7",
      "temperatureMonitoring": true,
      "securityLevel": 2
    }
  }'
```

**预期结果**：
```json
{
  "id": 2,
  "code": "TEST001",
  "name": "测试仓库",
  "status": 1,
  "createdAt": "2025-07-04T03:30:00Z",
  "updatedAt": "2025-07-04T03:30:00Z"
}
```

**验证方法**：
```bash
# 查询所有仓库
curl -X GET "http://localhost:5000/api/Warehouses" \
  -H "Authorization: Bearer $JWT_TOKEN"

# 查询特定仓库
curl -X GET "http://localhost:5000/api/Warehouses/2" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### 🏢 2. 创建区域

**目标**：在测试仓库下创建多个功能区域

**2.1 创建常温存储区**：
```bash
curl -X POST "http://localhost:5000/api/Zones" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "AMBIENT-A",
    "name": "常温存储区A",
    "description": "常温货物存储区域",
    "warehouseId": 2,
    "zoneType": 1,
    "status": 1,
    "area": 1000.0,
    "volume": 3000.0,
    "maxWeight": 50000.0,
    "temperatureRange": {
      "min": 15,
      "max": 25,
      "unit": "celsius"
    },
    "securityLevel": 2,
    "configuration": {
      "autoReplenishment": true,
      "pickingOptimization": true
    }
  }'
```

**2.2 创建冷藏区**：
```bash
curl -X POST "http://localhost:5000/api/Zones" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "COLD-A",
    "name": "冷藏区A",
    "description": "冷藏货物存储区域",
    "warehouseId": 2,
    "zoneType": 2,
    "status": 1,
    "area": 500.0,
    "volume": 1500.0,
    "maxWeight": 20000.0,
    "temperatureRange": {
      "min": 2,
      "max": 8,
      "unit": "celsius"
    },
    "securityLevel": 3,
    "configuration": {
      "temperatureAlerts": true,
      "continuousMonitoring": true
    }
  }'
```

**验证方法**：
```bash
# 查询仓库下的所有区域
curl -X GET "http://localhost:5000/api/Zones/by-warehouse/2" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### 📚 3. 创建货架

**目标**：在各区域下创建不同类型的货架

**3.1 在常温区创建标准货架**：
```bash
curl -X POST "http://localhost:5000/api/Shelves" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "RACK-A001",
    "name": "常温区标准货架001",
    "description": "五层标准货架，适用于箱装货物",
    "zoneId": 3,
    "shelfType": 1,
    "status": 1,
    "length": 2.5,
    "width": 1.0,
    "height": 2.5,
    "levels": 5,
    "positionsPerLevel": 4,
    "maxWeight": 2000.0,
    "maxWeightPerPosition": 100.0,
    "material": "钢制",
    "manufacturer": "标准货架公司",
    "configuration": {
      "allowMixedSKU": true,
      "fifoRequired": false
    }
  }'
```

**3.2 在冷藏区创建冷藏专用货架**：
```bash
curl -X POST "http://localhost:5000/api/Shelves" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "COLD-R001",
    "name": "冷藏区专用货架001",
    "description": "不锈钢冷藏货架，防腐蚀",
    "zoneId": 4,
    "shelfType": 2,
    "status": 1,
    "length": 2.0,
    "width": 0.8,
    "height": 2.0,
    "levels": 4,
    "positionsPerLevel": 3,
    "maxWeight": 1500.0,
    "maxWeightPerPosition": 125.0,
    "material": "不锈钢",
    "manufacturer": "冷藏设备公司",
    "safetyCertifications": {
      "foodGrade": true,
      "certificationNumber": "FDA-2025-001"
    },
    "configuration": {
      "temperatureLogging": true,
      "allowMixedSKU": false,
      "fifoRequired": true
    }
  }'
```

**验证方法**：
```bash
# 查询区域下的所有货架
curl -X GET "http://localhost:5000/api/Shelves/by-zone/3" \
  -H "Authorization: Bearer $JWT_TOKEN"

curl -X GET "http://localhost:5000/api/Shelves/by-zone/4" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### 📦 4. 创建储位

**目标**：在货架上创建具体的储位，连接LED控制

**4.1 在标准货架上创建储位**：
```bash
curl -X POST "http://localhost:5000/api/StorageLocations" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "A001-L01-P01",
    "name": "常温区货架001第1层第1位",
    "description": "标准储位，适合箱装货物",
    "shelfId": 5,
    "esp32ControllerId": 1,
    "startLedPosition": 0,
    "endLedPosition": 10,
    "ledChannel": 0,
    "status": 0,
    "capacity": 0.25,
    "maxWeight": 100.0,
    "dimensions": {
      "length": 0.5,
      "width": 1.0,
      "height": 0.5,
      "unit": "meter"
    },
    "level": "L01",
    "position": "P01"
  }'
```

**4.2 批量创建储位**：
```bash
# 创建第1层的4个储位
for i in {1..4}; do
  curl -X POST "http://localhost:5000/api/StorageLocations" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"code\": \"A001-L01-P$(printf %02d $i)\",
      \"name\": \"常温区货架001第1层第${i}位\",
      \"shelfId\": 5,
      \"esp32ControllerId\": 1,
      \"startLedPosition\": $((($i-1)*10)),
      \"endLedPosition\": $(($i*10-1)),
      \"ledChannel\": 0,
      \"status\": 0,
      \"level\": \"L01\",
      \"position\": \"P$(printf %02d $i)\"
    }"
  sleep 1
done
```

**验证方法**：
```bash
# 查询货架下的所有储位
curl -X GET "http://localhost:5000/api/StorageLocations/by-shelf/5" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

---

## 层级关系测试

### 🔗 1. 验证层级完整性

**目标**：确保所有层级关系正确建立

**测试步骤**：

**1.1 查询完整层级信息**：
```bash
# 查询储位的完整层级信息
curl -X GET "http://localhost:5000/api/StorageLocations/1" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  | jq '.shelf.zone.warehouse'
```

**预期结果**：
```json
{
  "storageLocation": {
    "id": 1,
    "code": "A001-L01-P01",
    "shelf": {
      "id": 5,
      "code": "RACK-A001",
      "zone": {
        "id": 3,
        "code": "AMBIENT-A",
        "warehouse": {
          "id": 2,
          "code": "TEST001",
          "name": "测试仓库"
        }
      }
    }
  }
}
```

**1.2 验证外键约束**：
```bash
# 尝试删除有子记录的仓库（应该失败）
curl -X DELETE "http://localhost:5000/api/Warehouses/2" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

**预期结果**：
```json
{
  "error": "Cannot delete warehouse with existing zones"
}
```

### 📊 2. 统计查询测试

**目标**：验证层级统计功能

**2.1 仓库统计**：
```bash
curl -X GET "http://localhost:5000/api/Warehouses/2/statistics" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

**预期结果**：
```json
{
  "warehouseId": 2,
  "totalZones": 2,
  "totalShelves": 2,
  "totalStorageLocations": 8,
  "utilizationRate": 0.0,
  "zoneBreakdown": [
    {
      "zoneId": 3,
      "zoneName": "常温存储区A",
      "shelves": 1,
      "storageLocations": 4
    },
    {
      "zoneId": 4,
      "zoneName": "冷藏区A", 
      "shelves": 1,
      "storageLocations": 4
    }
  ]
}
```

---

## 能力系统测试

### ⚡ 1. 储位能力定义

**目标**：为储位定义特殊能力

**1.1 为冷藏储位添加温度控制能力**：
```bash
curl -X POST "http://localhost:5000/api/StorageLocations/5/capabilities" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "capabilityType": 1,
    "capabilityLevel": 3,
    "capabilityName": "温度控制",
    "description": "精确温度控制，适合生鲜食品存储",
    "parameters": {
      "targetTemperature": 4,
      "tolerance": 1,
      "unit": "celsius",
      "monitoringInterval": 300
    },
    "isEnabled": true,
    "priority": 1,
    "validationRules": {
      "temperatureRange": {"min": 2, "max": 8},
      "alertThreshold": 0.5
    },
    "effectiveFrom": "2025-07-04T00:00:00Z",
    "certifications": {
      "haccp": true,
      "iso22000": true
    }
  }'
```

**1.2 添加危险品存储能力**：
```bash
curl -X POST "http://localhost:5000/api/StorageLocations/6/capabilities" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "capabilityType": 2,
    "capabilityLevel": 4,
    "capabilityName": "危险品存储",
    "description": "符合危险品存储规范的储位",
    "parameters": {
      "hazmatClasses": ["3", "8"],
      "ventilationRequired": true,
      "fireSuppressionSystem": "foam"
    },
    "isEnabled": true,
    "priority": 1,
    "validationRules": {
      "incompatibleMaterials": ["oxidizers", "water_reactive"],
      "quantityLimit": 100,
      "inspectionFrequency": "weekly"
    },
    "certifications": {
      "un": "UN3082",
      "dot": "DOT-HM-001"
    }
  }'
```

### 🔍 2. 能力查询测试

**目标**：根据能力查找储位

**2.1 查询具有温度控制能力的储位**：
```bash
curl -X GET "http://localhost:5000/api/StorageLocations/by-capability/1" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

**2.2 查询具有危险品存储能力的储位**：
```bash
curl -X GET "http://localhost:5000/api/StorageLocations/by-capability/2" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

---

## 分类系统测试

### 🏷️ 1. 多维度分类

**目标**：为储位添加业务分类

**1.1 添加行业分类**：
```bash
curl -X POST "http://localhost:5000/api/StorageLocations/5/classifications" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dimension": 1,
    "category": "Industry",
    "value": "Food",
    "displayName": "食品行业",
    "description": "适用于食品存储的储位",
    "tags": ["food-grade", "haccp-compliant"],
    "properties": {
      "temperatureControlled": true,
      "humidityControlled": false,
      "sanitationRequired": true
    },
    "priority": 1,
    "isEnabled": true,
    "businessRules": {
      "autoAssignment": true,
      "materialTypes": ["fresh", "frozen", "dry_goods"],
      "restrictions": ["no_chemicals", "no_non_food"]
    }
  }'
```

**1.2 添加材料类型分类**：
```bash
curl -X POST "http://localhost:5000/api/StorageLocations/6/classifications" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dimension": 2,
    "category": "MaterialType", 
    "value": "Chemicals",
    "displayName": "化学品",
    "description": "化学品专用储位",
    "tags": ["hazmat", "restricted-access"],
    "properties": {
      "ventilationRequired": true,
      "fireSuppressionSystem": true,
      "temperatureControlled": false
    },
    "priority": 1,
    "isEnabled": true,
    "businessRules": {
      "autoAssignment": false,
      "approvalRequired": true,
      "inspectionFrequency": "daily"
    }
  }'
```

### 🔍 2. 分类查询测试

**目标**：根据分类查找储位

**2.1 查询食品行业储位**：
```bash
curl -X GET "http://localhost:5000/api/StorageLocations/by-classification/Industry/Food" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

**2.2 查询化学品储位**：
```bash
curl -X GET "http://localhost:5000/api/StorageLocations/by-classification/MaterialType/Chemicals" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

---

## 配置继承测试

### 🔧 1. 层级配置设置

**目标**：测试配置从上级向下级继承

**1.1 设置仓库级配置**：
```bash
curl -X PUT "http://localhost:5000/api/Configuration/warehouse/2" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "globalSettings": {
      "operatingHours": "06:00-22:00",
      "defaultLanguage": "zh-CN",
      "temperatureUnit": "celsius",
      "weightUnit": "kg"
    },
    "securitySettings": {
      "accessCardRequired": true,
      "biometricAuth": false,
      "visitorEscortRequired": true
    },
    "operationalSettings": {
      "autoReplenishment": true,
      "cycleCounting": "weekly",
      "qualityCheck": "random"
    }
  }'
```

**1.2 设置区域级配置（覆盖仓库配置）**：
```bash
curl -X PUT "http://localhost:5000/api/Configuration/zone/4" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "temperatureSettings": {
      "operatingHours": "24:00-24:00",
      "monitoringInterval": 300,
      "alertThreshold": 0.5,
      "emergencyProtocol": "immediate_notification"
    },
    "securitySettings": {
      "biometricAuth": true,
      "additionalApproval": true
    },
    "operationalSettings": {
      "cycleCounting": "daily",
      "fifoEnforcement": true
    }
  }'
```

### 🔍 2. 配置解析测试

**目标**：验证配置继承和解析逻辑

**2.1 查询储位的有效配置**：
```bash
curl -X GET "http://localhost:5000/api/Configuration/resolve/5" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

**预期结果**：
```json
{
  "storageLocationId": 5,
  "effectiveConfiguration": {
    "operatingHours": "24:00-24:00",  // 从区域级继承
    "defaultLanguage": "zh-CN",        // 从仓库级继承
    "temperatureUnit": "celsius",      // 从仓库级继承
    "biometricAuth": true,             // 从区域级覆盖
    "cycleCounting": "daily"           // 从区域级覆盖
  },
  "resolutionPath": [
    {"level": "warehouse", "applied": ["defaultLanguage", "temperatureUnit", "weightUnit"]},
    {"level": "zone", "applied": ["operatingHours", "biometricAuth", "cycleCounting"], "overridden": ["operatingHours", "biometricAuth", "cycleCounting"]},
    {"level": "shelf", "applied": []},
    {"level": "storageLocation", "applied": []}
  ]
}
```

**2.2 查询配置继承层次**：
```bash
curl -X GET "http://localhost:5000/api/Configuration/hierarchy/5" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

---

## 业务场景测试

### 🏥 1. 医疗行业场景

**目标**：测试医疗用品存储的完整流程

**场景描述**：
存储疫苗，需要严格的温度控制和审计跟踪。

**1.1 创建疫苗专用区域**：
```bash
curl -X POST "http://localhost:5000/api/Zones" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "VACCINE",
    "name": "疫苗存储区",
    "warehouseId": 2,
    "zoneType": 2,
    "status": 1,
    "temperatureRange": {"min": 2, "max": 8},
    "securityLevel": 5,
    "configuration": {
      "industry": "medical",
      "regulatoryCompliance": ["FDA", "WHO", "NMPA"],
      "temperatureMonitoring": {
        "continuous": true,
        "alertThreshold": 0.1,
        "dataLogging": "every_minute"
      },
      "accessControl": {
        "biometricRequired": true,
        "dualApproval": true,
        "logAllAccess": true
      },
      "auditTrail": {
        "level": "complete",
        "retention": "10_years",
        "realTimeSync": true
      }
    }
  }'
```

**1.2 创建疫苗储位并添加医疗能力**：
```bash
# 创建储位
curl -X POST "http://localhost:5000/api/StorageLocations" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "VACCINE-001",
    "name": "疫苗储位001",
    "shelfId": 6,
    "esp32ControllerId": 1,
    "startLedPosition": 0,
    "endLedPosition": 5,
    "ledChannel": 1,
    "status": 0
  }'

# 添加生物样品存储能力
curl -X POST "http://localhost:5000/api/StorageLocations/10/capabilities" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "capabilityType": 5,
    "capabilityName": "生物样品存储",
    "parameters": {
      "temperatureRange": {"min": 2, "max": 8},
      "humidityRange": {"min": 40, "max": 60},
      "lightExposure": "minimal",
      "vibrationControl": true
    },
    "certifications": {
      "gmp": true,
      "iso15189": true,
      "who_prequalification": true
    },
    "validationRules": {
      "temperatureTolerance": 0.1,
      "humidityTolerance": 5,
      "maxExposureTime": 30
    }
  }'
```

**1.3 测试LED控制（疫苗取货指示）**：
```bash
# 点亮疫苗储位LED（蓝色，表示冷藏）
curl -X POST "http://localhost:5000/api/StorageLocations/10/test-light" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "color": "blue",
    "brightness": 200
  }'
```

### 🛒 2. 电商行业场景

**目标**：测试电商快消品存储和拣货流程

**场景描述**：
双11大促，需要快速拣货和批量处理。

**2.1 创建快消品区域**：
```bash
curl -X POST "http://localhost:5000/api/Zones" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "ECOM-FAST",
    "name": "快消品拣货区",
    "warehouseId": 2,
    "zoneType": 1,
    "status": 1,
    "configuration": {
      "industry": "ecommerce",
      "pickingOptimization": {
        "algorithm": "wave_picking",
        "batchSize": 50,
        "routeOptimization": true
      },
      "automation": {
        "autoReplenishment": true,
        "dynamicSlotting": true,
        "seasonalAdjustment": true
      },
      "performance": {
        "targetPickRate": 120,
        "accuracyTarget": 99.5,
        "cycleCountFrequency": "continuous"
      }
    }
  }'
```

**2.2 创建快拣储位**：
```bash
curl -X POST "http://localhost:5000/api/StorageLocations" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "FAST-PICK-001",
    "name": "快拣位001",
    "shelfId": 7,
    "esp32ControllerId": 1,
    "startLedPosition": 10,
    "endLedPosition": 20,
    "ledChannel": 0,
    "status": 0
  }'

# 添加电商分类
curl -X POST "http://localhost:5000/api/StorageLocations/11/classifications" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dimension": 1,
    "category": "Industry",
    "value": "Ecommerce",
    "businessRules": {
      "pickingStrategy": "batch",
      "replenishmentTrigger": "min_max",
      "velocityTracking": true
    }
  }'
```

**2.3 测试批量拣货LED指示**：
```bash
# 模拟波次拣货，点亮多个储位
for position in 11 12 13; do
  curl -X POST "http://localhost:5000/api/StorageLocations/$position/test-light" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "color": "green",
      "brightness": 255
    }'
  sleep 0.5
done
```

### 🏭 3. 制造业场景

**目标**：测试原材料和成品的分类存储

**场景描述**：
汽车制造厂，需要分别存储原材料、半成品和成品。

**3.1 创建原材料区域**：
```bash
curl -X POST "http://localhost:5000/api/Zones" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "RAW-MAT",
    "name": "原材料区",
    "warehouseId": 2,
    "zoneType": 1,
    "status": 1,
    "configuration": {
      "industry": "manufacturing",
      "materialFlow": {
        "fifoEnforcement": true,
        "lotTracking": true,
        "qualityGating": true
      },
      "planning": {
        "mrpIntegration": true,
        "leadTimeTracking": true,
        "supplierLinking": true
      }
    }
  }'
```

**3.2 创建重型货架储位**：
```bash
curl -X POST "http://localhost:5000/api/StorageLocations" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "HEAVY-001",
    "name": "重型储位001",
    "shelfId": 8,
    "esp32ControllerId": 1,
    "startLedPosition": 20,
    "endLedPosition": 30,
    "ledChannel": 2,
    "status": 0,
    "maxWeight": 1000.0
  }'

# 添加重型货物能力
curl -X POST "http://localhost:5000/api/StorageLocations/12/capabilities" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "capabilityType": 6,
    "capabilityName": "重型货物存储",
    "parameters": {
      "maxWeight": 1000,
      "structuralRating": "Class_A",
      "loadDistribution": "uniform"
    },
    "validationRules": {
      "weightCheck": "mandatory",
      "loadBalancing": true
    }
  }'
```

---

## 常见问题解答

### ❓ 1. 数据相关问题

**Q: 为什么要设计四层层级结构？**
A: 
- 🏭 **仓库层**：支持多仓库管理，每个仓库独立配置
- 🏢 **区域层**：支持功能分区（常温/冷藏/危险品等）
- 📚 **货架层**：对应物理货架，便于维护管理
- 📦 **储位层**：精确定位，连接LED控制

**Q: 为什么StorageLocation还保留Zone、ShelfCode字段？**
A: 为了向后兼容，确保现有系统和数据不受影响。新系统通过ShelfId关联层级，旧字段保留用于兼容性查询。

**Q: 能力系统和分类系统有什么区别？**
A:
- **能力系统**：描述储位"能做什么"（技术能力）
- **分类系统**：描述储位"适合什么"（业务分类）

### ❓ 2. 操作相关问题

**Q: 如何快速查找满足特定条件的储位？**
A: 使用组合查询：
```bash
# 查找冷藏区的食品级储位
curl -X GET "http://localhost:5000/api/StorageLocations/search" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -G \
  -d "zoneType=Refrigerated" \
  -d "capability=FoodGrade" \
  -d "status=Available"
```

**Q: 如何批量更新配置？**
A: 使用层级配置功能：
```bash
# 批量更新整个区域的配置
curl -X POST "http://localhost:5000/api/Configuration/apply-to-zone/3" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"newConfiguration": {...}}'
```

**Q: LED控制出现问题怎么办？**
A: 分步排查：
1. 检查ESP32控制器连接状态
2. 验证LED位置和通道配置
3. 测试单个储位LED控制
4. 检查硬件线路连接

### ❓ 3. 配置相关问题

**Q: 配置继承的优先级是什么？**
A: 优先级从高到低：
1. StorageLocation（储位级）
2. Shelf（货架级）
3. Zone（区域级）  
4. Warehouse（仓库级）

**Q: 如何实现行业特定配置？**
A: 通过JSON配置字段：
```json
{
  "industry": "medical",
  "compliance": ["FDA", "GMP"],
  "monitoring": {
    "temperature": true,
    "humidity": true,
    "access": "biometric"
  }
}
```

### ❓ 4. 性能相关问题

**Q: 大量储位查询会不会很慢？**
A: 系统已做优化：
- 数据库索引优化
- 分页查询支持
- 缓存机制
- 异步处理

**Q: 如何监控系统性能？**
A: 多层监控：
- API响应时间监控
- 数据库查询性能监控
- LED控制响应监控
- 业务操作审计

### ❓ 5. 扩展相关问题

**Q: 如何添加新的能力类型？**
A: 扩展CapabilityType枚举：
1. 在代码中添加新的枚举值
2. 创建数据库迁移
3. 更新API文档
4. 编写相应的业务逻辑

**Q: 如何支持新的行业需求？**
A: 通过配置和分类系统：
1. 定义新的行业配置模板
2. 创建行业特定的分类维度
3. 设置相应的业务规则
4. 实现行业特定的验证逻辑

---

## 总结

通过本测试文档，您应该能够：

✅ **理解系统架构**：掌握四层层级结构的设计理念和实现方式

✅ **掌握基本操作**：能够创建和管理仓库、区域、货架、储位

✅ **使用高级功能**：了解能力系统、分类系统和配置继承

✅ **解决实际问题**：应对不同行业的存储需求和业务场景

✅ **故障排查**：快速定位和解决常见问题

系统的设计理念是"**简单易用，功能强大**"：
- 对于日常操作，保持简单直观
- 对于复杂需求，提供灵活的扩展能力
- 对于多行业应用，支持个性化配置

希望这份文档能帮助您快速上手WMS-VGL的层级结构系统！

---

📞 **技术支持**：如有问题，请查看CLAUDE.md文档或联系开发团队。