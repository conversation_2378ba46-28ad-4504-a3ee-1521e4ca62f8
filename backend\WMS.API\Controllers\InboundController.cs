using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WMS.API.Constants;
using WMS.API.Models;
using WMS.API.Services;

namespace WMS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = PolicyConstants.InventoryManagementPolicy)]
    public class InboundController : ControllerBase
    {
        private readonly IInboundService _inboundService;
        private readonly ILogger<InboundController> _logger;

        public InboundController(IInboundService inboundService, ILogger<InboundController> logger)
        {
            _inboundService = inboundService;
            _logger = logger;
        }

        /// <summary>
        /// 获取入库订单列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<InboundOrder>>> GetInboundOrders(
            [FromQuery] InboundOrderStatus? status = null
        )
        {
            try
            {
                var orders = await _inboundService.GetInboundOrdersAsync(status);
                return Ok(orders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving inbound orders");
                return StatusCode(500, "获取入库订单失败");
            }
        }

        /// <summary>
        /// 根据ID获取入库订单详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<InboundOrder>> GetInboundOrder(int id)
        {
            try
            {
                var order = await _inboundService.GetInboundOrderByIdAsync(id);
                if (order == null)
                {
                    return NotFound($"入库订单 {id} 不存在");
                }

                return Ok(order);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving inbound order {OrderId}", id);
                return StatusCode(500, "获取入库订单详情失败");
            }
        }

        /// <summary>
        /// 根据订单号获取入库订单详情
        /// </summary>
        [HttpGet("by-number/{orderNumber}")]
        public async Task<ActionResult<InboundOrder>> GetInboundOrderByNumber(string orderNumber)
        {
            try
            {
                var order = await _inboundService.GetInboundOrderByNumberAsync(orderNumber);
                if (order == null)
                {
                    return NotFound($"入库订单 {orderNumber} 不存在");
                }

                return Ok(order);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving inbound order {OrderNumber}", orderNumber);
                return StatusCode(500, "获取入库订单详情失败");
            }
        }

        /// <summary>
        /// 创建入库订单
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<InboundOrder>> CreateInboundOrder(
            [FromBody] InboundOrderCreateRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var order = await _inboundService.CreateInboundOrderAsync(request, currentUser);

                return CreatedAtAction(nameof(GetInboundOrder), new { id = order.Id }, order);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inbound order");
                return StatusCode(500, "创建入库订单失败");
            }
        }

        /// <summary>
        /// 向入库订单添加项目
        /// </summary>
        [HttpPost("{id}/items")]
        public async Task<ActionResult<InboundOrderItem>> AddItemToOrder(
            int id,
            [FromBody] InboundOrderItemRequest request
        )
        {
            try
            {
                var item = await _inboundService.AddItemToOrderAsync(id, request);
                return Ok(item);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to inbound order {OrderId}", id);
                return StatusCode(500, "添加订单项目失败");
            }
        }

        /// <summary>
        /// 处理入库订单（批量处理）
        /// </summary>
        [HttpPost("{id}/process")]
        public async Task<ActionResult> ProcessInboundOrder(
            int id,
            [FromBody] InboundProcessRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _inboundService.ProcessInboundAsync(id, request, currentUser);

                if (!success)
                {
                    return BadRequest("处理入库订单失败");
                }

                return Ok(new { message = "入库订单处理成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing inbound order {OrderId}", id);
                return StatusCode(500, "处理入库订单失败");
            }
        }

        /// <summary>
        /// 使用条码处理入库项目
        /// </summary>
        [HttpPost("items/{itemId}/process-barcode")]
        public async Task<ActionResult> ProcessItemWithBarcode(
            int itemId,
            [FromBody] BarcodeInboundRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _inboundService.ProcessItemWithBarcodeAsync(
                    itemId,
                    request,
                    currentUser
                );

                if (!success)
                {
                    return BadRequest("条码入库处理失败");
                }

                return Ok(new { message = "条码入库处理成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing barcode inbound for item {ItemId}", itemId);
                return StatusCode(500, "条码入库处理失败");
            }
        }

        /// <summary>
        /// 更新入库订单状态
        /// </summary>
        [HttpPatch("{id}/status")]
        public async Task<ActionResult> UpdateOrderStatus(
            int id,
            [FromBody] UpdateOrderStatusRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _inboundService.UpdateInboundOrderStatusAsync(
                    id,
                    request.Status,
                    currentUser
                );

                if (!success)
                {
                    return NotFound($"入库订单 {id} 不存在");
                }

                return Ok(new { message = "订单状态更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating inbound order {OrderId} status", id);
                return StatusCode(500, "更新订单状态失败");
            }
        }

        /// <summary>
        /// 取消入库订单
        /// </summary>
        [HttpPost("{id}/cancel")]
        public async Task<ActionResult> CancelInboundOrder(
            int id,
            [FromBody] CancelOrderRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _inboundService.CancelInboundOrderAsync(
                    id,
                    request.Reason,
                    currentUser
                );

                if (!success)
                {
                    return NotFound($"入库订单 {id} 不存在");
                }

                return Ok(new { message = "入库订单已取消" });
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling inbound order {OrderId}", id);
                return StatusCode(500, "取消入库订单失败");
            }
        }

        /// <summary>
        /// 生成入库订单号
        /// </summary>
        [HttpGet("generate-order-number")]
        public async Task<ActionResult<string>> GenerateOrderNumber()
        {
            try
            {
                var orderNumber = await _inboundService.GenerateInboundOrderNumberAsync();
                return Ok(new { orderNumber });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating inbound order number");
                return StatusCode(500, "生成订单号失败");
            }
        }
    }

    // DTOs for controller
    public class UpdateOrderStatusRequest
    {
        public InboundOrderStatus Status { get; set; }
    }

    public class CancelOrderRequest
    {
        public string Reason { get; set; } = string.Empty;
    }
}
