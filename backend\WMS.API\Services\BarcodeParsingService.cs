using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.DTOs.Barcode;
using WMS.API.Models;

namespace WMS.API.Services
{
    /// <summary>
    /// 条码解析服务实现
    /// </summary>
    public class BarcodeParsingService : IBarcodeParsingService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<BarcodeParsingService> _logger;

        public BarcodeParsingService(
            ApplicationDbContext context,
            ILogger<BarcodeParsingService> logger
        )
        {
            _context = context;
            _logger = logger;
        }

        public async Task<BarcodeParsingResult> ParseBarcodeAsync(
            string barcode,
            BarcodeContext context
        )
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new BarcodeParsingResult
            {
                OriginalBarcode = barcode,
                ParsedAt = DateTime.UtcNow,
            };

            try
            {
                _logger.LogInformation(
                    "开始解析条码: {Barcode}, 操作类型: {OperationType}",
                    barcode,
                    context.OperationType
                );

                // 获取适用的解析规则
                var rules = await GetActiveRulesAsync(context.ClientId);

                // 按优先级排序
                rules = rules.OrderBy(r => r.Priority).ToList();

                // 尝试每个规则进行匹配
                foreach (var rule in rules)
                {
                    if (TryParseWithRule(barcode, rule, result))
                    {
                        result.MatchedRule = rule;
                        result.IsValid = true;
                        _logger.LogInformation("条码解析成功，匹配规则: {RuleName}", rule.RuleName);
                        break;
                    }
                }

                if (!result.IsValid)
                {
                    result.ValidationErrors.Add("未找到匹配的解析规则");
                    _logger.LogWarning("条码解析失败，未找到匹配规则: {Barcode}", barcode);
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ValidationErrors.Add($"解析过程中发生错误: {ex.Message}");
                _logger.LogError(ex, "条码解析过程中发生异常: {Barcode}", barcode);
            }
            finally
            {
                stopwatch.Stop();
                result.ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds;

                // 记录解析日志
                await LogParsingResultAsync(result, context);
            }

            return result;
        }

        public async Task<BarcodeParsingResult> ParseAndValidateAsync(
            string barcode,
            BarcodeContext context
        )
        {
            var result = await ParseBarcodeAsync(barcode, context);

            if (result.IsValid)
            {
                // 执行业务验证
                await ValidateBusinessRulesAsync(result, context);
            }

            return result;
        }

        public async Task<BatchBarcodeParsingResult> ParseBarcodesAsync(
            List<string> barcodes,
            BarcodeContext context,
            bool continueOnError = true
        )
        {
            var batchResult = new BatchBarcodeParsingResult
            {
                StartedAt = DateTime.UtcNow,
                TotalCount = barcodes.Count
            };

            var totalStopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation(
                    "开始批量解析条码: {Count}个条码, 操作类型: {OperationType}",
                    barcodes.Count,
                    context.OperationType
                );

                // 获取适用的解析规则（只查询一次，提高性能）
                var rules = await GetActiveRulesAsync(context.ClientId);
                rules = rules.OrderBy(r => r.Priority).ToList();

                // 并行处理条码解析（但限制并发数避免资源耗尽）
                var parallelOptions = new ParallelOptions
                {
                    MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 10)
                };

                var results = new ConcurrentBag<BarcodeParsingResult>();
                var exceptions = new ConcurrentBag<Exception>();

                await Parallel.ForEachAsync(barcodes, parallelOptions, async (barcode, cancellationToken) =>
                {
                    try
                    {
                        var result = await ParseSingleBarcodeWithRules(barcode, context, rules);
                        results.Add(result);
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                        
                        if (!continueOnError)
                        {
                            // 如果不继续处理错误，则创建一个失败结果
                            var failedResult = new BarcodeParsingResult
                            {
                                OriginalBarcode = barcode,
                                IsValid = false,
                                ValidationErrors = { $"解析过程中发生错误: {ex.Message}" },
                                ParsedAt = DateTime.UtcNow,
                                ProcessingTimeMs = 0
                            };
                            results.Add(failedResult);
                        }
                    }
                });

                // 按原始顺序排列结果
                batchResult.Results = barcodes.Select(barcode => 
                    results.FirstOrDefault(r => r.OriginalBarcode == barcode) ?? 
                    new BarcodeParsingResult 
                    { 
                        OriginalBarcode = barcode, 
                        IsValid = false, 
                        ValidationErrors = { "未知错误" },
                        ParsedAt = DateTime.UtcNow
                    }
                ).ToList();

                // 统计结果
                batchResult.SuccessCount = batchResult.Results.Count(r => r.IsValid);
                batchResult.FailureCount = batchResult.Results.Count(r => !r.IsValid);
                batchResult.TotalProcessingTimeMs = batchResult.Results.Sum(r => r.ProcessingTimeMs);

                _logger.LogInformation(
                    "批量解析完成: 共{Total}个条码，成功{Success}个，失败{Failure}个，成功率{Rate:F1}%",
                    batchResult.TotalCount,
                    batchResult.SuccessCount,
                    batchResult.FailureCount,
                    batchResult.SuccessRate
                );

                // 记录批量处理日志
                await LogBatchParsingResultAsync(batchResult, context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量解析条码过程中发生异常");
                
                // 创建错误结果
                batchResult.Results = barcodes.Select(barcode => new BarcodeParsingResult
                {
                    OriginalBarcode = barcode,
                    IsValid = false,
                    ValidationErrors = { $"批量处理异常: {ex.Message}" },
                    ParsedAt = DateTime.UtcNow
                }).ToList();
                
                batchResult.FailureCount = barcodes.Count;
            }
            finally
            {
                totalStopwatch.Stop();
                batchResult.CompletedAt = DateTime.UtcNow;
                
                // 如果没有单个处理时间，使用总时间
                if (batchResult.TotalProcessingTimeMs == 0)
                {
                    batchResult.TotalProcessingTimeMs = (int)totalStopwatch.ElapsedMilliseconds;
                }
            }

            return batchResult;
        }

        public async Task<List<BarcodeParsingRule>> GetActiveRulesAsync(string? clientId = null)
        {
            var query = _context
                .BarcodeParsingRules.Include(r => r.Category)
                .Where(r => r.IsActive);

            if (!string.IsNullOrEmpty(clientId))
            {
                query = query.Where(r => r.ClientId == null || r.ClientId == clientId);
            }

            return await query.OrderBy(r => r.Priority).ToListAsync();
        }

        public async Task<List<BarcodeParsingRule>> GetRulesByCategoryAsync(
            string categoryCode,
            string? clientId = null
        )
        {
            var query = _context
                .BarcodeParsingRules.Include(r => r.Category)
                .Where(r => r.IsActive && r.Category!.CategoryCode == categoryCode);

            if (!string.IsNullOrEmpty(clientId))
            {
                query = query.Where(r => r.ClientId == null || r.ClientId == clientId);
            }

            return await query.OrderBy(r => r.Priority).ToListAsync();
        }

        public async Task<bool> ValidateUniqueCodeAsync(string uniqueCode, int materialId)
        {
            var existingCode = await _context.UniqueCodeRegistries.FirstOrDefaultAsync(u =>
                u.UniqueCode == uniqueCode
            );

            if (existingCode == null)
            {
                return true; // 唯一码不存在，可以使用
            }

            // 检查是否为同一物料
            if (existingCode.MaterialId != materialId)
            {
                return false; // 唯一码已被其他物料使用
            }

            // 检查状态
            return existingCode.Status == UniqueCodeStatus.REGISTERED
                || existingCode.Status == UniqueCodeStatus.OUT_STOCK;
        }

        public async Task<bool> RegisterUniqueCodeAsync(
            string uniqueCode,
            int materialId,
            int? storageLocationId = null,
            string? userId = null
        )
        {
            try
            {
                var existingCode = await _context.UniqueCodeRegistries.FirstOrDefaultAsync(u =>
                    u.UniqueCode == uniqueCode
                );

                if (existingCode != null)
                {
                    if (existingCode.MaterialId != materialId)
                    {
                        return false; // 唯一码已被其他物料使用
                    }

                    // 更新现有记录
                    existingCode.Status = UniqueCodeStatus.IN_STOCK;
                    existingCode.StorageLocationId = storageLocationId;
                    existingCode.LastOperationDate = DateTime.UtcNow;
                    existingCode.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    // 创建新记录
                    var newCode = new UniqueCodeRegistry
                    {
                        UniqueCode = uniqueCode,
                        MaterialId = materialId,
                        StorageLocationId = storageLocationId,
                        Status = UniqueCodeStatus.IN_STOCK,
                        CreatedBy = userId,
                    };
                    _context.UniqueCodeRegistries.Add(newCode);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "注册唯一码失败: {UniqueCode}, MaterialId: {MaterialId}",
                    uniqueCode,
                    materialId
                );
                return false;
            }
        }

        private bool TryParseWithRule(
            string barcode,
            BarcodeParsingRule rule,
            BarcodeParsingResult result
        )
        {
            try
            {
                var regex = new Regex(
                    rule.RegexPattern,
                    RegexOptions.Compiled | RegexOptions.IgnoreCase
                );
                var match = regex.Match(barcode);

                if (!match.Success)
                {
                    return false;
                }

                // 解析字段映射配置
                var fieldMappings = JsonSerializer.Deserialize<Dictionary<string, FieldMapping>>(
                    rule.FieldMappings
                );
                if (fieldMappings == null)
                {
                    return false;
                }

                // 设置分类
                result.Category = rule.Category;

                // 提取字段值
                foreach (var mapping in fieldMappings)
                {
                    var fieldName = mapping.Key;
                    var fieldConfig = mapping.Value;

                    string? fieldValue = null;

                    if (fieldConfig.Group == 0)
                    {
                        // Group 0 表示整个匹配
                        fieldValue = match.Value;
                    }
                    else if (fieldConfig.Group <= match.Groups.Count - 1)
                    {
                        fieldValue = match.Groups[fieldConfig.Group].Value;
                    }

                    // 应用转换
                    if (
                        !string.IsNullOrEmpty(fieldValue)
                        && !string.IsNullOrEmpty(fieldConfig.Transform)
                    )
                    {
                        fieldValue = ApplyTransform(fieldValue, fieldConfig.Transform);
                    }

                    // 应用默认值
                    if (string.IsNullOrEmpty(fieldValue) && fieldConfig.DefaultValue != null)
                    {
                        fieldValue = fieldConfig.DefaultValue.ToString();
                    }

                    // 验证必需字段
                    if (fieldConfig.Required && string.IsNullOrEmpty(fieldValue))
                    {
                        result.ValidationErrors.Add($"必需字段 {fieldName} 未找到或为空");
                        return false;
                    }

                    // 设置字段值
                    SetFieldValue(result, fieldName, fieldValue, fieldConfig.Type);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用规则 {RuleName} 解析条码时发生错误", rule.RuleName);
                return false;
            }
        }

        private string ApplyTransform(string value, string transform)
        {
            if (transform.StartsWith("removePrefix:"))
            {
                var prefix = transform.Substring("removePrefix:".Length);
                if (value.StartsWith(prefix))
                {
                    return value.Substring(prefix.Length);
                }
            }
            else if (transform == "toUpper")
            {
                return value.ToUpper();
            }
            else if (transform == "toLower")
            {
                return value.ToLower();
            }

            return value;
        }

        private void SetFieldValue(
            BarcodeParsingResult result,
            string fieldName,
            string? fieldValue,
            string? fieldType
        )
        {
            if (string.IsNullOrEmpty(fieldValue))
                return;

            switch (fieldName.ToLower())
            {
                case "materialsku":
                    result.MaterialSku = fieldValue;
                    break;
                case "quantity":
                    if (decimal.TryParse(fieldValue, out var quantity))
                        result.Quantity = quantity;
                    break;
                case "batchnumber":
                    result.BatchNumber = fieldValue;
                    break;
                case "lpncode":
                    result.LpnCode = fieldValue;
                    break;
                case "uniquecode":
                    result.UniqueCode = fieldValue;
                    break;
                case "workordernumber":
                    result.WorkOrderNumber = fieldValue;
                    break;
                case "storagelocationcode":
                    result.StorageLocationCode = fieldValue;
                    break;
                default:
                    // 扩展字段
                    object convertedValue = fieldValue;
                    if (fieldType == "decimal" && decimal.TryParse(fieldValue, out var decimalVal))
                        convertedValue = decimalVal;
                    else if (fieldType == "int" && int.TryParse(fieldValue, out var intVal))
                        convertedValue = intVal;
                    else if (fieldType == "bool" && bool.TryParse(fieldValue, out var boolVal))
                        convertedValue = boolVal;

                    result.ExtendedFields[fieldName] = convertedValue;
                    break;
            }
        }

        private async Task ValidateBusinessRulesAsync(
            BarcodeParsingResult result,
            BarcodeContext context
        )
        {
            try
            {
                // 物料存在性验证
                if (!string.IsNullOrEmpty(result.MaterialSku))
                {
                    var materialExists = await _context.Materials.AnyAsync(m =>
                        m.Sku == result.MaterialSku
                    );

                    if (!materialExists)
                    {
                        result.ValidationErrors.Add($"物料 {result.MaterialSku} 不存在于系统中");
                    }
                }

                // 唯一码验证
                if (
                    !string.IsNullOrEmpty(result.UniqueCode)
                    && !string.IsNullOrEmpty(result.MaterialSku)
                )
                {
                    var material = await _context.Materials.FirstOrDefaultAsync(m =>
                        m.Sku == result.MaterialSku
                    );

                    if (material != null)
                    {
                        var isUniqueCodeValid = await ValidateUniqueCodeAsync(
                            result.UniqueCode,
                            material.Id
                        );
                        if (!isUniqueCodeValid)
                        {
                            result.ValidationErrors.Add(
                                $"唯一码 {result.UniqueCode} 已存在或不匹配"
                            );
                        }
                    }
                }

                // 储位有效性验证
                if (!string.IsNullOrEmpty(result.StorageLocationCode))
                {
                    var locationExists = await _context.StorageLocations.AnyAsync(sl =>
                        sl.Code == result.StorageLocationCode
                    );

                    if (!locationExists)
                    {
                        result.ValidationErrors.Add($"储位 {result.StorageLocationCode} 不存在");
                    }
                }

                // 如果有验证错误，设置为无效
                if (result.ValidationErrors.Any())
                {
                    result.IsValid = false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "业务规则验证时发生错误");
                result.ValidationErrors.Add("业务规则验证时发生内部错误");
                result.IsValid = false;
            }
        }

        private async Task LogParsingResultAsync(
            BarcodeParsingResult result,
            BarcodeContext context
        )
        {
            try
            {
                // 创建简化的序列化对象，避免循环引用
                var simplifiedResult = new
                {
                    result.OriginalBarcode,
                    result.IsValid,
                    result.MaterialSku,
                    result.Quantity,
                    result.BatchNumber,
                    result.LpnCode,
                    result.UniqueCode,
                    result.WorkOrderNumber,
                    result.StorageLocationCode,
                    result.ParsedAt,
                    result.ProcessingTimeMs,
                    result.ValidationErrors,
                    result.ExtendedFields,
                    MatchedRule = result.MatchedRule != null ? new
                    {
                        result.MatchedRule.Id,
                        result.MatchedRule.RuleName,
                        result.MatchedRule.Priority,
                        CategoryCode = result.MatchedRule.Category?.CategoryCode,
                        CategoryName = result.MatchedRule.Category?.CategoryName
                    } : null,
                    Category = result.Category != null ? new
                    {
                        result.Category.Id,
                        result.Category.CategoryCode,
                        result.Category.CategoryName
                    } : null
                };

                var log = new BarcodeParsingLog
                {
                    OriginalBarcode = result.OriginalBarcode,
                    ParsedResult = result.IsValid ? JsonSerializer.Serialize(simplifiedResult) : null,
                    MatchedRuleId = result.MatchedRule?.Id,
                    IsSuccess = result.IsValid,
                    ErrorMessage = result.ValidationErrors.Any()
                        ? string.Join("; ", result.ValidationErrors)
                        : null,
                    ParsedAt = result.ParsedAt,
                    UserId = context.UserId,
                    ClientId = context.ClientId,
                    OperationType = context.OperationType,
                    StorageLocationCode = context.StorageLocationCode,
                    ProcessingTimeMs = result.ProcessingTimeMs,
                };

                _context.BarcodeParsingLogs.Add(log);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录条码解析日志时发生错误");
                // 不抛出异常，避免影响主要功能
            }
        }

        /// <summary>
        /// 使用预加载的规则解析单个条码（用于批量处理）
        /// </summary>
        private async Task<BarcodeParsingResult> ParseSingleBarcodeWithRules(
            string barcode,
            BarcodeContext context,
            List<BarcodeParsingRule> rules
        )
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new BarcodeParsingResult
            {
                OriginalBarcode = barcode,
                ParsedAt = DateTime.UtcNow,
            };

            try
            {
                // 尝试每个规则进行匹配
                foreach (var rule in rules)
                {
                    if (TryParseWithRule(barcode, rule, result))
                    {
                        result.MatchedRule = rule;
                        result.IsValid = true;
                        break;
                    }
                }

                if (!result.IsValid)
                {
                    result.ValidationErrors.Add("未找到匹配的解析规则");
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ValidationErrors.Add($"解析过程中发生错误: {ex.Message}");
                _logger.LogError(ex, "条码解析过程中发生异常: {Barcode}", barcode);
            }
            finally
            {
                stopwatch.Stop();
                result.ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds;

                // 记录解析日志
                await LogParsingResultAsync(result, context);
            }

            return result;
        }

        /// <summary>
        /// 记录批量处理结果日志
        /// </summary>
        private async Task LogBatchParsingResultAsync(
            BatchBarcodeParsingResult batchResult,
            BarcodeContext context
        )
        {
            try
            {
                // 为每个失败的条码记录日志
                var failedResults = batchResult.Results.Where(r => !r.IsValid).ToList();
                
                foreach (var failedResult in failedResults)
                {
                    await LogParsingResultAsync(failedResult, context);
                }

                _logger.LogInformation(
                    "批量处理结果: {Summary}, 总耗时: {TotalTime}ms, 平均耗时: {AvgTime:F2}ms",
                    batchResult.Summary,
                    batchResult.TotalProcessingTimeMs,
                    batchResult.AverageProcessingTimeMs
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录批量处理日志时发生错误");
            }
        }
    }

    /// <summary>
    /// 字段映射配置
    /// </summary>
    public class FieldMapping
    {
        /// <summary>
        /// 正则表达式组号
        /// </summary>
        public int Group { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool Required { get; set; }

        /// <summary>
        /// 字段类型
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }

        /// <summary>
        /// 转换规则
        /// </summary>
        public string? Transform { get; set; }

        /// <summary>
        /// 字段描述
        /// </summary>
        public string? Description { get; set; }
    }
}
