using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 储位能力类型枚举
    /// </summary>
    public enum CapabilityType
    {
        /// <summary>
        /// 温度控制能力
        /// </summary>
        Temperature = 0,

        /// <summary>
        /// 安全控制能力
        /// </summary>
        Security = 1,

        /// <summary>
        /// 访问控制能力
        /// </summary>
        Access = 2,

        /// <summary>
        /// 承重能力
        /// </summary>
        Weight = 3,

        /// <summary>
        /// 湿度控制能力
        /// </summary>
        Humidity = 4,

        /// <summary>
        /// 危险品处理能力
        /// </summary>
        Hazmat = 5,

        /// <summary>
        /// 监控能力
        /// </summary>
        Monitoring = 6,

        /// <summary>
        /// 防火能力
        /// </summary>
        FireSafety = 7,

        /// <summary>
        /// 防盗能力
        /// </summary>
        AntiTheft = 8,

        /// <summary>
        /// 特殊处理能力（如医药品、食品等）
        /// </summary>
        SpecialHandling = 9
    }

    /// <summary>
    /// 能力等级枚举
    /// </summary>
    public enum CapabilityLevel
    {
        /// <summary>
        /// 无此能力
        /// </summary>
        None = 0,

        /// <summary>
        /// 基础级别
        /// </summary>
        Basic = 1,

        /// <summary>
        /// 标准级别
        /// </summary>
        Standard = 2,

        /// <summary>
        /// 高级别
        /// </summary>
        High = 3,

        /// <summary>
        /// 最高级别
        /// </summary>
        Maximum = 4
    }

    /// <summary>
    /// 储位能力实体类
    /// 用于描述储位具备的各种能力和特性
    /// </summary>
    public class LocationCapability
    {
        /// <summary>
        /// 能力记录唯一标识ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 关联的储位ID
        /// </summary>
        public int StorageLocationId { get; set; }

        /// <summary>
        /// 关联的储位实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual StorageLocation? StorageLocation { get; set; }

        /// <summary>
        /// 能力类型
        /// </summary>
        public CapabilityType CapabilityType { get; set; }

        /// <summary>
        /// 能力等级
        /// </summary>
        public CapabilityLevel CapabilityLevel { get; set; }

        /// <summary>
        /// 能力名称（可读性更好的名称）
        /// 例如："冷藏能力", "生物识别访问", "重载承重"
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CapabilityName { get; set; } = string.Empty;

        /// <summary>
        /// 能力描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 能力参数（JSON格式）
        /// 存储具体的能力参数，例如：
        /// Temperature: {"minTemp": 2, "maxTemp": 8, "unit": "°C", "precision": 0.1}
        /// Security: {"authMethods": ["biometric", "card"], "encryption": "AES256"}
        /// Weight: {"maxWeight": 500, "unit": "kg", "precision": 0.1}
        /// </summary>
        public string? Parameters { get; set; }

        /// <summary>
        /// 能力配置（JSON格式）
        /// 存储该能力的具体配置信息
        /// </summary>
        public string? Configuration { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 优先级（数字越小优先级越高）
        /// 用于能力冲突时的优先级判断
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 验证规则（JSON格式）
        /// 定义该能力的验证规则
        /// </summary>
        public string? ValidationRules { get; set; }

        /// <summary>
        /// 能力生效开始时间
        /// </summary>
        public DateTime? EffectiveFrom { get; set; }

        /// <summary>
        /// 能力生效结束时间
        /// </summary>
        public DateTime? EffectiveTo { get; set; }

        /// <summary>
        /// 认证信息（JSON格式）
        /// 存储相关的认证或证书信息
        /// </summary>
        public string? Certifications { get; set; }

        /// <summary>
        /// 最后检验日期
        /// </summary>
        public DateTime? LastVerificationDate { get; set; }

        /// <summary>
        /// 下次检验日期
        /// </summary>
        public DateTime? NextVerificationDate { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }
    }
}