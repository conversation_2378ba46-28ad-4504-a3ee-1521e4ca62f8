using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Services
{
    public interface IInventoryTransactionService
    {
        Task<InventoryTransaction> CreateTransactionAsync(
            TransactionType type,
            int storageLocationId,
            int materialId,
            decimal quantity,
            string operatedBy,
            int? inboundOrderId = null,
            int? outboundOrderId = null,
            int? targetStorageLocationId = null,
            string? batchNumber = null,
            string? lpnCode = null,
            string? uniqueCode = null,
            DateTime? expiryDate = null,
            string? reason = null,
            string? remarks = null
        );

        Task<bool> ExecuteTransactionAsync(int transactionId, string? approvedBy = null);
        Task<bool> CancelTransactionAsync(int transactionId, string reason);
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByOrderAsync(
            int orderId,
            TransactionType orderType
        );
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByStorageLocationAsync(
            int storageLocationId
        );
        Task<InventoryTransaction?> GetTransactionByNumberAsync(string transactionNumber);
        Task<string> GenerateTransactionNumberAsync(TransactionType type);
    }

    public class InventoryTransactionService : IInventoryTransactionService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<InventoryTransactionService> _logger;

        public InventoryTransactionService(
            ApplicationDbContext context,
            ILogger<InventoryTransactionService> logger
        )
        {
            _context = context;
            _logger = logger;
        }

        public async Task<InventoryTransaction> CreateTransactionAsync(
            TransactionType type,
            int storageLocationId,
            int materialId,
            decimal quantity,
            string operatedBy,
            int? inboundOrderId = null,
            int? outboundOrderId = null,
            int? targetStorageLocationId = null,
            string? batchNumber = null,
            string? lpnCode = null,
            string? uniqueCode = null,
            DateTime? expiryDate = null,
            string? reason = null,
            string? remarks = null
        )
        {
            try
            {
                var transactionNumber = await GenerateTransactionNumberAsync(type);

                // 获取当前库存数量
                var currentInventory = await GetCurrentInventoryAsync(
                    storageLocationId,
                    materialId,
                    batchNumber,
                    lpnCode
                );
                var beforeQuantity = currentInventory?.Quantity ?? 0;

                // 计算事务后数量
                decimal afterQuantity = type switch
                {
                    TransactionType.Inbound => beforeQuantity + quantity,
                    TransactionType.Outbound => beforeQuantity - quantity,
                    TransactionType.Adjustment => quantity, // 调整为指定数量
                    TransactionType.Transfer => beforeQuantity - quantity, // 源储位减少
                    TransactionType.Freeze => beforeQuantity, // 冻结不改变数量
                    TransactionType.Unfreeze => beforeQuantity, // 解冻不改变数量
                    _ => beforeQuantity,
                };

                var transaction = new InventoryTransaction
                {
                    TransactionNumber = transactionNumber,
                    Type = type,
                    Status = TransactionStatus.Pending,
                    StorageLocationId = storageLocationId,
                    MaterialId = materialId,
                    Quantity = quantity,
                    BeforeQuantity = beforeQuantity,
                    AfterQuantity = afterQuantity,
                    BatchNumber = batchNumber,
                    LpnCode = lpnCode,
                    UniqueCode = uniqueCode,
                    ExpiryDate = expiryDate,
                    InboundOrderId = inboundOrderId,
                    OutboundOrderId = outboundOrderId,
                    TargetStorageLocationId = targetStorageLocationId,
                    Reason = reason,
                    Remarks = remarks,
                    OperatedBy = operatedBy,
                    OperatedAt = DateTime.UtcNow,
                };

                _context.InventoryTransactions.Add(transaction);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Created inventory transaction {TransactionNumber} for {Type} operation",
                    transactionNumber,
                    type
                );

                return transaction;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inventory transaction");
                throw;
            }
        }

        public async Task<bool> ExecuteTransactionAsync(
            int transactionId,
            string? approvedBy = null
        )
        {
            // 检查是否已经在事务中
            var shouldCreateTransaction = _context.Database.CurrentTransaction == null;
            Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction? dbTransaction = null;
            
            if (shouldCreateTransaction)
            {
                dbTransaction = await _context.Database.BeginTransactionAsync();
            }
            
            try
            {
                var transaction = await _context
                    .InventoryTransactions.Include(t => t.StorageLocation)
                    .Include(t => t.Material)
                    .Include(t => t.TargetStorageLocation)
                    .FirstOrDefaultAsync(t => t.Id == transactionId);

                if (transaction == null)
                {
                    _logger.LogWarning("Transaction {TransactionId} not found", transactionId);
                    return false;
                }

                if (transaction.Status != TransactionStatus.Pending)
                {
                    _logger.LogWarning(
                        "Transaction {TransactionNumber} is not in pending status",
                        transaction.TransactionNumber
                    );
                    return false;
                }

                // 验证库存充足性（出库和移库操作）
                if (
                    transaction.Type == TransactionType.Outbound
                    || transaction.Type == TransactionType.Transfer
                )
                {
                    var currentInventory = await GetCurrentInventoryAsync(
                        transaction.StorageLocationId,
                        transaction.MaterialId,
                        transaction.BatchNumber,
                        transaction.LpnCode
                    );

                    if (
                        currentInventory == null
                        || currentInventory.Quantity < transaction.Quantity
                    )
                    {
                        transaction.Status = TransactionStatus.Failed;
                        transaction.Remarks = "库存不足";
                        await _context.SaveChangesAsync();
                        
                        if (shouldCreateTransaction && dbTransaction != null)
                        {
                            await dbTransaction.CommitAsync();
                        }
                        return false;
                    }
                }

                // 执行库存变更
                var success = await ApplyInventoryChangesAsync(transaction);
                if (!success)
                {
                    transaction.Status = TransactionStatus.Failed;
                    await _context.SaveChangesAsync();
                    
                    if (shouldCreateTransaction && dbTransaction != null)
                    {
                        await dbTransaction.RollbackAsync();
                    }
                    return false;
                }

                // 更新事务状态
                transaction.Status = TransactionStatus.Completed;
                transaction.ApprovedBy = approvedBy;
                transaction.ApprovedAt = DateTime.UtcNow;
                transaction.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                
                if (shouldCreateTransaction && dbTransaction != null)
                {
                    await dbTransaction.CommitAsync();
                }

                _logger.LogInformation(
                    "Successfully executed transaction {TransactionNumber}",
                    transaction.TransactionNumber
                );

                return true;
            }
            catch (Exception ex)
            {
                if (shouldCreateTransaction && dbTransaction != null)
                {
                    await dbTransaction.RollbackAsync();
                }
                _logger.LogError(ex, "Error executing transaction {TransactionId}", transactionId);
                throw;
            }
            finally
            {
                if (shouldCreateTransaction && dbTransaction != null)
                {
                    await dbTransaction.DisposeAsync();
                }
            }
        }

        public async Task<bool> CancelTransactionAsync(int transactionId, string reason)
        {
            try
            {
                var transaction = await _context.InventoryTransactions.FirstOrDefaultAsync(t =>
                    t.Id == transactionId
                );

                if (transaction == null)
                {
                    return false;
                }

                if (transaction.Status != TransactionStatus.Pending)
                {
                    return false;
                }

                transaction.Status = TransactionStatus.Cancelled;
                transaction.Reason = reason;
                transaction.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Cancelled transaction {TransactionNumber}, reason: {Reason}",
                    transaction.TransactionNumber,
                    reason
                );

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling transaction {TransactionId}", transactionId);
                throw;
            }
        }

        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByOrderAsync(
            int orderId,
            TransactionType orderType
        )
        {
            return orderType switch
            {
                TransactionType.Inbound => await _context
                    .InventoryTransactions.Where(t => t.InboundOrderId == orderId)
                    .Include(t => t.StorageLocation)
                    .Include(t => t.Material)
                    .OrderByDescending(t => t.CreatedAt)
                    .ToListAsync(),

                TransactionType.Outbound => await _context
                    .InventoryTransactions.Where(t => t.OutboundOrderId == orderId)
                    .Include(t => t.StorageLocation)
                    .Include(t => t.Material)
                    .OrderByDescending(t => t.CreatedAt)
                    .ToListAsync(),

                _ => await _context
                    .InventoryTransactions.Where(t =>
                        t.InboundOrderId == orderId || t.OutboundOrderId == orderId
                    )
                    .Include(t => t.StorageLocation)
                    .Include(t => t.Material)
                    .OrderByDescending(t => t.CreatedAt)
                    .ToListAsync(),
            };
        }

        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByStorageLocationAsync(
            int storageLocationId
        )
        {
            return await _context
                .InventoryTransactions.Where(t =>
                    t.StorageLocationId == storageLocationId
                    || t.TargetStorageLocationId == storageLocationId
                )
                .Include(t => t.StorageLocation)
                .Include(t => t.TargetStorageLocation)
                .Include(t => t.Material)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        public async Task<InventoryTransaction?> GetTransactionByNumberAsync(
            string transactionNumber
        )
        {
            return await _context
                .InventoryTransactions.Include(t => t.StorageLocation)
                .Include(t => t.TargetStorageLocation)
                .Include(t => t.Material)
                .Include(t => t.InboundOrder)
                .Include(t => t.OutboundOrder)
                .FirstOrDefaultAsync(t => t.TransactionNumber == transactionNumber);
        }

        public async Task<string> GenerateTransactionNumberAsync(TransactionType type)
        {
            var prefix = type switch
            {
                TransactionType.Inbound => "IN",
                TransactionType.Outbound => "OUT",
                TransactionType.Adjustment => "ADJ",
                TransactionType.Transfer => "TRF",
                TransactionType.Freeze => "FRZ",
                TransactionType.Unfreeze => "UFZ",
                _ => "TXN",
            };

            var date = DateTime.UtcNow.ToString("yyyyMMdd");
            var sequence = await GetNextSequenceAsync(prefix, date);

            return $"{prefix}{date}{sequence:D4}";
        }

        private async Task<Inventory?> GetCurrentInventoryAsync(
            int storageLocationId,
            int materialId,
            string? batchNumber,
            string? lpnCode
        )
        {
            return await _context
                .Inventories.Where(i =>
                    i.StorageLocationId == storageLocationId
                    && i.MaterialId == materialId
                    && (batchNumber == null || i.BatchNumber == batchNumber)
                    && (lpnCode == null || i.LpnCode == lpnCode)
                )
                .FirstOrDefaultAsync();
        }

        private async Task<bool> ApplyInventoryChangesAsync(InventoryTransaction transaction)
        {
            try
            {
                switch (transaction.Type)
                {
                    case TransactionType.Inbound:
                        return await ApplyInboundAsync(transaction);

                    case TransactionType.Outbound:
                        return await ApplyOutboundAsync(transaction);

                    case TransactionType.Adjustment:
                        return await ApplyAdjustmentAsync(transaction);

                    case TransactionType.Transfer:
                        return await ApplyTransferAsync(transaction);

                    case TransactionType.Freeze:
                    case TransactionType.Unfreeze:
                        return await ApplyFreezeUnfreezeAsync(transaction);

                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error applying inventory changes for transaction {TransactionNumber}",
                    transaction.TransactionNumber
                );
                return false;
            }
        }

        private async Task<bool> ApplyInboundAsync(InventoryTransaction transaction)
        {
            var inventory = await GetCurrentInventoryAsync(
                transaction.StorageLocationId,
                transaction.MaterialId,
                transaction.BatchNumber,
                transaction.LpnCode
            );

            if (inventory == null)
            {
                // 创建新库存记录
                inventory = new Inventory
                {
                    StorageLocationId = transaction.StorageLocationId,
                    MaterialId = transaction.MaterialId,
                    Quantity = transaction.Quantity,
                    BatchNumber = transaction.BatchNumber,
                    LpnCode = transaction.LpnCode,
                    ExpiryDate = transaction.ExpiryDate,
                    Status = InventoryStatus.Available,
                };
                _context.Inventories.Add(inventory);
            }
            else
            {
                // 更新现有库存
                inventory.Quantity += transaction.Quantity;
                inventory.UpdatedAt = DateTime.UtcNow;
            }

            return true;
        }

        private async Task<bool> ApplyOutboundAsync(InventoryTransaction transaction)
        {
            var inventory = await GetCurrentInventoryAsync(
                transaction.StorageLocationId,
                transaction.MaterialId,
                transaction.BatchNumber,
                transaction.LpnCode
            );

            if (inventory == null || inventory.Quantity < transaction.Quantity)
            {
                return false;
            }

            inventory.Quantity -= transaction.Quantity;
            inventory.UpdatedAt = DateTime.UtcNow;

            // 如果库存为0，删除记录
            if (inventory.Quantity == 0)
            {
                _context.Inventories.Remove(inventory);
            }

            return true;
        }

        private async Task<bool> ApplyAdjustmentAsync(InventoryTransaction transaction)
        {
            var inventory = await GetCurrentInventoryAsync(
                transaction.StorageLocationId,
                transaction.MaterialId,
                transaction.BatchNumber,
                transaction.LpnCode
            );

            if (inventory == null)
            {
                // 创建新库存记录
                inventory = new Inventory
                {
                    StorageLocationId = transaction.StorageLocationId,
                    MaterialId = transaction.MaterialId,
                    Quantity = transaction.Quantity,
                    BatchNumber = transaction.BatchNumber,
                    LpnCode = transaction.LpnCode,
                    ExpiryDate = transaction.ExpiryDate,
                    Status = InventoryStatus.Available,
                };
                _context.Inventories.Add(inventory);
            }
            else
            {
                // 调整为指定数量
                inventory.Quantity = transaction.Quantity;
                inventory.UpdatedAt = DateTime.UtcNow;

                // 如果调整后数量为0，删除记录
                if (inventory.Quantity == 0)
                {
                    _context.Inventories.Remove(inventory);
                }
            }

            return true;
        }

        private async Task<bool> ApplyTransferAsync(InventoryTransaction transaction)
        {
            if (transaction.TargetStorageLocationId == null)
            {
                return false;
            }

            // 从源储位扣减
            var sourceInventory = await GetCurrentInventoryAsync(
                transaction.StorageLocationId,
                transaction.MaterialId,
                transaction.BatchNumber,
                transaction.LpnCode
            );

            if (sourceInventory == null || sourceInventory.Quantity < transaction.Quantity)
            {
                return false;
            }

            sourceInventory.Quantity -= transaction.Quantity;
            sourceInventory.UpdatedAt = DateTime.UtcNow;

            if (sourceInventory.Quantity == 0)
            {
                _context.Inventories.Remove(sourceInventory);
            }

            // 向目标储位增加
            var targetInventory = await GetCurrentInventoryAsync(
                transaction.TargetStorageLocationId.Value,
                transaction.MaterialId,
                transaction.BatchNumber,
                transaction.LpnCode
            );

            if (targetInventory == null)
            {
                // 创建新库存记录
                targetInventory = new Inventory
                {
                    StorageLocationId = transaction.TargetStorageLocationId.Value,
                    MaterialId = transaction.MaterialId,
                    Quantity = transaction.Quantity,
                    BatchNumber = transaction.BatchNumber,
                    LpnCode = transaction.LpnCode,
                    ExpiryDate = transaction.ExpiryDate,
                    Status = InventoryStatus.Available,
                };
                _context.Inventories.Add(targetInventory);
            }
            else
            {
                targetInventory.Quantity += transaction.Quantity;
                targetInventory.UpdatedAt = DateTime.UtcNow;
            }

            return true;
        }

        private async Task<bool> ApplyFreezeUnfreezeAsync(InventoryTransaction transaction)
        {
            var inventory = await GetCurrentInventoryAsync(
                transaction.StorageLocationId,
                transaction.MaterialId,
                transaction.BatchNumber,
                transaction.LpnCode
            );

            if (inventory == null)
            {
                return false;
            }

            inventory.Status =
                transaction.Type == TransactionType.Freeze
                    ? InventoryStatus.Frozen
                    : InventoryStatus.Available;
            inventory.UpdatedAt = DateTime.UtcNow;

            return true;
        }

        private async Task<int> GetNextSequenceAsync(string prefix, string date)
        {
            var pattern = $"{prefix}{date}%";
            var lastTransaction = await _context
                .InventoryTransactions.Where(t => EF.Functions.Like(t.TransactionNumber, pattern))
                .OrderByDescending(t => t.TransactionNumber)
                .FirstOrDefaultAsync();

            if (lastTransaction == null)
            {
                return 1;
            }

            var lastSequence = lastTransaction.TransactionNumber.Substring(
                prefix.Length + date.Length
            );
            if (int.TryParse(lastSequence, out var sequence))
            {
                return sequence + 1;
            }

            return 1;
        }
    }
}
