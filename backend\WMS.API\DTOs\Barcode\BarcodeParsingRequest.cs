using System.ComponentModel.DataAnnotations;

namespace WMS.API.DTOs.Barcode
{
    /// <summary>
    /// 条码解析请求
    /// </summary>
    public class BarcodeParsingRequest
    {
        /// <summary>
        /// 要解析的条码
        /// </summary>
        [Required]
        public string Barcode { get; set; } = string.Empty;

        /// <summary>
        /// 解析上下文
        /// </summary>
        [Required]
        public BarcodeContext Context { get; set; } = new();
    }

    /// <summary>
    /// 批量条码解析请求
    /// </summary>
    public class BatchBarcodeParsingRequest
    {
        /// <summary>
        /// 要解析的条码列表
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "至少需要提供一个条码")]
        public List<string> Barcodes { get; set; } = new();

        /// <summary>
        /// 解析上下文
        /// </summary>
        [Required]
        public BarcodeContext Context { get; set; } = new();

        /// <summary>
        /// 是否继续处理错误（如果某个条码解析失败，是否继续处理其他条码）
        /// </summary>
        public bool ContinueOnError { get; set; } = true;
    }

    /// <summary>
    /// 条码解析上下文
    /// </summary>
    public class BarcodeContext
    {
        /// <summary>
        /// 操作类型（INBOUND, OUTBOUND, INVENTORY, MOVEMENT）
        /// </summary>
        [Required]
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// 储位代码（可选）
        /// </summary>
        public string? StorageLocationCode { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 客户ID（可选，如果不提供则从用户信息获取）
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 额外参数
        /// </summary>
        public Dictionary<string, object> AdditionalParams { get; set; } = new();
    }
}