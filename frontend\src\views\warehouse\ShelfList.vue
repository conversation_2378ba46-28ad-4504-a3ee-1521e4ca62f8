<template>
  <div class="shelf-list">
    <div class="page-header">
      <h1 class="page-title">货架管理</h1>
      <p class="page-desc">管理区域内的物理货架</p>
    </div>

    <a-card class="content-card">
      <template #title>
        <span class="card-title">货架列表</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary">
            <PlusOutlined />
            新增货架
          </a-button>
          <a-button>
            <ExportOutlined />
            导出数据
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="shelves"
        :loading="loading"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 'active' ? 'green' : 'red'">
              {{ record.status === 'active' ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewStorageLocations(record)">
                查看储位
              </a-button>
              <a-button type="link" size="small">编辑</a-button>
              <a-button type="link" size="small" danger>删除</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  ExportOutlined,
} from '@ant-design/icons-vue'

const router = useRouter()
const loading = ref(false)

const columns = [
  { title: '货架编码', dataIndex: 'code', key: 'code', width: 120 },
  { title: '货架名称', dataIndex: 'name', key: 'name', width: 150 },
  { title: '货架规格', dataIndex: 'specification', key: 'specification', width: 150 },
  { title: '承重(kg)', dataIndex: 'maxWeight', key: 'maxWeight', width: 100 },
  { title: '储位数量', dataIndex: 'storageCount', key: 'storageCount', width: 100 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 80 },
  { title: '操作', key: 'actions', width: 200, fixed: 'right' },
]

const shelves = ref([
  {
    id: '1',
    code: 'A01',
    name: '货架A01',
    specification: '2000x1000x3000mm',
    maxWeight: 1000,
    storageCount: 80,
    status: 'active',
  },
  {
    id: '2',
    code: 'A02',
    name: '货架A02',
    specification: '2000x1000x3000mm',
    maxWeight: 1000,
    storageCount: 80,
    status: 'active',
  },
  {
    id: '3',
    code: 'B01',
    name: '货架B01',
    specification: '1500x800x2500mm',
    maxWeight: 800,
    storageCount: 60,
    status: 'active',
  },
])

const viewStorageLocations = (record: any) => {
  router.push(`/warehouse/storage-locations?shelfId=${record.id}`)
}
</script>

<style scoped lang="less">
.shelf-list {
  .page-header {
    background: white;
    padding: 20px 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);

    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    .page-desc {
      color: #8c8c8c;
      margin: 0;
    }
  }
}

.content-card {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }
}
</style>