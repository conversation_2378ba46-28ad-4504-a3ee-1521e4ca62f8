using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ZonesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ZonesController> _logger;

        public ZonesController(ApplicationDbContext context, ILogger<ZonesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有区域列表
        /// </summary>
        /// <returns>区域列表</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Zone>>> GetZones()
        {
            try
            {
                var zones = await _context
                    .Zones.Include(z => z.Warehouse)
                    .Include(z => z.Shelves)
                    .OrderBy(z => z.WarehouseId)
                    .ThenBy(z => z.Code)
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} zones", zones.Count);
                return Ok(zones);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving zones");
                return StatusCode(500, "Internal server error while retrieving zones");
            }
        }

        /// <summary>
        /// 获取指定区域详情
        /// </summary>
        /// <param name="id">区域ID</param>
        /// <returns>区域详情</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<Zone>> GetZone(int id)
        {
            try
            {
                var zone = await _context
                    .Zones.Include(z => z.Warehouse)
                    .Include(z => z.Shelves)
                    .ThenInclude(s => s.StorageLocations)
                    .FirstOrDefaultAsync(z => z.Id == id);

                if (zone == null)
                {
                    _logger.LogWarning("Zone with ID {Id} not found", id);
                    return NotFound($"Zone with ID {id} not found");
                }

                _logger.LogInformation("Retrieved zone {Id}: {Name}", id, zone.Name);
                return Ok(zone);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving zone {Id}", id);
                return StatusCode(500, "Internal server error while retrieving zone");
            }
        }

        /// <summary>
        /// 根据仓库ID获取区域列表
        /// </summary>
        /// <param name="warehouseId">仓库ID</param>
        /// <returns>指定仓库的区域列表</returns>
        [HttpGet("by-warehouse/{warehouseId}")]
        public async Task<ActionResult<IEnumerable<Zone>>> GetZonesByWarehouse(int warehouseId)
        {
            try
            {
                var zones = await _context
                    .Zones.Include(z => z.Shelves)
                    .Where(z => z.WarehouseId == warehouseId)
                    .OrderBy(z => z.Code)
                    .ToListAsync();

                _logger.LogInformation(
                    "Retrieved {Count} zones for warehouse {WarehouseId}",
                    zones.Count,
                    warehouseId
                );
                return Ok(zones);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving zones for warehouse {WarehouseId}",
                    warehouseId
                );
                return StatusCode(500, "Internal server error while retrieving zones");
            }
        }

        /// <summary>
        /// 根据区域类型查询区域
        /// </summary>
        /// <param name="zoneType">区域类型</param>
        /// <param name="warehouseId">仓库ID（可选）</param>
        /// <returns>指定类型的区域列表</returns>
        [HttpGet("by-type")]
        public async Task<ActionResult<IEnumerable<Zone>>> GetZonesByType(
            ZoneType zoneType,
            int? warehouseId = null
        )
        {
            try
            {
                var query = _context
                    .Zones.Include(z => z.Warehouse)
                    .Include(z => z.Shelves)
                    .Where(z => z.ZoneType == zoneType);

                if (warehouseId.HasValue)
                {
                    query = query.Where(z => z.WarehouseId == warehouseId.Value);
                }

                var zones = await query
                    .OrderBy(z => z.WarehouseId)
                    .ThenBy(z => z.Code)
                    .ToListAsync();

                _logger.LogInformation(
                    "Retrieved {Count} zones of type {ZoneType}",
                    zones.Count,
                    zoneType
                );
                return Ok(zones);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving zones by type {ZoneType}", zoneType);
                return StatusCode(500, "Internal server error while retrieving zones");
            }
        }

        /// <summary>
        /// 创建新区域
        /// </summary>
        /// <param name="zone">区域信息</param>
        /// <returns>创建的区域</returns>
        [HttpPost]
        [Authorize(Policy = "ESP32ManagementPolicy")]
        public async Task<ActionResult<Zone>> CreateZone(Zone zone)
        {
            try
            {
                // 检查仓库是否存在
                var warehouse = await _context.Warehouses.FindAsync(zone.WarehouseId);
                if (warehouse == null)
                {
                    return BadRequest($"Warehouse with ID {zone.WarehouseId} not found");
                }

                // 检查同一仓库内编码是否已存在
                var existingZone = await _context.Zones.FirstOrDefaultAsync(z =>
                    z.Code == zone.Code && z.WarehouseId == zone.WarehouseId
                );

                if (existingZone != null)
                {
                    return BadRequest(
                        $"Zone with code '{zone.Code}' already exists in warehouse {zone.WarehouseId}"
                    );
                }

                // 设置创建时间
                zone.CreatedAt = DateTime.UtcNow;
                zone.UpdatedAt = DateTime.UtcNow;

                _context.Zones.Add(zone);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Created new zone {Id}: {Name} with code {Code} in warehouse {WarehouseId}",
                    zone.Id,
                    zone.Name,
                    zone.Code,
                    zone.WarehouseId
                );

                return CreatedAtAction(nameof(GetZone), new { id = zone.Id }, zone);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating zone");
                return StatusCode(500, "Internal server error while creating zone");
            }
        }

        /// <summary>
        /// 更新区域信息
        /// </summary>
        /// <param name="id">区域ID</param>
        /// <param name="zone">更新的区域信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        [Authorize(Policy = "ESP32ManagementPolicy")]
        public async Task<IActionResult> UpdateZone(int id, Zone zone)
        {
            if (id != zone.Id)
            {
                return BadRequest("Zone ID mismatch");
            }

            try
            {
                var existingZone = await _context.Zones.FindAsync(id);
                if (existingZone == null)
                {
                    return NotFound($"Zone with ID {id} not found");
                }

                // 检查仓库是否存在
                var warehouse = await _context.Warehouses.FindAsync(zone.WarehouseId);
                if (warehouse == null)
                {
                    return BadRequest($"Warehouse with ID {zone.WarehouseId} not found");
                }

                // 检查编码冲突（排除自己）
                var duplicateCode = await _context.Zones.AnyAsync(z =>
                    z.Code == zone.Code && z.WarehouseId == zone.WarehouseId && z.Id != id
                );

                if (duplicateCode)
                {
                    return BadRequest(
                        $"Zone with code '{zone.Code}' already exists in warehouse {zone.WarehouseId}"
                    );
                }

                // 更新字段
                existingZone.Code = zone.Code;
                existingZone.Name = zone.Name;
                existingZone.Description = zone.Description;
                existingZone.WarehouseId = zone.WarehouseId;
                existingZone.ZoneType = zone.ZoneType;
                existingZone.Status = zone.Status;
                existingZone.Area = zone.Area;
                existingZone.Volume = zone.Volume;
                existingZone.MaxWeight = zone.MaxWeight;
                existingZone.TemperatureRange = zone.TemperatureRange;
                existingZone.HumidityRange = zone.HumidityRange;
                existingZone.SecurityLevel = zone.SecurityLevel;
                existingZone.AccessControl = zone.AccessControl;
                existingZone.SpecialRequirements = zone.SpecialRequirements;
                existingZone.Configuration = zone.Configuration;
                existingZone.ResponsiblePerson = zone.ResponsiblePerson;
                existingZone.ContactInfo = zone.ContactInfo;
                existingZone.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated zone {Id}: {Name}", id, zone.Name);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating zone {Id}", id);
                return StatusCode(500, "Internal server error while updating zone");
            }
        }

        /// <summary>
        /// 删除区域
        /// </summary>
        /// <param name="id">区域ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        [Authorize(Policy = "SystemConfigPolicy")]
        public async Task<IActionResult> DeleteZone(int id)
        {
            try
            {
                var zone = await _context
                    .Zones.Include(z => z.Shelves)
                    .FirstOrDefaultAsync(z => z.Id == id);

                if (zone == null)
                {
                    return NotFound($"Zone with ID {id} not found");
                }

                // 检查是否有关联的货架
                if (zone.Shelves.Any())
                {
                    return BadRequest(
                        "Cannot delete zone with existing shelves. Please delete shelves first."
                    );
                }

                _context.Zones.Remove(zone);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted zone {Id}: {Name}", id, zone.Name);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting zone {Id}", id);
                return StatusCode(500, "Internal server error while deleting zone");
            }
        }

        /// <summary>
        /// 获取区域统计信息
        /// </summary>
        /// <param name="id">区域ID</param>
        /// <returns>统计信息</returns>
        [HttpGet("{id}/statistics")]
        public async Task<ActionResult<object>> GetZoneStatistics(int id)
        {
            try
            {
                var zone = await _context
                    .Zones.Include(z => z.Warehouse)
                    .Include(z => z.Shelves)
                    .ThenInclude(s => s.StorageLocations)
                    .FirstOrDefaultAsync(z => z.Id == id);

                if (zone == null)
                {
                    return NotFound($"Zone with ID {id} not found");
                }

                var statistics = new
                {
                    ZoneId = id,
                    ZoneName = zone.Name,
                    ZoneCode = zone.Code,
                    ZoneType = zone.ZoneType.ToString(),
                    WarehouseName = zone.Warehouse.Name,
                    TotalShelves = zone.Shelves.Count,
                    TotalStorageLocations = zone.Shelves.Sum(s => s.StorageLocations.Count),
                    AvailableStorageLocations = zone.Shelves.Sum(s =>
                        s.StorageLocations.Count(sl => sl.Status == StorageLocationStatus.Available)
                    ),
                    UtilizationRate = zone.Shelves.Any()
                        ? zone.Shelves.SelectMany(s => s.StorageLocations)
                            .Count(sl => sl.Status == StorageLocationStatus.Occupied)
                            / (double)zone.Shelves.SelectMany(s => s.StorageLocations).Count()
                        : 0,
                    ShelfBreakdown = zone
                        .Shelves.Select(s => new
                        {
                            ShelfId = s.Id,
                            ShelfName = s.Name,
                            ShelfCode = s.Code,
                            ShelfType = s.ShelfType.ToString(),
                            StorageLocations = s.StorageLocations.Count,
                            AvailableLocations = s.StorageLocations.Count(sl =>
                                sl.Status == StorageLocationStatus.Available
                            ),
                        })
                        .ToList(),
                };

                _logger.LogInformation("Retrieved statistics for zone {Id}", id);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving zone statistics {Id}", id);
                return StatusCode(500, "Internal server error while retrieving zone statistics");
            }
        }

        /// <summary>
        /// 获取区域配置
        /// </summary>
        /// <param name="id">区域ID</param>
        /// <returns>区域配置</returns>
        [HttpGet("{id}/configuration")]
        public async Task<ActionResult<object>> GetZoneConfiguration(int id)
        {
            try
            {
                var zone = await _context.Zones.FindAsync(id);
                if (zone == null)
                {
                    return NotFound($"Zone with ID {id} not found");
                }

                return Ok(
                    new
                    {
                        ZoneId = id,
                        Configuration = zone.Configuration,
                        TemperatureRange = zone.TemperatureRange,
                        HumidityRange = zone.HumidityRange,
                        AccessControl = zone.AccessControl,
                        SpecialRequirements = zone.SpecialRequirements,
                    }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving zone configuration {Id}", id);
                return StatusCode(500, "Internal server error while retrieving zone configuration");
            }
        }

        /// <summary>
        /// 更新区域配置
        /// </summary>
        /// <param name="id">区域ID</param>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}/configuration")]
        [Authorize(Policy = "ESP32ManagementPolicy")]
        public async Task<IActionResult> UpdateZoneConfiguration(
            int id,
            [FromBody] string configuration
        )
        {
            try
            {
                var zone = await _context.Zones.FindAsync(id);
                if (zone == null)
                {
                    return NotFound($"Zone with ID {id} not found");
                }

                zone.Configuration = configuration;
                zone.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated configuration for zone {Id}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating zone configuration {Id}", id);
                return StatusCode(500, "Internal server error while updating zone configuration");
            }
        }
    }
}
