<template>
  <div class="zone-list">
    <div class="page-header">
      <h1 class="page-title">区域管理</h1>
      <p class="page-desc">管理仓库区域，配置环境控制参数</p>
    </div>

    <!-- 区域列表 -->
    <a-card class="content-card">
      <template #title>
        <span class="card-title">区域列表</span>
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <PlusOutlined />
            新增区域
          </a-button>
          <a-button>
            <ExportOutlined />
            导出数据
          </a-button>
        </a-space>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="区域编码">
              <a-input v-model:value="searchForm.code" placeholder="输入区域编码" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="区域名称">
              <a-input v-model:value="searchForm.name" placeholder="输入区域名称" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="区域类型">
              <a-select v-model:value="searchForm.type" placeholder="选择区域类型">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="ambient">常温</a-select-option>
                <a-select-option value="refrigerated">冷藏</a-select-option>
                <a-select-option value="frozen">冷冻</a-select-option>
                <a-select-option value="hazmat">危险品</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label=" " style="visibility: hidden;">操作</a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <SearchOutlined />
                搜索
              </a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="zones"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeLabel(record.type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'temperature'">
            {{ record.minTemp }}°C ~ {{ record.maxTemp }}°C
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 'active' ? 'green' : 'red'">
              {{ record.status === 'active' ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewShelves(record)">
                查看货架
              </a-button>
              <a-button type="link" size="small" @click="editZone(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" danger @click="deleteZone(record)">
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑区域模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑区域' : '新增区域'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="区域编码" name="code">
              <a-input v-model:value="form.code" placeholder="请输入区域编码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="区域名称" name="name">
              <a-input v-model:value="form.name" placeholder="请输入区域名称" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="区域类型" name="type">
              <a-select v-model:value="form.type" placeholder="请选择区域类型">
                <a-select-option value="ambient">常温</a-select-option>
                <a-select-option value="refrigerated">冷藏</a-select-option>
                <a-select-option value="frozen">冷冻</a-select-option>
                <a-select-option value="hazmat">危险品</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="form.status" placeholder="请选择状态">
                <a-select-option value="active">启用</a-select-option>
                <a-select-option value="inactive">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="最低温度(°C)" name="minTemp">
              <a-input-number v-model:value="form.minTemp" placeholder="最低温度" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最高温度(°C)" name="maxTemp">
              <a-input-number v-model:value="form.maxTemp" placeholder="最高温度" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="最低湿度(%)" name="minHumidity">
              <a-input-number v-model:value="form.minHumidity" placeholder="最低湿度" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最高湿度(%)" name="maxHumidity">
              <a-input-number v-model:value="form.maxHumidity" placeholder="最高湿度" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="form.description" placeholder="请输入描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ExportOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  code: '',
  name: '',
  type: '',
})

// 表单数据
const form = reactive({
  id: '',
  code: '',
  name: '',
  type: '',
  status: 'active',
  minTemp: null,
  maxTemp: null,
  minHumidity: null,
  maxHumidity: null,
  description: '',
})

// 表单验证规则
const rules = {
  code: [{ required: true, message: '请输入区域编码' }],
  name: [{ required: true, message: '请输入区域名称' }],
  type: [{ required: true, message: '请选择区域类型' }],
  status: [{ required: true, message: '请选择状态' }],
}

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`,
})

// 表格列配置
const columns = [
  {
    title: '区域编码',
    dataIndex: 'code',
    key: 'code',
    width: 120,
  },
  {
    title: '区域名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
  },
  {
    title: '区域类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '温度范围',
    key: 'temperature',
    width: 150,
  },
  {
    title: '湿度范围',
    dataIndex: 'humidity',
    key: 'humidity',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
  },
]

// 区域数据
const zones = ref([
  {
    id: '1',
    code: 'A001',
    name: 'A区域',
    type: 'ambient',
    minTemp: 15,
    maxTemp: 25,
    minHumidity: 40,
    maxHumidity: 60,
    humidity: '40% ~ 60%',
    status: 'active',
    description: '常温存储区域',
  },
  {
    id: '2',
    code: 'B001',
    name: 'B区域',
    type: 'refrigerated',
    minTemp: 2,
    maxTemp: 8,
    minHumidity: 80,
    maxHumidity: 90,
    humidity: '80% ~ 90%',
    status: 'active',
    description: '冷藏存储区域',
  },
  {
    id: '3',
    code: 'C001',
    name: 'C区域',
    type: 'frozen',
    minTemp: -20,
    maxTemp: -15,
    minHumidity: 70,
    maxHumidity: 80,
    humidity: '70% ~ 80%',
    status: 'active',
    description: '冷冻存储区域',
  },
])

// 工具方法
const getTypeColor = (type: string) => {
  const colors = {
    ambient: 'blue',
    refrigerated: 'cyan',
    frozen: 'purple',
    hazmat: 'red',
  }
  return colors[type as keyof typeof colors] || 'default'
}

const getTypeLabel = (type: string) => {
  const labels = {
    ambient: '常温',
    refrigerated: '冷藏',
    frozen: '冷冻',
    hazmat: '危险品',
  }
  return labels[type as keyof typeof labels] || type
}

// 事件处理方法
const showAddModal = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

const handleSearch = () => {
  message.info('搜索功能待实现')
}

const handleReset = () => {
  Object.assign(searchForm, {
    code: '',
    name: '',
    type: '',
  })
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const viewShelves = (record: any) => {
  router.push(`/warehouse/shelves?zoneId=${record.id}`)
}

const editZone = (record: any) => {
  isEdit.value = true
  Object.assign(form, record)
  modalVisible.value = true
}

const deleteZone = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除区域 "${record.name}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      message.success('删除成功')
    },
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    message.success(isEdit.value ? '更新成功' : '创建成功')
    modalVisible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    id: '',
    code: '',
    name: '',
    type: '',
    status: 'active',
    minTemp: null,
    maxTemp: null,
    minHumidity: null,
    maxHumidity: null,
    description: '',
  })
  formRef.value?.resetFields()
}

// 生命周期
onMounted(() => {
  pagination.total = zones.value.length
})
</script>

<style scoped lang="less">
.zone-list {
  .page-header {
    background: white;
    padding: 20px 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);

    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    .page-desc {
      color: #8c8c8c;
      margin: 0;
    }
  }
}

.content-card {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }
}

.search-form {
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;

  .ant-form-item {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .search-form {
    .ant-col {
      margin-bottom: 16px;
    }
  }
}
</style>