{"permissions": {"allow": ["Bash(dotnet new:*)", "Bash(powershell.exe:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker volume rm:*)", "Bash(dotnet build)", "Bash(export:*)", "Bash(dotnet --version)", "Bash(ls:*)", "Bash(/mnt/c/Program Files/dotnet/dotnet.exe build)", "Bash(/mnt/c/Program\\ Files/dotnet/dotnet.exe run)", "Bash(/mnt/c/Program\\ Files/dotnet/dotnet.exe ef migrations add RemoveIPAddressUniqueConstraint)", "Bash(/mnt/c/Program\\ Files/dotnet/dotnet.exe ef database update)", "<PERSON><PERSON>(pkill:*)", "Bash(/mnt/c/Program\\ Files/dotnet/dotnet.exe ef migrations add FixTimestampTypes)", "<PERSON><PERSON>(taskkill:*)", "Bash(kill:*)", "Bash(dotnet restore:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./prepare-offline.sh:*)", "Bash(bash:*)", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(./test-deployment.sh:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker:*)", "Bash(find:*)", "Bash(dotnet --info)", "<PERSON><PERSON>(cat:*)", "Bash(git add:*)", "Bash(rg:*)", "Bash(/mnt/c/Windows/System32/cmd.exe /c \"dotnet --version\")", "Bash(/mnt/c/Windows/System32/cmd.exe /c \"docker-compose up -d wms-postgres\")", "Bash(ip route:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(grep:*)", "Bash(git config:*)", "Bash(# 创建一个完整的测试脚本\ncat > test_apis.sh << ''EOF''\n#!/bin/bash\n\n# 获取token\necho \"\"=== 获取认证Token ===\"\"\nTOKEN_RESPONSE=$(curl -s -X POST \"\"http://************:5000/api/Auth/login\"\" \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -d ''{\n    \"\"email\"\": \"\"<EMAIL>\"\",\n    \"\"password\"\": \"\"Admin@123456\"\",\n    \"\"rememberMe\"\": false\n  }'')\n\nTOKEN=$(echo \"\"$TOKEN_RESPONSE\"\" | grep -o ''\"\"accessToken\"\":\"\"[^\"\"]*\"\"'' | sed ''s/\"\"accessToken\"\":\"\"\\(.*\\)\"\"/\\1/'')\necho \"\"Token获取成功\"\"\n\n# 测试1: 为储位添加能力\necho -e \"\"\\n=== 测试1: 为储位添加能力 ===\"\"\nRESPONSE=$(curl -s -X POST \"\"http://************:5000/api/StorageLocations/1/capabilities\"\" \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"Authorization: Bearer $TOKEN\"\" \\\n  -w \"\"HTTP_STATUS:%{http_code}\"\" \\\n  -d ''{\n    \"\"capabilityType\"\": 1,\n    \"\"capabilityLevel\"\": 3,\n    \"\"capabilityName\"\": \"\"温度控制\"\",\n    \"\"description\"\": \"\"精确温度控制，适合生鲜食品存储\"\",\n    \"\"parameters\"\": \"\"{\\\"\"targetTemperature\\\"\":4,\\\"\"tolerance\\\"\":1,\\\"\"unit\\\"\":\\\"\"celsius\\\"\",\\\"\"monitoringInterval\\\"\":300}\"\",\n    \"\"isEnabled\"\": true,\n    \"\"priority\"\": 1,\n    \"\"validationRules\"\": \"\"{\\\"\"temperatureRange\\\"\":{\\\"\"min\\\"\":2,\\\"\"max\\\"\":8},\\\"\"alertThreshold\\\"\":0.5}\"\",\n    \"\"effectiveFrom\"\": \"\"2025-07-04T00:00:00Z\"\",\n    \"\"certifications\"\": \"\"{\\\"\"haccp\\\"\":true,\\\"\"iso22000\\\"\":true}\"\"\n  }'')\n\nHTTP_STATUS=$(echo \"\"$RESPONSE\"\" | grep -o \"\"HTTP_STATUS:[0-9]*\"\" | cut -d: -f2)\nRESPONSE_BODY=$(echo \"\"$RESPONSE\"\" | sed ''s/HTTP_STATUS:[0-9]*$//'')\n\necho \"\"HTTP状态码: $HTTP_STATUS\"\"\necho \"\"响应内容: $RESPONSE_BODY\"\"\n\n# 测试2: 为储位添加分类\necho -e \"\"\\n=== 测试2: 为储位添加分类 ===\"\"\nRESPONSE=$(curl -s -X POST \"\"http://************:5000/api/StorageLocations/1/classifications\"\" \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"Authorization: Bearer $TOKEN\"\" \\\n  -w \"\"HTTP_STATUS:%{http_code}\"\" \\\n  -d ''{\n    \"\"dimension\"\": 1,\n    \"\"category\"\": \"\"Industry\"\",\n    \"\"value\"\": \"\"Food\"\",\n    \"\"displayName\"\": \"\"食品行业\"\",\n    \"\"description\"\": \"\"适用于食品存储的储位\"\",\n    \"\"tags\"\": \"\"[\\\"\"food-grade\\\"\",\\\"\"haccp-compliant\\\"\"]\"\",\n    \"\"properties\"\": \"\"{\\\"\"temperatureControlled\\\"\":true,\\\"\"humidityControlled\\\"\":false,\\\"\"sanitationRequired\\\"\":true}\"\",\n    \"\"priority\"\": 1,\n    \"\"isEnabled\"\": true,\n    \"\"businessRules\"\": \"\"{\\\"\"autoAssignment\\\"\":true,\\\"\"materialTypes\\\"\":[\\\"\"fresh\\\"\",\\\"\"frozen\\\"\",\\\"\"dry_goods\\\"\"],\\\"\"restrictions\\\"\":[\\\"\"no_chemicals\\\"\",\\\"\"no_non_food\\\"\"]}\"\"\n  }'')\n\nHTTP_STATUS=$(echo \"\"$RESPONSE\"\" | grep -o \"\"HTTP_STATUS:[0-9]*\"\" | cut -d: -f2)\nRESPONSE_BODY=$(echo \"\"$RESPONSE\"\" | sed ''s/HTTP_STATUS:[0-9]*$//'')\n\necho \"\"HTTP状态码: $HTTP_STATUS\"\"\necho \"\"响应内容: $RESPONSE_BODY\"\"\n\n# 测试3: 货架安全检查 \necho -e \"\"\\n=== 测试3: 货架安全检查 ===\"\"\nRESPONSE=$(curl -s -X POST \"\"http://************:5000/api/Shelves/1/inspection\"\" \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"Authorization: Bearer $TOKEN\"\" \\\n  -w \"\"HTTP_STATUS:%{http_code}\"\" \\\n  -d ''{\n    \"\"inspectionType\"\": \"\"safety\"\",\n    \"\"result\"\": \"\"passed\"\",\n    \"\"inspectorName\"\": \"\"李工程师\"\",\n    \"\"nextInspectionDate\"\": \"\"2025-10-04T10:00:00Z\"\"\n  }'')\n\nHTTP_STATUS=$(echo \"\"$RESPONSE\"\" | grep -o \"\"HTTP_STATUS:[0-9]*\"\" | cut -d: -f2)\nRESPONSE_BODY=$(echo \"\"$RESPONSE\"\" | sed ''s/HTTP_STATUS:[0-9]*$//'')\n\necho \"\"HTTP状态码: $HTTP_STATUS\"\"\necho \"\"响应内容: $RESPONSE_BODY\"\"\n\n# 测试4: 设置仓库级配置\necho -e \"\"\\n=== 测试4: 设置仓库级配置 ===\"\"\nRESPONSE=$(curl -s -X PUT \"\"http://************:5000/api/Configuration/warehouse/1\"\" \\\n  -H \"\"Content-Type: application/json\"\" \\\n  -H \"\"Authorization: Bearer $TOKEN\"\" \\\n  -w \"\"HTTP_STATUS:%{http_code}\"\" \\\n  -d ''{\n    \"\"globalSettings\"\": {\n      \"\"operatingHours\"\": \"\"06:00-22:00\"\",\n      \"\"defaultLanguage\"\": \"\"zh-CN\"\"\n    },\n    \"\"securitySettings\"\": {\n      \"\"accessCardRequired\"\": true\n    }\n  }'')\n\nHTTP_STATUS=$(echo \"\"$RESPONSE\"\" | grep -o \"\"HTTP_STATUS:[0-9]*\"\" | cut -d: -f2)\nRESPONSE_BODY=$(echo \"\"$RESPONSE\"\" | sed ''s/HTTP_STATUS:[0-9]*$//'')\n\necho \"\"HTTP状态码: $HTTP_STATUS\"\"\necho \"\"响应内容: $RESPONSE_BODY\"\"\nEOF\n\nchmod +x test_apis.sh\n./test_apis.sh)", "Bash(./test_apis.sh:*)", "Bash(./debug_shelf_inspection.sh:*)", "Bash(git commit:*)", "<PERSON><PERSON>(mv:*)"], "deny": []}}