# LED灯带控制器 API 接口文档

## 基本信息

- **设备名称**: QR-Shelf Server
- **通信协议**: HTTP
- **默认端口**: 80
- **基础URL**: `http://{设备IP}`

## API 接口列表

### 1. 根路径

#### `GET /`

**描述**: 获取设备基本信息

**请求参数**: 无

**成功响应**:
```
状态码: 404
Content-Type: text/plain
响应体: "Hello, This is QR-Shelf Server, please contact with admin for more information."
```

---

### 2. WiFi配置管理

#### `POST /updateWifiConfig`

**描述**: 更新WiFi配置信息

**请求参数**:
```json
Content-Type: application/json

{
  "ssid": "WiFi网络名称",
  "password": "WiFi密码", 
  "enable": 1
}
```

**参数说明**:
- `ssid` (string, 必填): WiFi网络名称，最大长度32字符
- `password` (string, 必填): WiFi密码，最大长度64字符
- `enable` (integer, 可选): WiFi自动重连开关，1=启用自动重连，0=仅开机时连接，默认为0

**成功响应**:
```
状态码: 200
Content-Type: text/json
响应体: "Body received:\n{请求体}\nVersion:{版本信息}"
```

**失败响应**:
```
状态码: 200
Content-Type: text/json
响应体: "Body not received"
```

#### `POST /resetWifiConfig`

**描述**: 重置WiFi配置到默认状态

**请求参数**: 
```json
Content-Type: application/json
{}
```

**成功响应**:
```
状态码: 200
Content-Type: text/json
响应体: "Reset wifiConfig successful"
```

**失败响应**:
```
状态码: 200
Content-Type: text/json
响应体: "Body not received"
```

---

### 3. LED灯带控制

#### `GET /controlLEDs`

**描述**: 控制LED灯带的精确显示

**请求参数** (URL查询参数):
- `data` (string, 必填): 控制数据，格式为逗号分隔的数值列表

**数据格式说明**:
每个数值为32位整数，包含以下信息：
- 高4位 (28-31): LED通道索引 (0-3)
- 12位 (16-27): 起始位置索引 (0-4095)
- 3位 (13-15): 颜色值 (0-7，二进制RGB)
- 5位 (8-12): 亮度级别 (0-31)
- 8位 (0-7): LED数量 (0-255)

**颜色值编码**:
- 0: 黑色 (000)
- 1: 红色 (001)
- 2: 绿色 (010)
- 3: 黄色 (011)
- 4: 蓝色 (100)
- 5: 洋红 (101)
- 6: 青色 (110)
- 7: 白色 (111)

**成功响应**:
```
状态码: 200
Content-Type: text/plain
响应体: "OK"
```

**示例**:
```
GET /controlLEDs?data=268435712,536870912
```

#### `POST /controlLEDsV2`

**描述**: 使用JSON格式控制LED灯带 (推荐使用，参数更直观)

**请求参数**:
```json
Content-Type: application/json

{
  "leds": [
    {
      "channel": 0,
      "start": 0,
      "count": 10,
      "brightness": 255,
      "color": "FF0000"
    },
    {
      "channel": 1,
      "start": 50,
      "count": 20,
      "brightness": 128,
      "color": "00FF00"
    }
  ]
}
```

**参数说明**:
- `leds` (array, 必填): LED控制配置列表，最多支持80个配置项
  - `channel` (integer, 必填): LED通道 (0-3)
  - `start` (integer, 必填): 起始位置 (0-4095)
  - `count` (integer, 必填): LED数量 (1-255)
  - `brightness` (integer, 必填): 亮度 (0-255)
  - `color` (string, 必填): 十六进制颜色值，格式为"RRGGBB"

**颜色格式示例**:
- "FF0000": 红色
- "00FF00": 绿色
- "0000FF": 蓝色
- "FFFFFF": 白色
- "FFFF00": 黄色
- "FF00FF": 洋红
- "00FFFF": 青色
- "000000": 黑色

**成功响应**:
```json
状态码: 200
Content-Type: application/json

{
  "status": "success",
  "message": "LED control completed",
  "processed_count": 2,
  "total_received": 2
}
```

**失败响应**:
```json
状态码: 400
Content-Type: application/json

{
  "status": "error",
  "message": "Body not received"
}

或

{
  "status": "error",
  "message": "Invalid JSON"
}

或

{
  "status": "error",
  "message": "Missing 'leds' array parameter"
}

或

{
  "status": "error",
  "message": "Too many LED configurations. Maximum 80 items per request."
}
```

**示例**:
```bash
# 设置多个储位的LED
curl -X POST http://*************/controlLEDsV2 \
  -H "Content-Type: application/json" \
  -d '{
    "leds": [
      {"channel": 0, "start": 0, "count": 30, "brightness": 255, "color": "FF0000"},
      {"channel": 0, "start": 30, "count": 30, "brightness": 200, "color": "00FF00"},
      {"channel": 1, "start": 0, "count": 50, "brightness": 150, "color": "0000FF"}
    ]
  }'
```

#### `GET /controlBigLed`

**描述**: 控制大功率LED引脚 (Y0-Y3)

**请求参数** (URL查询参数):
- `y0` (string, 可选): Y0引脚状态，值为 "on" 或 "off"
- `y1` (string, 可选): Y1引脚状态，值为 "on" 或 "off"
- `y2` (string, 可选): Y2引脚状态，值为 "on" 或 "off"
- `y3` (string, 可选): Y3引脚状态，值为 "on" 或 "off"

**成功响应**:
```
状态码: 200
Content-Type: text/plain
响应体: "OK"
```

**示例**:
```
GET /controlBigLed?y0=on&y1=off&y2=on&y3=off
```

#### `POST /setAllLeds`

**描述**: 设置指定LED通道的所有灯珠为同一颜色和亮度

**请求参数** (URL查询参数):
- `strip` (integer, 必填): LED通道索引 (0-3)
- `color` (integer, 必填): 颜色值 (0-7)
- `brightness` (integer, 必填): 亮度级别 (0-31)

**成功响应**:
```
状态码: 200
Content-Type: text/plain
响应体: "All LEDs in the specified strip set successfully"
```

**失败响应**:
```
状态码: 400
Content-Type: text/plain

响应体示例:
"Invalid strip value. Must be between 0 and 3."
"Invalid color value. Must be between 0 and 7."
"Invalid brightness value. Must be between 0 and 31."
"Missing strip, color or brightness parameter"
```

**示例**:
```
POST /setAllLeds?strip=0&color=7&brightness=15
```

---

### 4. 设备参数配置

#### `POST /setDeviceParams`

**描述**: 设置设备参数 (支持Key-Value格式)

**请求参数**:
```json
Content-Type: application/json

{
  "factoryMode": false,
  "ledCount": 300
}
```

**参数说明**:
- `factoryMode` (boolean, 可选): 出厂模式开关
  - `true`: 启用出厂模式，LED红绿蓝白持续交替闪烁
  - `false`: 退出出厂模式，LED红绿蓝白闪烁2个循环后全灭
- `ledCount` (integer, 可选): 设置所有通道的LED数量
  - 有效范围: 1-1200
  - 设置后会立即保存到EEPROM，重启后生效
  - 影响所有4个通道的LED数量

**成功响应**:
```
状态码: 200
Content-Type: application/json

{
  "status": "success",
  "message": "Device parameters updated successfully"
}
```

**失败响应**:
```
状态码: 400
Content-Type: application/json

{
  "status": "error", 
  "message": "Body not received"
}

或

{
  "status": "error",
  "message": "Invalid JSON"
}
```

#### `GET /getDeviceParams`

**描述**: 获取设备参数配置信息

**请求参数**: 无

**成功响应**:
```
状态码: 200
Content-Type: application/json

{
  "factoryMode": false,
  "ledCount": 1200,
  "maxLedCount": 1200,
  "blinkInterval": 500,
  "ledRefreshRate": 100,
  "wifiEnabled": true,
  "wifiSSID": "MyWiFi",
  "wifiConnected": true,
  "useStaticIP": false,
  "staticIP": "DHCP",
  "subnet": "DHCP", 
  "gateway": "DHCP",
  "ethernetConnected": true,
  "firmwareVersion": "Dec 20 2024 15:30:45",
  "uptime": 123456,
  "freeHeap": 234567
}
```

**参数说明**:
- `factoryMode` (boolean): 当前出厂模式状态
- `ledCount` (integer): 当前设置的LED数量
- `maxLedCount` (integer): 支持的最大LED数量
- `blinkInterval` (integer): 闪烁间隔（毫秒）
- `ledRefreshRate` (integer): LED刷新率（Hz）
- `wifiEnabled` (boolean): WiFi自动重连是否启用
- `wifiSSID` (string): WiFi网络名称
- `wifiConnected` (boolean): WiFi是否已连接
- `useStaticIP` (boolean): 是否使用静态IP
- `staticIP` (string): 静态IP地址或"DHCP"
- `subnet` (string): 子网掩码或"DHCP"
- `gateway` (string): 网关地址或"DHCP"
- `ethernetConnected` (boolean): 以太网是否已连接
- `firmwareVersion` (string): 固件版本（编译时间）
- `uptime` (integer): 系统运行时间（毫秒）
- `freeHeap` (integer): 可用内存（字节）

#### `POST /setBlinkInterval`

**描述**: 设置闪烁间隔和LED刷新率

**请求参数** (URL查询参数):
- `interval` (integer, 必填): 闪烁间隔 (毫秒)
- `refreshRate` (integer, 必填): LED刷新率 (Hz)

**成功响应**:
```
状态码: 200
Content-Type: text/plain
响应体: "Blink interval and refresh rate updated successfully"
```

**失败响应**:
```
状态码: 400
Content-Type: text/plain

响应体示例:
"Invalid interval value or refreshRate value"
"Missing interval parameter or refreshRate parameter"
```

**示例**:
```
POST /setBlinkInterval?interval=1000&refreshRate=60
```

---

### 5. 网络配置

#### `GET /networkInfo`

**描述**: 获取设备网络信息

**请求参数**: 无

**成功响应**:
```
状态码: 200
Content-Type: application/json

{
  "ethernet_ip": "*************",
  "ethernet_mac": "AA:BB:CC:DD:EE:FF",
  "wifi_ip": "***********01", 
  "wifi_mac": "AA:BB:CC:DD:EE:FF",
  "BlinkInterval": 500,
  "ledRefreshRate": 100
}
```

**说明**:
- 当网络未连接时，IP显示为 "Not connected"
- MAC地址总是可用

#### `POST /setEthernetIP`

**描述**: 设置以太网静态IP配置

**请求参数**:
```json
Content-Type: application/json

{
  "ip": "*************",
  "subnet": "*************", 
  "gateway": "***********"
}
```

**参数说明**:
- `ip` (string, 必填): 静态IP地址
- `subnet` (string, 必填): 子网掩码
- `gateway` (string, 必填): 网关地址

**成功响应**:
```
状态码: 200
Content-Type: application/json

{
  "status": "success",
  "message": "Ethernet static IP set and saved, please check /networkInfo for the new IP"
}

或 (当以太网未连接时)

{
  "status": "success", 
  "message": "Ethernet static IP settings saved, will be applied when Ethernet connects"
}
```

**失败响应**:
```
状态码: 400
Content-Type: application/json

{
  "status": "error",
  "message": "Invalid JSON"
}

或

{
  "status": "error",
  "message": "Missing required fields"
}

或

{
  "status": "error",
  "message": "No data received"
}
```

#### `POST /clearEthernetIP`

**描述**: 清除以太网静态IP配置，恢复DHCP模式

**请求参数**: 空JSON对象 `{}`

**成功响应**:
```
状态码: 200
Content-Type: application/json

{
  "status": "success",
  "message": "Ethernet static IP settings cleared"
}
```

---

### 6. 系统管理

#### `POST /version`

**描述**: 获取固件版本信息

**请求参数**: 空JSON对象 `{}`

**成功响应**:
```
状态码: 200
Content-Type: application/json

{
  "compile_date": "Dec 20 2024",
  "compile_time": "10:30:15"
}
```

#### `POST /update`

**描述**: 固件OTA更新

**请求参数**: 
- Content-Type: multipart/form-data
- 固件文件 (.bin格式)

**成功响应**:
```
状态码: 200
Content-Type: text/plain
响应体: "Update successfully completed. Device will restart."
```

**失败响应**:
```
状态码: 500
Content-Type: text/plain

响应体示例:
"Update failed: [错误代码]"
"Update aborted."
```

**说明**: 更新成功后设备将自动重启

---

## 错误处理

### 404 未找到

当访问不存在的接口时返回：

```
状态码: 404
Content-Type: text/plain

响应体:
File Not Found

URI: /unknown
Method: GET
Arguments: 0
```

## 使用示例

### cURL命令示例

```bash
# 1. 获取设备信息
curl http://*************/

# 2. 设置WiFi配置
curl -X POST http://*************/updateWifiConfig \
  -H "Content-Type: application/json" \
  -d '{"ssid":"MyWiFi","password":"password123","enable":1}'

# 3. 退出出厂模式
curl -X POST http://*************/setDeviceParams \
  -H "Content-Type: application/json" \
  -d '{"factoryMode":false}'

# 4. 控制LED (通道0，红色，全亮度，前10个灯珠)
curl "http://*************/controlLEDs?data=4035797"

# 4a. 使用新接口控制多个储位LED (推荐)
curl -X POST http://*************/controlLEDsV2 \
  -H "Content-Type: application/json" \
  -d '{"leds":[{"channel":0,"start":0,"count":10,"brightness":255,"color":"FF0000"}]}'

# 5. 设置所有LED为白色
curl -X POST "http://*************/setAllLeds?strip=0&color=7&brightness=31"

# 6. 获取网络信息
curl http://*************/networkInfo

# 7. 控制大功率LED
curl "http://*************/controlBigLed?y0=on&y1=off"

# 8. 获取版本信息
curl -X POST http://*************/version \
  -H "Content-Type: application/json" \
  -d '{}'

# 9. 设置LED数量为300个并退出出厂模式
curl -X POST http://*************/setDeviceParams \
  -H "Content-Type: application/json" \
  -d '{"factoryMode":false,"ledCount":300}'

# 10. 获取设备参数配置
curl http://*************/getDeviceParams
```

## 注意事项

1. **LED通道**: 设备支持4个LED通道 (0-3)，每个通道最多1200个LED灯珠
2. **出厂模式**: 首次烧录后默认为出厂模式，需要调用`/setDeviceParams`接口退出
3. **网络**: 设备同时支持WiFi和以太网连接
4. **数据持久化**: 配置信息保存在EEPROM中，重启后保持
5. **安全性**: 建议在内网环境使用，避免外网直接暴露

## 版本信息

- **API版本**: 1.0
- **固件编译**: 动态生成 (基于编译时间)
- **支持协议**: HTTP/1.1 