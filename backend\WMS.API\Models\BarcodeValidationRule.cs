using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMS.API.Models
{
    /// <summary>
    /// 条码验证规则类型枚举
    /// </summary>
    public enum BarcodeValidationRuleType
    {
        /// <summary>
        /// 单储位单料号
        /// </summary>
        SINGLE_SKU_PER_LOCATION,

        /// <summary>
        /// 唯一码管控
        /// </summary>
        UNIQUE_CODE_CONTROL,

        /// <summary>
        /// 数量验证
        /// </summary>
        QUANTITY_VALIDATION,

        /// <summary>
        /// 物料存在性验证
        /// </summary>
        MATERIAL_EXISTENCE,

        /// <summary>
        /// 储位有效性验证
        /// </summary>
        STORAGE_LOCATION_VALIDITY,
    }

    /// <summary>
    /// 条码验证规则
    /// </summary>
    public class BarcodeValidationRule
    {
        /// <summary>
        /// 验证规则ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string RuleName { get; set; } = string.Empty;

        /// <summary>
        /// 规则类型
        /// </summary>
        public BarcodeValidationRuleType RuleType { get; set; }

        /// <summary>
        /// 规则配置参数（JSON格式）
        /// </summary>
        [Required]
        [Column(TypeName = "jsonb")]
        public string Configuration { get; set; } = "{}";

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 客户ID（可选，用于特定客户的规则）
        /// </summary>
        [StringLength(50)]
        public string? ClientId { get; set; }

        /// <summary>
        /// 规则描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }
    }
}
