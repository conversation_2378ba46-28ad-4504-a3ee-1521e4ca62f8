using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// ESP32控制器实体类
    /// 用于管理仓库中的LED灯带控制器
    /// </summary>
    public class ESP32Controller
    {
        /// <summary>
        /// 控制器唯一标识ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 控制器名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 控制器IP地址
        /// 注意：IP地址可能会变动，不应作为唯一标识
        /// </summary>
        [Required]
        [StringLength(50)]
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 控制器端口号，默认80
        /// </summary>
        public int Port { get; set; } = 80;

        /// <summary>
        /// 控制器描述信息
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 控制器在线状态
        /// </summary>
        public bool IsOnline { get; set; } = false;

        /// <summary>
        /// 最后一次心跳时间
        /// </summary>
        public DateTime LastHeartbeat { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// mDNS网络发现名称
        /// 用于网络自动发现功能
        /// </summary>
        [StringLength(100)]
        public string? MdnsName { get; set; }

        /// <summary>
        /// 关联的储位集合
        /// 一个控制器可以管理多个储位的LED灯条
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<StorageLocation>? StorageLocations { get; set; }
    }
}
