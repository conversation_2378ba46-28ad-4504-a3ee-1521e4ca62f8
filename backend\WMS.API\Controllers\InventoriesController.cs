using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class InventoriesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<InventoriesController> _logger;

        public InventoriesController(ApplicationDbContext context, ILogger<InventoriesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有库存
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetInventories()
        {
            try
            {
                var inventories = await _context.Inventories
                    .Include(i => i.Material)
                    .Include(i => i.StorageLocation)
                    .Select(i => new
                    {
                        i.Id,
                        i.StorageLocationId,
                        StorageLocation = new
                        {
                            i.StorageLocation!.Id,
                            i.StorageLocation.Code,
                            i.StorageLocation.Name
                        },
                        i.MaterialId,
                        Material = new
                        {
                            i.Material!.Id,
                            i.Material.Sku,
                            i.Material.Name,
                            i.Material.Unit
                        },
                        i.Quantity,
                        i.BatchNumber,
                        i.LpnCode,
                        i.Status,
                        i.ExpiryDate,
                        i.CreatedAt,
                        i.UpdatedAt
                    })
                    .OrderBy(i => i.Material.Sku)
                    .ThenBy(i => i.StorageLocation.Code)
                    .ToListAsync();

                return Ok(inventories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存列表失败");
                return StatusCode(500, "获取库存列表失败");
            }
        }

        /// <summary>
        /// 根据ID获取库存
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetInventory(int id)
        {
            try
            {
                var inventory = await _context.Inventories
                    .Include(i => i.Material)
                    .Include(i => i.StorageLocation)
                    .Where(i => i.Id == id)
                    .Select(i => new
                    {
                        i.Id,
                        i.StorageLocationId,
                        StorageLocation = new
                        {
                            i.StorageLocation!.Id,
                            i.StorageLocation.Code,
                            i.StorageLocation.Name
                        },
                        i.MaterialId,
                        Material = new
                        {
                            i.Material!.Id,
                            i.Material.Sku,
                            i.Material.Name,
                            i.Material.Unit
                        },
                        i.Quantity,
                        i.BatchNumber,
                        i.LpnCode,
                        i.Status,
                        i.ExpiryDate,
                        i.CreatedAt,
                        i.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (inventory == null)
                {
                    return NotFound("库存记录不存在");
                }

                return Ok(inventory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存详情失败，ID: {Id}", id);
                return StatusCode(500, "获取库存详情失败");
            }
        }

        /// <summary>
        /// 根据物料ID获取库存
        /// </summary>
        [HttpGet("by-material/{materialId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetInventoriesByMaterial(int materialId)
        {
            try
            {
                var inventories = await _context.Inventories
                    .Include(i => i.Material)
                    .Include(i => i.StorageLocation)
                    .Where(i => i.MaterialId == materialId)
                    .Select(i => new
                    {
                        i.Id,
                        i.StorageLocationId,
                        StorageLocation = new
                        {
                            i.StorageLocation!.Id,
                            i.StorageLocation.Code,
                            i.StorageLocation.Name
                        },
                        i.MaterialId,
                        Material = new
                        {
                            i.Material!.Id,
                            i.Material.Sku,
                            i.Material.Name,
                            i.Material.Unit
                        },
                        i.Quantity,
                        i.BatchNumber,
                        i.LpnCode,
                        i.Status,
                        i.ExpiryDate,
                        i.CreatedAt,
                        i.UpdatedAt
                    })
                    .OrderBy(i => i.StorageLocation.Code)
                    .ThenBy(i => i.CreatedAt)
                    .ToListAsync();

                return Ok(inventories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据物料ID获取库存失败，MaterialId: {MaterialId}", materialId);
                return StatusCode(500, "获取库存失败");
            }
        }

        /// <summary>
        /// 根据储位ID获取库存
        /// </summary>
        [HttpGet("by-location/{locationId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetInventoriesByLocation(int locationId)
        {
            try
            {
                var inventories = await _context.Inventories
                    .Include(i => i.Material)
                    .Include(i => i.StorageLocation)
                    .Where(i => i.StorageLocationId == locationId)
                    .Select(i => new
                    {
                        i.Id,
                        i.StorageLocationId,
                        StorageLocation = new
                        {
                            i.StorageLocation!.Id,
                            i.StorageLocation.Code,
                            i.StorageLocation.Name
                        },
                        i.MaterialId,
                        Material = new
                        {
                            i.Material!.Id,
                            i.Material.Sku,
                            i.Material.Name,
                            i.Material.Unit
                        },
                        i.Quantity,
                        i.BatchNumber,
                        i.LpnCode,
                        i.Status,
                        i.ExpiryDate,
                        i.CreatedAt,
                        i.UpdatedAt
                    })
                    .OrderBy(i => i.Material.Sku)
                    .ThenBy(i => i.CreatedAt)
                    .ToListAsync();

                return Ok(inventories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据储位ID获取库存失败，LocationId: {LocationId}", locationId);
                return StatusCode(500, "获取库存失败");
            }
        }

        /// <summary>
        /// 根据物料SKU获取库存汇总
        /// </summary>
        [HttpGet("summary/by-sku/{sku}")]
        public async Task<ActionResult<object>> GetInventorySummaryBySku(string sku)
        {
            try
            {
                var summary = await _context.Inventories
                    .Include(i => i.Material)
                    .Where(i => i.Material!.Sku == sku)
                    .GroupBy(i => new { i.Material!.Sku, i.Material.Name, i.Material.Unit })
                    .Select(g => new
                    {
                        Sku = g.Key.Sku,
                        Name = g.Key.Name,
                        Unit = g.Key.Unit,
                        TotalQuantity = g.Sum(i => i.Quantity),
                        AvailableQuantity = g.Where(i => i.Status == InventoryStatus.Available).Sum(i => i.Quantity),
                        PendingQuantity = g.Where(i => i.Status == InventoryStatus.Pending).Sum(i => i.Quantity),
                        FrozenQuantity = g.Where(i => i.Status == InventoryStatus.Frozen).Sum(i => i.Quantity),
                        LocationCount = g.Select(i => i.StorageLocationId).Distinct().Count(),
                        BatchCount = g.Select(i => i.BatchNumber).Distinct().Count()
                    })
                    .FirstOrDefaultAsync();

                if (summary == null)
                {
                    return NotFound($"物料SKU '{sku}' 没有库存记录");
                }

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据SKU获取库存汇总失败，SKU: {Sku}", sku);
                return StatusCode(500, "获取库存汇总失败");
            }
        }

        /// <summary>
        /// 获取库存状态统计
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<object>> GetInventoryStatistics()
        {
            try
            {
                var statistics = await _context.Inventories
                    .GroupBy(i => i.Status)
                    .Select(g => new
                    {
                        Status = g.Key,
                        Count = g.Count(),
                        TotalQuantity = g.Sum(i => i.Quantity)
                    })
                    .ToListAsync();

                var totalRecords = await _context.Inventories.CountAsync();
                var totalMaterials = await _context.Inventories.Select(i => i.MaterialId).Distinct().CountAsync();
                var totalLocations = await _context.Inventories.Select(i => i.StorageLocationId).Distinct().CountAsync();

                var result = new
                {
                    TotalRecords = totalRecords,
                    TotalMaterials = totalMaterials,
                    TotalLocations = totalLocations,
                    StatusStatistics = statistics
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存统计失败");
                return StatusCode(500, "获取库存统计失败");
            }
        }

        /// <summary>
        /// 按批次号获取库存
        /// </summary>
        [HttpGet("by-batch/{batchNumber}")]
        public async Task<ActionResult<IEnumerable<object>>> GetInventoriesByBatch(string batchNumber)
        {
            try
            {
                var inventories = await _context.Inventories
                    .Include(i => i.Material)
                    .Include(i => i.StorageLocation)
                    .Where(i => i.BatchNumber == batchNumber)
                    .Select(i => new
                    {
                        i.Id,
                        i.StorageLocationId,
                        StorageLocation = new
                        {
                            i.StorageLocation!.Id,
                            i.StorageLocation.Code,
                            i.StorageLocation.Name
                        },
                        i.MaterialId,
                        Material = new
                        {
                            i.Material!.Id,
                            i.Material.Sku,
                            i.Material.Name,
                            i.Material.Unit
                        },
                        i.Quantity,
                        i.BatchNumber,
                        i.LpnCode,
                        i.Status,
                        i.ExpiryDate,
                        i.CreatedAt,
                        i.UpdatedAt
                    })
                    .OrderBy(i => i.Material.Sku)
                    .ThenBy(i => i.StorageLocation.Code)
                    .ToListAsync();

                return Ok(inventories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据批次号获取库存失败，BatchNumber: {BatchNumber}", batchNumber);
                return StatusCode(500, "获取库存失败");
            }
        }

        /// <summary>
        /// 搜索库存
        /// </summary>
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<object>>> SearchInventories(
            string? sku = null,
            string? materialName = null,
            string? locationCode = null,
            string? batchNumber = null,
            InventoryStatus? status = null)
        {
            try
            {
                var query = _context.Inventories
                    .Include(i => i.Material)
                    .Include(i => i.StorageLocation)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(sku))
                {
                    query = query.Where(i => i.Material!.Sku.Contains(sku));
                }

                if (!string.IsNullOrWhiteSpace(materialName))
                {
                    query = query.Where(i => i.Material!.Name.Contains(materialName));
                }

                if (!string.IsNullOrWhiteSpace(locationCode))
                {
                    query = query.Where(i => i.StorageLocation!.Code.Contains(locationCode));
                }

                if (!string.IsNullOrWhiteSpace(batchNumber))
                {
                    query = query.Where(i => i.BatchNumber == batchNumber);
                }

                if (status.HasValue)
                {
                    query = query.Where(i => i.Status == status.Value);
                }

                var inventories = await query
                    .Select(i => new
                    {
                        i.Id,
                        i.StorageLocationId,
                        StorageLocation = new
                        {
                            i.StorageLocation!.Id,
                            i.StorageLocation.Code,
                            i.StorageLocation.Name
                        },
                        i.MaterialId,
                        Material = new
                        {
                            i.Material!.Id,
                            i.Material.Sku,
                            i.Material.Name,
                            i.Material.Unit
                        },
                        i.Quantity,
                        i.BatchNumber,
                        i.LpnCode,
                        i.Status,
                        i.ExpiryDate,
                        i.CreatedAt,
                        i.UpdatedAt
                    })
                    .OrderBy(i => i.Material.Sku)
                    .ThenBy(i => i.StorageLocation.Code)
                    .ToListAsync();

                return Ok(inventories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索库存失败");
                return StatusCode(500, "搜索库存失败");
            }
        }
    }
}