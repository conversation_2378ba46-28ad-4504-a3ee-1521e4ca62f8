<template>
  <a-menu
    v-model:selectedKeys="selectedKeys"
    v-model:openKeys="openKeys"
    mode="inline"
    theme="light"
    class="app-menu"
    @click="handleMenuClick"
  >
    <template v-for="item in menuItems" :key="item.key">
      <a-menu-item
        v-if="!item.children && hasPermission(item.permission)"
        :key="item.key"
      >
        <component :is="item.icon" v-if="item.icon" />
        <span>{{ item.label }}</span>
      </a-menu-item>
      
      <a-sub-menu
        v-else-if="item.children && hasPermission(item.permission)"
        :key="item.key"
      >
        <template #title>
          <component :is="item.icon" v-if="item.icon" />
          <span>{{ item.label }}</span>
        </template>
        
        <a-menu-item
          v-for="child in item.children"
          :key="child.key"
          v-show="hasPermission(child.permission)"
        >
          <component :is="child.icon" v-if="child.icon" />
          <span>{{ child.label }}</span>
        </a-menu-item>
      </a-sub-menu>
    </template>
  </a-menu>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  DashboardOutlined,
  HomeOutlined,
  InboxOutlined,
  ShopOutlined,
  BarChartOutlined,
  AlertOutlined,
  ImportOutlined,
  ExportOutlined,
  SwapOutlined,
  ShoppingCartOutlined,
  AuditOutlined,
  SettingOutlined,
  MonitorOutlined,
  BulbOutlined,
  TeamOutlined,
  UserOutlined,
  SafetyOutlined,
  FileTextOutlined,
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

const menuItems = [
  {
    key: 'dashboard',
    label: '首页',
    icon: DashboardOutlined,
    path: '/',
    permission: null,
  },
  {
    key: 'warehouse',
    label: '仓库管理',
    icon: HomeOutlined,
    permission: 'warehouse_management',
    children: [
      {
        key: 'warehouse-list',
        label: '仓库列表',
        icon: HomeOutlined,
        path: '/warehouse',
        permission: 'warehouse_management',
      },
      {
        key: 'zone-list',
        label: '区域管理',
        icon: InboxOutlined,
        path: '/warehouse/zones',
        permission: 'warehouse_management',
      },
      {
        key: 'shelf-list',
        label: '货架管理',
        icon: ShopOutlined,
        path: '/warehouse/shelves',
        permission: 'warehouse_management',
      },
      {
        key: 'storage-location-list',
        label: '储位管理',
        icon: InboxOutlined,
        path: '/warehouse/storage-locations',
        permission: 'warehouse_management',
      },
    ],
  },
  {
    key: 'inventory',
    label: '库存管理',
    icon: InboxOutlined,
    permission: 'inventory_management',
    children: [
      {
        key: 'inventory-list',
        label: '库存查询',
        icon: InboxOutlined,
        path: '/inventory',
        permission: 'inventory_management',
      },
      {
        key: 'inventory-statistics',
        label: '库存统计',
        icon: BarChartOutlined,
        path: '/inventory/statistics',
        permission: 'inventory_management',
      },
      {
        key: 'inventory-alerts',
        label: '库存预警',
        icon: AlertOutlined,
        path: '/inventory/alerts',
        permission: 'inventory_management',
      },
    ],
  },
  {
    key: 'operations',
    label: '作业管理',
    icon: SwapOutlined,
    permission: 'operations_management',
    children: [
      {
        key: 'operations-list',
        label: '作业总览',
        icon: SwapOutlined,
        path: '/operations',
        permission: 'operations_management',
      },
      {
        key: 'inbound-operations',
        label: '入库作业',
        icon: ImportOutlined,
        path: '/operations/inbound',
        permission: 'operations_management',
      },
      {
        key: 'outbound-operations',
        label: '出库作业',
        icon: ExportOutlined,
        path: '/operations/outbound',
        permission: 'operations_management',
      },
      {
        key: 'transfer-operations',
        label: '移库作业',
        icon: SwapOutlined,
        path: '/operations/transfer',
        permission: 'operations_management',
      },
      {
        key: 'picking-operations',
        label: '拣货作业',
        icon: ShoppingCartOutlined,
        path: '/operations/picking',
        permission: 'operations_management',
      },
      {
        key: 'stocktaking-operations',
        label: '盘点作业',
        icon: AuditOutlined,
        path: '/operations/stocktaking',
        permission: 'operations_management',
      },
    ],
  },
  {
    key: 'hardware',
    label: '硬件管理',
    icon: SettingOutlined,
    permission: 'esp32_management',
    children: [
      {
        key: 'esp32-list',
        label: 'ESP32设备',
        icon: SettingOutlined,
        path: '/hardware',
        permission: 'esp32_management',
      },
      {
        key: 'device-monitoring',
        label: '设备监控',
        icon: MonitorOutlined,
        path: '/hardware/monitoring',
        permission: 'esp32_management',
      },
      {
        key: 'led-control',
        label: 'LED控制',
        icon: BulbOutlined,
        path: '/hardware/led-control',
        permission: 'esp32_management',
      },
    ],
  },
  {
    key: 'users',
    label: '用户管理',
    icon: TeamOutlined,
    permission: 'user_management',
    children: [
      {
        key: 'user-list',
        label: '用户管理',
        icon: UserOutlined,
        path: '/users',
        permission: 'user_management',
      },
      {
        key: 'role-list',
        label: '角色权限',
        icon: SafetyOutlined,
        path: '/users/roles',
        permission: 'user_management',
      },
      {
        key: 'operation-logs',
        label: '操作日志',
        icon: FileTextOutlined,
        path: '/users/logs',
        permission: 'user_management',
      },
    ],
  },
]

const hasPermission = (permission: string | null): boolean => {
  if (!permission) return true
  return authStore.hasPermission(permission)
}

const findMenuKeyByPath = (path: string): string | null => {
  for (const item of menuItems) {
    if (item.path === path) {
      return item.key
    }
    if (item.children) {
      for (const child of item.children) {
        if (child.path === path) {
          return child.key
        }
      }
    }
  }
  return null
}

const findParentKey = (menuKey: string): string | null => {
  for (const item of menuItems) {
    if (item.children) {
      for (const child of item.children) {
        if (child.key === menuKey) {
          return item.key
        }
      }
    }
  }
  return null
}

const updateSelectedKeys = () => {
  const menuKey = findMenuKeyByPath(route.path)
  if (menuKey) {
    selectedKeys.value = [menuKey]
    const parentKey = findParentKey(menuKey)
    if (parentKey && !openKeys.value.includes(parentKey)) {
      openKeys.value.push(parentKey)
    }
  }
}

const handleMenuClick = ({ key }: { key: string }) => {
  // Find the menu item by key
  const findMenuItem = (items: typeof menuItems): any => {
    for (const item of items) {
      if (item.key === key) {
        return item
      }
      if (item.children) {
        const found = findMenuItem(item.children)
        if (found) return found
      }
    }
    return null
  }
  
  const menuItem = findMenuItem(menuItems)
  if (menuItem?.path) {
    router.push(menuItem.path)
  }
}

// Watch route changes to update selected keys
watch(
  () => route.path,
  () => {
    updateSelectedKeys()
  },
  { immediate: true }
)
</script>

<style scoped lang="less">
.app-menu {
  border-right: none;
  height: 100%;
  
  :deep(.ant-menu-item) {
    margin: 4px 8px;
    border-radius: 6px;
    
    &.ant-menu-item-selected {
      background: #e6f7ff;
      color: #1890ff;
      
      &::after {
        display: none;
      }
    }
  }
  
  :deep(.ant-menu-submenu) {
    .ant-menu-submenu-title {
      margin: 4px 8px;
      border-radius: 6px;
      
      &:hover {
        background: #f5f5f5;
      }
    }
  }
  
  :deep(.ant-menu-sub) {
    background: #fafafa;
    
    .ant-menu-item {
      &.ant-menu-item-selected {
        background: #e6f7ff;
        color: #1890ff;
      }
    }
  }
}
</style>