{"id": "b1c2d3e4-f5g6-7h8i-9j0k-l1m2n3o4p5q6", "name": "WMS-VGL Development Environment", "values": [{"key": "baseUrl", "value": "http://localhost:5000", "description": "API服务器基础URL - 本地开发环境", "type": "default", "enabled": true}, {"key": "baseUrlWSL", "value": "http://************:5000", "description": "WSL环境下的API服务器URL（通过网关IP访问）", "type": "default", "enabled": true}, {"key": "accessToken", "value": "", "description": "JWT访问令牌（登录后自动设置）", "type": "secret", "enabled": true}, {"key": "refreshToken", "value": "", "description": "JWT刷新令牌（登录后自动设置）", "type": "secret", "enabled": true}, {"key": "adminEmail", "value": "<EMAIL>", "description": "默认管理员邮箱", "type": "default", "enabled": true}, {"key": "adminPassword", "value": "Admin@123456", "description": "默认管理员密码", "type": "secret", "enabled": true}, {"key": "userId", "value": "", "description": "测试用户ID（用于用户管理API测试）", "type": "default", "enabled": true}, {"key": "controllerId", "value": "1", "description": "测试控制器ID（用于ESP32控制器API测试）", "type": "default", "enabled": true}, {"key": "storageLocationId", "value": "1", "description": "测试储位ID（用于储位管理API测试）", "type": "default", "enabled": true}, {"key": "testUserEmail", "value": "<EMAIL>", "description": "测试用户邮箱", "type": "default", "enabled": true}, {"key": "testUserPassword", "value": "Test@123456", "description": "测试用户密码", "type": "secret", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-06-26T12:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}