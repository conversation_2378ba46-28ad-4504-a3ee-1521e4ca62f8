<template>
  <a-layout class="app-layout">
    <a-layout-header class="app-header">
      <div class="header-left">
        <div class="logo">
          <span class="logo-icon">📦</span>
          <span class="logo-text">WMS-VGL</span>
        </div>
        <nav class="main-nav">
          <a-menu
            mode="horizontal"
            :selected-keys="[currentModule]"
            class="nav-menu"
            @click="handleNavClick"
          >
            <a-menu-item key="dashboard">首页</a-menu-item>
            <a-menu-item key="warehouse">仓库管理</a-menu-item>
            <a-menu-item key="inventory">库存管理</a-menu-item>
            <a-menu-item key="operations">作业管理</a-menu-item>
            <a-menu-item key="hardware">硬件管理</a-menu-item>
            <a-menu-item key="users">用户管理</a-menu-item>
          </a-menu>
        </nav>
      </div>

      <div class="header-right">
        <a-space>
          <a-badge :count="3">
            <a-button type="text" shape="circle">
              <BellOutlined />
            </a-button>
          </a-badge>

          <a-dropdown>
            <a-button type="text" class="user-button">
              <a-avatar size="small" :src="userAvatar">
                {{ userInitials }}
              </a-avatar>
              <span class="username">{{ authStore.user?.firstName || '管理员' }}</span>
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人资料
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </a-layout-header>
    
    <a-layout>
      <a-layout-sider
        v-model:collapsed="collapsed"
        :trigger="null"
        collapsible
        class="app-sider"
        width="200"
      >
        <div class="sidebar-toggle-wrapper">
          <a-button
            type="text"
            @click="toggleSidebar"
            class="sidebar-toggle"
          >
            <MenuFoldOutlined v-if="!collapsed" />
            <MenuUnfoldOutlined v-if="collapsed" />
          </a-button>
        </div>
        <AppMenu />
      </a-layout-sider>

      <a-layout-content class="app-content">
        <AppBreadcrumb />
        <div class="content-wrapper">
          <RouterView />
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Modal, message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DownOutlined,
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import AppMenu from '@/components/common/AppMenu.vue'
import AppBreadcrumb from '@/components/common/AppBreadcrumb.vue'

const router = useRouter()
const authStore = useAuthStore()

const collapsed = ref(false)
const currentModule = computed(() => {
  const route = router.currentRoute.value
  const path = route.path
  if (path.startsWith('/warehouse')) return 'warehouse'
  if (path.startsWith('/inventory')) return 'inventory'
  if (path.startsWith('/operations')) return 'operations'
  if (path.startsWith('/hardware')) return 'hardware'
  if (path.startsWith('/users')) return 'users'
  return 'dashboard'
})

const userAvatar = computed(() => {
  // You can implement avatar logic here
  return null
})

const userInitials = computed(() => {
  const user = authStore.user
  if (!user?.firstName && !user?.lastName) return 'U'
  return `${user?.firstName?.[0] || ''}${user?.lastName?.[0] || ''}`.toUpperCase()
})

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const handleNavClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'dashboard':
      router.push('/')
      break
    case 'warehouse':
      router.push('/warehouse')
      break
    case 'inventory':
      router.push('/inventory')
      break
    case 'operations':
      router.push('/operations')
      break
    case 'hardware':
      router.push('/hardware')
      break
    case 'users':
      router.push('/users')
      break
  }
}

const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '您确定要退出系统吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await authStore.logout()
        message.success('已退出登录')
        router.push('/login')
      } catch (error) {
        message.error('退出登录失败')
      }
    },
  })
}
</script>

<style scoped lang="less">
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: #001529;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 32px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.logo-icon {
  font-size: 24px;
}

.logo-text {
  transition: all 0.2s;
}

.main-nav {
  .nav-menu {
    background: transparent;
    border-bottom: none;

    :deep(.ant-menu-item) {
      color: rgba(255, 255, 255, 0.85);
      border-bottom: 2px solid transparent;

      &:hover {
        color: white;
        background: #1890ff;
      }

      &.ant-menu-item-selected {
        color: white;
        background: #1890ff;
        border-bottom-color: #1890ff;
      }
    }
  }
}

.header-right {
  :deep(.ant-btn) {
    color: rgba(255, 255, 255, 0.85);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }
  }

  .user-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 12px;
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.85);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }
  }

  .username {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.85);
  }
}

.sidebar-toggle-wrapper {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .sidebar-toggle {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;

    &:hover {
      background: #f5f5f5;
    }
  }
}

.app-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.02);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.app-content {
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  padding: 24px;
  min-height: calc(100vh - 64px - 40px);
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 12px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .username {
    display: none;
  }
}
</style>