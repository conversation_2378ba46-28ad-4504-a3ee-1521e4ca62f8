<template>
  <a-layout class="app-layout">
    <a-layout-header class="app-header">
      <div class="header-left">
        <a-button 
          type="text" 
          @click="toggleSidebar"
          class="sidebar-toggle"
        >
          <MenuFoldOutlined v-if="collapsed" />
          <MenuUnfoldOutlined v-else />
        </a-button>
        <div class="logo">
          <span class="logo-icon">📦</span>
          <span v-if="!collapsed" class="logo-text">WMS-VGL</span>
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-badge :count="3">
            <a-button type="text" shape="circle">
              <BellOutlined />
            </a-button>
          </a-badge>
          
          <a-dropdown>
            <a-button type="text" class="user-button">
              <a-avatar size="small" :src="userAvatar">
                {{ userInitials }}
              </a-avatar>
              <span class="username">{{ authStore.user?.firstName || '用户' }}</span>
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人资料
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </a-layout-header>
    
    <a-layout>
      <a-layout-sider
        v-model:collapsed="collapsed"
        :trigger="null"
        collapsible
        class="app-sider"
      >
        <AppMenu />
      </a-layout-sider>
      
      <a-layout-content class="app-content">
        <AppBreadcrumb />
        <div class="content-wrapper">
          <RouterView />
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Modal, message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DownOutlined,
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import AppMenu from '@/components/common/AppMenu.vue'
import AppBreadcrumb from '@/components/common/AppBreadcrumb.vue'

const router = useRouter()
const authStore = useAuthStore()

const collapsed = ref(false)

const userAvatar = computed(() => {
  // You can implement avatar logic here
  return null
})

const userInitials = computed(() => {
  const user = authStore.user
  if (!user?.firstName && !user?.lastName) return 'U'
  return `${user?.firstName?.[0] || ''}${user?.lastName?.[0] || ''}`.toUpperCase()
})

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '您确定要退出系统吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await authStore.logout()
        message.success('已退出登录')
        router.push('/login')
      } catch (error) {
        message.error('退出登录失败')
      }
    },
  })
}
</script>

<style scoped lang="less">
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: #fff;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  font-size: 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.logo-icon {
  font-size: 24px;
}

.logo-text {
  transition: all 0.2s;
}

.header-right {
  .user-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 12px;
    border-radius: 6px;
    
    &:hover {
      background: #f5f5f5;
    }
  }
  
  .username {
    font-size: 14px;
    color: #262626;
  }
}

.app-sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.app-content {
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  padding: 24px;
  min-height: calc(100vh - 64px - 40px);
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 12px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .username {
    display: none;
  }
}
</style>