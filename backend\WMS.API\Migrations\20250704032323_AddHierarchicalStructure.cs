﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace WMS.API.Migrations
{
    /// <inheritdoc />
    public partial class AddHierarchicalStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Shelf",
                table: "StorageLocations",
                newName: "ShelfCode");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "StorageLocations",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Capacity",
                table: "StorageLocations",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Configuration",
                table: "StorageLocations",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Coordinates",
                table: "StorageLocations",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "StorageLocations",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Dimensions",
                table: "StorageLocations",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastInventoryDate",
                table: "StorageLocations",
                type: "timestamptz",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MaxWeight",
                table: "StorageLocations",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "StorageLocations",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NextInventoryDate",
                table: "StorageLocations",
                type: "timestamptz",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Position",
                table: "StorageLocations",
                type: "character varying(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Properties",
                table: "StorageLocations",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Remarks",
                table: "StorageLocations",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResponsiblePerson",
                table: "StorageLocations",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ShelfId",
                table: "StorageLocations",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UpdatedBy",
                table: "StorageLocations",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "LocationCapabilities",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    StorageLocationId = table.Column<int>(type: "integer", nullable: false),
                    CapabilityType = table.Column<int>(type: "integer", nullable: false),
                    CapabilityLevel = table.Column<int>(type: "integer", nullable: false),
                    CapabilityName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Parameters = table.Column<string>(type: "jsonb", nullable: true),
                    Configuration = table.Column<string>(type: "jsonb", nullable: true),
                    IsEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    ValidationRules = table.Column<string>(type: "jsonb", nullable: true),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    EffectiveTo = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    Certifications = table.Column<string>(type: "jsonb", nullable: true),
                    LastVerificationDate = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    NextVerificationDate = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    Remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LocationCapabilities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LocationCapabilities_StorageLocations_StorageLocationId",
                        column: x => x.StorageLocationId,
                        principalTable: "StorageLocations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "LocationClassifications",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    StorageLocationId = table.Column<int>(type: "integer", nullable: false),
                    Dimension = table.Column<int>(type: "integer", nullable: false),
                    Category = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Value = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DisplayName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Tags = table.Column<string>(type: "jsonb", nullable: true),
                    Properties = table.Column<string>(type: "jsonb", nullable: true),
                    Configuration = table.Column<string>(type: "jsonb", nullable: true),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    IsEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    IsAutoAssigned = table.Column<bool>(type: "boolean", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    EffectiveTo = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    IsInherited = table.Column<bool>(type: "boolean", nullable: false),
                    InheritedFrom = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ValidationRules = table.Column<string>(type: "jsonb", nullable: true),
                    BusinessRules = table.Column<string>(type: "jsonb", nullable: true),
                    ExternalSystemRef = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Remarks = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LocationClassifications", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LocationClassifications_StorageLocations_StorageLocationId",
                        column: x => x.StorageLocationId,
                        principalTable: "StorageLocations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Warehouses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Address = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: true),
                    WarehouseType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    TotalArea = table.Column<decimal>(type: "numeric", nullable: true),
                    TotalVolume = table.Column<decimal>(type: "numeric", nullable: true),
                    ContactPerson = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Configuration = table.Column<string>(type: "jsonb", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Warehouses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Zones",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    WarehouseId = table.Column<int>(type: "integer", nullable: false),
                    ZoneType = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Area = table.Column<decimal>(type: "numeric", nullable: true),
                    Volume = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxWeight = table.Column<decimal>(type: "numeric", nullable: true),
                    TemperatureRange = table.Column<string>(type: "jsonb", nullable: true),
                    HumidityRange = table.Column<string>(type: "jsonb", nullable: true),
                    SecurityLevel = table.Column<int>(type: "integer", nullable: false),
                    AccessControl = table.Column<string>(type: "jsonb", nullable: true),
                    SpecialRequirements = table.Column<string>(type: "jsonb", nullable: true),
                    Configuration = table.Column<string>(type: "jsonb", nullable: true),
                    ResponsiblePerson = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ContactInfo = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Zones", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Zones_Warehouses_WarehouseId",
                        column: x => x.WarehouseId,
                        principalTable: "Warehouses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Shelves",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ZoneId = table.Column<int>(type: "integer", nullable: false),
                    ShelfType = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Length = table.Column<decimal>(type: "numeric", nullable: true),
                    Width = table.Column<decimal>(type: "numeric", nullable: true),
                    Height = table.Column<decimal>(type: "numeric", nullable: true),
                    Levels = table.Column<int>(type: "integer", nullable: true),
                    PositionsPerLevel = table.Column<int>(type: "integer", nullable: true),
                    MaxWeight = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxWeightPerPosition = table.Column<decimal>(type: "numeric", nullable: true),
                    Material = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Manufacturer = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ManufactureDate = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    InstallationDate = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    LastInspectionDate = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    NextInspectionDate = table.Column<DateTime>(type: "timestamptz", nullable: true),
                    SafetyCertifications = table.Column<string>(type: "jsonb", nullable: true),
                    StoragePolicy = table.Column<string>(type: "jsonb", nullable: true),
                    AccessRestrictions = table.Column<string>(type: "jsonb", nullable: true),
                    Configuration = table.Column<string>(type: "jsonb", nullable: true),
                    ResponsiblePerson = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Remarks = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamptz", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Shelves", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Shelves_Zones_ZoneId",
                        column: x => x.ZoneId,
                        principalTable: "Zones",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Create default hierarchy structure for existing data
            migrationBuilder.InsertData(
                table: "Warehouses",
                columns: new[] { "Id", "Code", "Name", "Status", "CreatedAt", "UpdatedAt" },
                values: new object[] { 1, "DEFAULT", "默认仓库", 1, DateTime.UtcNow, DateTime.UtcNow });

            migrationBuilder.InsertData(
                table: "Zones", 
                columns: new[] { "Id", "Code", "Name", "WarehouseId", "ZoneType", "Status", "SecurityLevel", "CreatedAt", "UpdatedAt" },
                values: new object[] { 1, "DEFAULT", "默认区域", 1, 1, 1, 1, DateTime.UtcNow, DateTime.UtcNow });

            migrationBuilder.InsertData(
                table: "Shelves",
                columns: new[] { "Id", "Code", "Name", "ZoneId", "ShelfType", "Status", "CreatedAt", "UpdatedAt" },
                values: new object[] { 1, "DEFAULT", "默认货架", 1, 1, 1, DateTime.UtcNow, DateTime.UtcNow });

            // Update existing StorageLocations to reference the default shelf
            migrationBuilder.Sql("UPDATE \"StorageLocations\" SET \"ShelfId\" = 1 WHERE \"ShelfId\" IS NULL;");

            // Fill compatibility fields from the default hierarchy
            migrationBuilder.Sql("UPDATE \"StorageLocations\" SET \"Zone\" = 'DEFAULT', \"ShelfCode\" = 'DEFAULT' WHERE \"Zone\" IS NULL OR \"ShelfCode\" IS NULL;");

            migrationBuilder.CreateIndex(
                name: "IX_StorageLocations_ShelfId",
                table: "StorageLocations",
                column: "ShelfId");

            migrationBuilder.CreateIndex(
                name: "IX_LocationCapabilities_StorageLocationId_CapabilityType",
                table: "LocationCapabilities",
                columns: new[] { "StorageLocationId", "CapabilityType" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LocationClassifications_StorageLocationId_Dimension_Category",
                table: "LocationClassifications",
                columns: new[] { "StorageLocationId", "Dimension", "Category" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Shelves_ZoneId_Code",
                table: "Shelves",
                columns: new[] { "ZoneId", "Code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Warehouses_Code",
                table: "Warehouses",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Zones_WarehouseId_Code",
                table: "Zones",
                columns: new[] { "WarehouseId", "Code" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_StorageLocations_Shelves_ShelfId",
                table: "StorageLocations",
                column: "ShelfId",
                principalTable: "Shelves",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StorageLocations_Shelves_ShelfId",
                table: "StorageLocations");

            migrationBuilder.DropTable(
                name: "LocationCapabilities");

            migrationBuilder.DropTable(
                name: "LocationClassifications");

            migrationBuilder.DropTable(
                name: "Shelves");

            migrationBuilder.DropTable(
                name: "Zones");

            migrationBuilder.DropTable(
                name: "Warehouses");

            migrationBuilder.DropIndex(
                name: "IX_StorageLocations_ShelfId",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "Capacity",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "Configuration",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "Coordinates",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "Dimensions",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "LastInventoryDate",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "MaxWeight",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "NextInventoryDate",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "Position",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "Properties",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "Remarks",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "ResponsiblePerson",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "ShelfId",
                table: "StorageLocations");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "StorageLocations");

            migrationBuilder.RenameColumn(
                name: "ShelfCode",
                table: "StorageLocations",
                newName: "Shelf");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "StorageLocations",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(500)",
                oldMaxLength: 500,
                oldNullable: true);
        }
    }
}
