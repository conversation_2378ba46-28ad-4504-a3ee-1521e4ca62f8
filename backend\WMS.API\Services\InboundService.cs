using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;
using WMS.API.Services;

namespace WMS.API.Services
{
    public interface IInboundService
    {
        Task<InboundOrder> CreateInboundOrderAsync(
            InboundOrderCreateRequest request,
            string createdBy
        );
        Task<InboundOrderItem> AddItemToOrderAsync(int orderId, InboundOrderItemRequest request);
        Task<bool> ProcessInboundAsync(
            int orderId,
            InboundProcessRequest request,
            string processedBy
        );
        Task<bool> ProcessItemWithBarcodeAsync(
            int itemId,
            BarcodeInboundRequest request,
            string processedBy
        );
        Task<IEnumerable<InboundOrder>> GetInboundOrdersAsync(InboundOrderStatus? status = null);
        Task<InboundOrder?> GetInboundOrderByIdAsync(int id);
        Task<InboundOrder?> GetInboundOrderByNumberAsync(string orderNumber);
        Task<bool> UpdateInboundOrderStatusAsync(
            int orderId,
            InboundOrderStatus status,
            string? processedBy = null
        );
        Task<bool> CancelInboundOrderAsync(int orderId, string reason, string cancelledBy);
        Task<string> GenerateInboundOrderNumberAsync();
    }

    public class InboundService : IInboundService
    {
        private readonly ApplicationDbContext _context;
        private readonly IInventoryTransactionService _transactionService;
        private readonly IBarcodeParsingService _barcodeService;
        private readonly ILogger<InboundService> _logger;

        public InboundService(
            ApplicationDbContext context,
            IInventoryTransactionService transactionService,
            IBarcodeParsingService barcodeService,
            ILogger<InboundService> logger
        )
        {
            _context = context;
            _transactionService = transactionService;
            _barcodeService = barcodeService;
            _logger = logger;
        }

        public async Task<InboundOrder> CreateInboundOrderAsync(
            InboundOrderCreateRequest request,
            string createdBy
        )
        {
            try
            {
                var orderNumber = await GenerateInboundOrderNumberAsync();

                var order = new InboundOrder
                {
                    OrderNumber = orderNumber,
                    SupplierName = request.SupplierName,
                    SupplierCode = request.SupplierCode,
                    ExpectedDate = request.ExpectedDate,
                    Remarks = request.Remarks,
                    CreatedBy = createdBy,
                    Status = InboundOrderStatus.Pending,
                };

                _context.InboundOrders.Add(order);
                await _context.SaveChangesAsync();

                // 添加订单项目
                if (request.Items != null && request.Items.Any())
                {
                    foreach (var itemRequest in request.Items)
                    {
                        await AddItemToOrderAsync(order.Id, itemRequest);
                    }
                }

                _logger.LogInformation(
                    "Created inbound order {OrderNumber} with {ItemCount} items",
                    orderNumber,
                    request.Items?.Count() ?? 0
                );

                return order;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inbound order");
                throw;
            }
        }

        public async Task<InboundOrderItem> AddItemToOrderAsync(
            int orderId,
            InboundOrderItemRequest request
        )
        {
            try
            {
                var order = await _context.InboundOrders.FindAsync(orderId);
                if (order == null)
                {
                    throw new ArgumentException("Inbound order not found", nameof(orderId));
                }

                if (order.Status != InboundOrderStatus.Pending)
                {
                    throw new InvalidOperationException("Cannot add items to non-pending order");
                }

                var material = await _context.Materials.FindAsync(request.MaterialId);
                if (material == null)
                {
                    throw new ArgumentException("Material not found", nameof(request.MaterialId));
                }

                var item = new InboundOrderItem
                {
                    InboundOrderId = orderId,
                    MaterialId = request.MaterialId,
                    ExpectedQuantity = request.ExpectedQuantity,
                    BatchNumber = request.BatchNumber,
                    LpnCode = request.LpnCode,
                    ExpiryDate = request.ExpiryDate,
                    Remarks = request.Remarks,
                };

                _context.InboundOrderItems.Add(item);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Added item to inbound order {OrderId}: Material {MaterialId}, Quantity {Quantity}",
                    orderId,
                    request.MaterialId,
                    request.ExpectedQuantity
                );

                return item;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to inbound order {OrderId}", orderId);
                throw;
            }
        }

        public async Task<bool> ProcessInboundAsync(
            int orderId,
            InboundProcessRequest request,
            string processedBy
        )
        {
            using var dbTransaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var order = await _context
                    .InboundOrders.Include(o => o.Items)
                    .ThenInclude(i => i.Material)
                    .FirstOrDefaultAsync(o => o.Id == orderId);

                if (order == null)
                {
                    return false;
                }

                if (order.Status != InboundOrderStatus.Pending)
                {
                    return false;
                }

                // 更新订单状态
                order.Status = InboundOrderStatus.InProgress;
                order.ProcessedBy = processedBy;
                order.ActualDate = DateTime.UtcNow;
                order.UpdatedAt = DateTime.UtcNow;

                // 处理每个订单项目
                foreach (var item in order.Items!)
                {
                    var processItem = request.Items.FirstOrDefault(i => i.ItemId == item.Id);
                    if (processItem != null)
                    {
                        item.ActualQuantity = processItem.ActualQuantity;
                        item.BatchNumber = processItem.BatchNumber ?? item.BatchNumber;
                        item.LpnCode = processItem.LpnCode ?? item.LpnCode;
                        item.ExpiryDate = processItem.ExpiryDate ?? item.ExpiryDate;
                        item.Remarks = processItem.Remarks ?? item.Remarks;
                        item.UpdatedAt = DateTime.UtcNow;

                        // 创建库存事务
                        if (processItem.ActualQuantity > 0)
                        {
                            var transaction = await _transactionService.CreateTransactionAsync(
                                TransactionType.Inbound,
                                processItem.StorageLocationId,
                                item.MaterialId,
                                processItem.ActualQuantity,
                                processedBy,
                                inboundOrderId: orderId,
                                batchNumber: item.BatchNumber,
                                lpnCode: item.LpnCode,
                                uniqueCode: processItem.UniqueCode,
                                expiryDate: item.ExpiryDate,
                                remarks: $"入库订单 {order.OrderNumber} 处理"
                            );

                            // 执行库存事务
                            await _transactionService.ExecuteTransactionAsync(
                                transaction.Id,
                                processedBy
                            );
                        }
                    }
                }

                // 检查是否所有项目都已处理完成
                var allItemsProcessed = order.Items.All(i => i.ActualQuantity.HasValue);
                if (allItemsProcessed)
                {
                    order.Status = InboundOrderStatus.Completed;
                }

                await _context.SaveChangesAsync();
                await dbTransaction.CommitAsync();

                _logger.LogInformation(
                    "Successfully processed inbound order {OrderNumber}",
                    order.OrderNumber
                );

                return true;
            }
            catch (Exception ex)
            {
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error processing inbound order {OrderId}", orderId);
                throw;
            }
        }

        public async Task<bool> ProcessItemWithBarcodeAsync(
            int itemId,
            BarcodeInboundRequest request,
            string processedBy
        )
        {
            using var dbTransaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var item = await _context
                    .InboundOrderItems.Include(i => i.InboundOrder)
                    .Include(i => i.Material)
                    .FirstOrDefaultAsync(i => i.Id == itemId);

                if (item == null)
                {
                    return false;
                }

                if (item.InboundOrder!.Status == InboundOrderStatus.Completed)
                {
                    return false;
                }

                // 解析条码
                var barcodeContext = new DTOs.Barcode.BarcodeContext
                {
                    OperationType = "INBOUND",
                    StorageLocationCode = request.StorageLocationCode,
                    UserId = processedBy,
                };

                var barcodeResult = await _barcodeService.ParseBarcodeAsync(
                    request.BarcodeData,
                    barcodeContext
                );

                if (!barcodeResult.IsValid)
                {
                    _logger.LogWarning(
                        "Barcode parsing failed for item {ItemId}: {Errors}",
                        itemId,
                        string.Join(", ", barcodeResult.ValidationErrors)
                    );
                    return false;
                }

                // 验证物料匹配
                if (
                    !string.IsNullOrEmpty(barcodeResult.MaterialSku)
                    && barcodeResult.MaterialSku != item.Material!.Sku
                )
                {
                    _logger.LogWarning(
                        "Material mismatch for item {ItemId}: expected {ExpectedSku}, got {ActualSku}",
                        itemId,
                        item.Material.Sku,
                        barcodeResult.MaterialSku
                    );
                    return false;
                }

                // 获取储位信息
                var storageLocation = await _context.StorageLocations.FirstOrDefaultAsync(s =>
                    s.Code == request.StorageLocationCode
                );

                if (storageLocation == null)
                {
                    _logger.LogWarning(
                        "Storage location {LocationCode} not found",
                        request.StorageLocationCode
                    );
                    return false;
                }

                // 更新订单项目
                var actualQuantity = barcodeResult.Quantity ?? request.ActualQuantity;
                item.ActualQuantity = actualQuantity;
                item.BatchNumber = barcodeResult.BatchNumber ?? item.BatchNumber;
                item.LpnCode = barcodeResult.LpnCode ?? item.LpnCode;
                item.ExpiryDate = item.ExpiryDate; // ExpiryDate is not in BarcodeParsingResult, keep original
                item.UpdatedAt = DateTime.UtcNow;

                // 更新订单状态
                if (item.InboundOrder.Status == InboundOrderStatus.Pending)
                {
                    item.InboundOrder.Status = InboundOrderStatus.InProgress;
                    item.InboundOrder.ProcessedBy = processedBy;
                    item.InboundOrder.ActualDate = DateTime.UtcNow;
                    item.InboundOrder.UpdatedAt = DateTime.UtcNow;
                }

                // 创建库存事务
                if (actualQuantity > 0)
                {
                    var transaction = await _transactionService.CreateTransactionAsync(
                        TransactionType.Inbound,
                        storageLocation.Id,
                        item.MaterialId,
                        actualQuantity,
                        processedBy,
                        inboundOrderId: item.InboundOrderId,
                        batchNumber: item.BatchNumber,
                        lpnCode: item.LpnCode,
                        uniqueCode: barcodeResult.UniqueCode,
                        expiryDate: item.ExpiryDate,
                        remarks: $"条码入库: {request.BarcodeData}"
                    );

                    transaction.BarcodeData = request.BarcodeData;
                    transaction.ParsedBy = processedBy;
                    transaction.ParsedAt = DateTime.UtcNow;

                    // 执行库存事务
                    await _transactionService.ExecuteTransactionAsync(transaction.Id, processedBy);
                }

                // 检查订单是否完成
                var orderItems = await _context
                    .InboundOrderItems.Where(i => i.InboundOrderId == item.InboundOrderId)
                    .ToListAsync();

                if (orderItems.All(i => i.ActualQuantity.HasValue))
                {
                    item.InboundOrder.Status = InboundOrderStatus.Completed;
                }

                await _context.SaveChangesAsync();
                await dbTransaction.CommitAsync();

                _logger.LogInformation(
                    "Successfully processed barcode inbound for item {ItemId}, barcode: {BarcodeData}",
                    itemId,
                    request.BarcodeData
                );

                return true;
            }
            catch (Exception ex)
            {
                await dbTransaction.RollbackAsync();
                _logger.LogError(ex, "Error processing barcode inbound for item {ItemId}", itemId);
                throw;
            }
        }

        public async Task<IEnumerable<InboundOrder>> GetInboundOrdersAsync(
            InboundOrderStatus? status = null
        )
        {
            var query = _context
                .InboundOrders.Include(o => o.Items)
                .ThenInclude(i => i.Material)
                .AsQueryable();

            if (status.HasValue)
            {
                query = query.Where(o => o.Status == status.Value);
            }

            return await query.OrderByDescending(o => o.CreatedAt).ToListAsync();
        }

        public async Task<InboundOrder?> GetInboundOrderByIdAsync(int id)
        {
            return await _context
                .InboundOrders.Include(o => o.Items)
                .ThenInclude(i => i.Material)
                .Include(o => o.Transactions)
                .FirstOrDefaultAsync(o => o.Id == id);
        }

        public async Task<InboundOrder?> GetInboundOrderByNumberAsync(string orderNumber)
        {
            return await _context
                .InboundOrders.Include(o => o.Items)
                .ThenInclude(i => i.Material)
                .Include(o => o.Transactions)
                .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber);
        }

        public async Task<bool> UpdateInboundOrderStatusAsync(
            int orderId,
            InboundOrderStatus status,
            string? processedBy = null
        )
        {
            try
            {
                var order = await _context.InboundOrders.FindAsync(orderId);
                if (order == null)
                {
                    return false;
                }

                order.Status = status;
                order.UpdatedAt = DateTime.UtcNow;

                if (!string.IsNullOrEmpty(processedBy))
                {
                    order.ProcessedBy = processedBy;
                }

                if (status == InboundOrderStatus.InProgress && order.ActualDate == null)
                {
                    order.ActualDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Updated inbound order {OrderNumber} status to {Status}",
                    order.OrderNumber,
                    status
                );

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating inbound order {OrderId} status", orderId);
                throw;
            }
        }

        public async Task<bool> CancelInboundOrderAsync(
            int orderId,
            string reason,
            string cancelledBy
        )
        {
            try
            {
                var order = await _context
                    .InboundOrders.Include(o => o.Transactions)
                    .FirstOrDefaultAsync(o => o.Id == orderId);

                if (order == null)
                {
                    return false;
                }

                if (order.Status == InboundOrderStatus.Completed)
                {
                    throw new InvalidOperationException("Cannot cancel completed order");
                }

                // 取消相关的事务
                if (order.Transactions != null)
                {
                    foreach (
                        var transaction in order.Transactions.Where(t =>
                            t.Status == TransactionStatus.Pending
                        )
                    )
                    {
                        await _transactionService.CancelTransactionAsync(transaction.Id, reason);
                    }
                }

                order.Status = InboundOrderStatus.Cancelled;
                order.Remarks = string.IsNullOrEmpty(order.Remarks)
                    ? $"取消原因: {reason}"
                    : $"{order.Remarks}\n取消原因: {reason}";
                order.ProcessedBy = cancelledBy;
                order.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Cancelled inbound order {OrderNumber}, reason: {Reason}",
                    order.OrderNumber,
                    reason
                );

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling inbound order {OrderId}", orderId);
                throw;
            }
        }

        public async Task<string> GenerateInboundOrderNumberAsync()
        {
            var prefix = "IB";
            var date = DateTime.UtcNow.ToString("yyyyMMdd");
            var sequence = await GetNextSequenceAsync(prefix, date);

            return $"{prefix}{date}{sequence:D4}";
        }

        private async Task<int> GetNextSequenceAsync(string prefix, string date)
        {
            var pattern = $"{prefix}{date}%";
            var lastOrder = await _context
                .InboundOrders.Where(o => EF.Functions.Like(o.OrderNumber, pattern))
                .OrderByDescending(o => o.OrderNumber)
                .FirstOrDefaultAsync();

            if (lastOrder == null)
            {
                return 1;
            }

            var lastSequence = lastOrder.OrderNumber.Substring(prefix.Length + date.Length);
            if (int.TryParse(lastSequence, out var sequence))
            {
                return sequence + 1;
            }

            return 1;
        }
    }

    // DTOs
    public class InboundOrderCreateRequest
    {
        public string? SupplierName { get; set; }
        public string? SupplierCode { get; set; }
        public DateTime? ExpectedDate { get; set; }
        public string? Remarks { get; set; }
        public IEnumerable<InboundOrderItemRequest>? Items { get; set; }
    }

    public class InboundOrderItemRequest
    {
        public int MaterialId { get; set; }
        public decimal ExpectedQuantity { get; set; }
        public string? BatchNumber { get; set; }
        public string? LpnCode { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string? Remarks { get; set; }
    }

    public class InboundProcessRequest
    {
        public IEnumerable<InboundProcessItemRequest> Items { get; set; } =
            new List<InboundProcessItemRequest>();
    }

    public class InboundProcessItemRequest
    {
        public int ItemId { get; set; }
        public int StorageLocationId { get; set; }
        public decimal ActualQuantity { get; set; }
        public string? BatchNumber { get; set; }
        public string? LpnCode { get; set; }
        public string? UniqueCode { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string? Remarks { get; set; }
    }

    public class BarcodeInboundRequest
    {
        public string BarcodeData { get; set; } = string.Empty;
        public string StorageLocationCode { get; set; } = string.Empty;
        public decimal ActualQuantity { get; set; }
    }
}
