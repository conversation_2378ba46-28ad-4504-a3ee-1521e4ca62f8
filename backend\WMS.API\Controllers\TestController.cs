using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<TestController> _logger;

        public TestController(ApplicationDbContext context, ILogger<TestController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpPost("create-test-data")]
        public async Task<ActionResult<object>> CreateTestData()
        {
            try
            {
                // 注释掉旧的测试数据代码，等待迁移完成后重新实现
                return Ok(new { message = "Test data creation temporarily disabled during migration to hybrid capability model. Please create data through proper hierarchy: Warehouse -> Zone -> Shelf -> StorageLocation" });

                /*
                // 创建ESP32控制器
                var controller = new ESP32Controller
                {
                    Name = "Warehouse Main Controller",
                    IpAddress = "*************",
                    Port = 80,
                    Description = "Main LED controller for warehouse shelves",
                    MdnsName = "wms-shelf-01",
                };

                _context.ESP32Controllers.Add(controller);
                await _context.SaveChangesAsync();

                // TODO: 重新实现测试数据创建，使用新的层级结构
                // 1. 创建 Warehouse
                // 2. 创建 Zone
                // 3. 创建 Shelf
                // 4. 创建 StorageLocation
                // 5. 添加 Capabilities 和 Classifications
                */
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test data");
                return BadRequest(
                    new { Message = "Failed to create test data", Error = ex.Message }
                );
            }
        }

        [HttpDelete("clear-all-data")]
        public async Task<ActionResult<object>> ClearAllData()
        {
            try
            {
                // 清空所有数据
                _context.Inventories.RemoveRange(_context.Inventories);
                _context.StorageLocations.RemoveRange(_context.StorageLocations);
                _context.ESP32Controllers.RemoveRange(_context.ESP32Controllers);
                _context.Materials.RemoveRange(_context.Materials);

                await _context.SaveChangesAsync();

                return Ok(new { Message = "All data cleared successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing data");
                return BadRequest(new { Message = "Failed to clear data", Error = ex.Message });
            }
        }
    }
}
