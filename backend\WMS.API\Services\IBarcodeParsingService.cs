using WMS.API.DTOs.Barcode;
using WMS.API.Models;

namespace WMS.API.Services
{
    /// <summary>
    /// 条码解析服务接口
    /// </summary>
    public interface IBarcodeParsingService
    {
        /// <summary>
        /// 解析条码
        /// </summary>
        /// <param name="barcode">条码字符串</param>
        /// <param name="context">解析上下文</param>
        /// <returns>解析结果</returns>
        Task<BarcodeParsingResult> ParseBarcodeAsync(string barcode, BarcodeContext context);

        /// <summary>
        /// 解析条码并进行验证
        /// </summary>
        /// <param name="barcode">条码字符串</param>
        /// <param name="context">解析上下文</param>
        /// <returns>解析和验证结果</returns>
        Task<BarcodeParsingResult> ParseAndValidateAsync(string barcode, BarcodeContext context);

        /// <summary>
        /// 获取活跃的解析规则
        /// </summary>
        /// <param name="clientId">客户ID（可选）</param>
        /// <returns>解析规则列表</returns>
        Task<List<BarcodeParsingRule>> GetActiveRulesAsync(string? clientId = null);

        /// <summary>
        /// 按分类获取解析规则
        /// </summary>
        /// <param name="categoryCode">分类代码</param>
        /// <param name="clientId">客户ID（可选）</param>
        /// <returns>解析规则列表</returns>
        Task<List<BarcodeParsingRule>> GetRulesByCategoryAsync(
            string categoryCode,
            string? clientId = null
        );

        /// <summary>
        /// 验证唯一码
        /// </summary>
        /// <param name="uniqueCode">唯一码</param>
        /// <param name="materialId">物料ID</param>
        /// <returns>是否有效</returns>
        Task<bool> ValidateUniqueCodeAsync(string uniqueCode, int materialId);

        /// <summary>
        /// 批量解析条码
        /// </summary>
        /// <param name="barcodes">条码列表</param>
        /// <param name="context">解析上下文</param>
        /// <param name="continueOnError">是否在错误时继续处理</param>
        /// <returns>批量解析结果</returns>
        Task<BatchBarcodeParsingResult> ParseBarcodesAsync(
            List<string> barcodes, 
            BarcodeContext context, 
            bool continueOnError = true
        );

        /// <summary>
        /// 注册唯一码
        /// </summary>
        /// <param name="uniqueCode">唯一码</param>
        /// <param name="materialId">物料ID</param>
        /// <param name="storageLocationId">储位ID（可选）</param>
        /// <param name="userId">用户ID</param>
        /// <returns>注册结果</returns>
        Task<bool> RegisterUniqueCodeAsync(
            string uniqueCode,
            int materialId,
            int? storageLocationId = null,
            string? userId = null
        );
    }
}
