using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMS.API.Models
{
    /// <summary>
    /// 配置应用日志表
    /// </summary>
    public class ConfigurationApplicationLog
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 关联具体的业务操作ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OperationId { get; set; } = string.Empty;

        /// <summary>
        /// 客户ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 区域代码
        /// </summary>
        [StringLength(20)]
        public string? ZoneCode { get; set; }

        /// <summary>
        /// 货架代码
        /// </summary>
        [StringLength(50)]
        public string? ShelfCode { get; set; }

        /// <summary>
        /// 储位代码
        /// </summary>
        [StringLength(100)]
        public string? LocationCode { get; set; }

        /// <summary>
        /// 物料类别
        /// </summary>
        [StringLength(50)]
        public string? MaterialCategory { get; set; }

        /// <summary>
        /// 配置键
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ConfigKey { get; set; } = string.Empty;

        /// <summary>
        /// 最终生效的配置层级
        /// </summary>
        [StringLength(20)]
        public string? AppliedConfigLevel { get; set; }

        /// <summary>
        /// 应用的配置ID
        /// </summary>
        public int? AppliedConfigId { get; set; }

        /// <summary>
        /// 关联的配置实体
        /// </summary>
        public virtual HierarchicalConfiguration? AppliedConfig { get; set; }

        /// <summary>
        /// 最终生效的配置值（JSON格式）
        /// </summary>
        [Required]
        [Column(TypeName = "jsonb")]
        public string FinalConfigValue { get; set; } = "{}";

        /// <summary>
        /// 配置解析路径，记录覆盖过程
        /// </summary>
        [Column(TypeName = "text")]
        public string? ConfigResolutionPath { get; set; }

        /// <summary>
        /// 应用时间
        /// </summary>
        public DateTime AppliedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 用户ID
        /// </summary>
        [StringLength(100)]
        public string? UserId { get; set; }
    }
}
