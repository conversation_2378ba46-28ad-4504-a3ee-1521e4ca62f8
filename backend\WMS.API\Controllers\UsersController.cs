using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Constants;
using WMS.API.Models;

namespace WMS.API.Controllers
{
    /// <summary>
    /// 用户管理控制器
    /// 提供用户的增删改查功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = PolicyConstants.UserManagementPolicy)]
    public class UsersController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ILogger<UsersController> _logger;

        public UsersController(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ILogger<UsersController> logger
        )
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有用户列表
        /// </summary>
        /// <returns>用户列表</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<UserListItem>>> GetUsers()
        {
            try
            {
                var users = await _userManager.Users.OrderBy(u => u.FullName).ToListAsync();

                var userList = new List<UserListItem>();

                foreach (var user in users)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    userList.Add(
                        new UserListItem
                        {
                            Id = user.Id,
                            Email = user.Email!,
                            FullName = user.FullName,
                            EmployeeId = user.EmployeeId,
                            Department = user.Department,
                            Position = user.Position,
                            IsActive = user.IsActive,
                            LastLoginAt = user.LastLoginAt,
                            CreatedAt = user.CreatedAt,
                            Roles = roles.ToList(),
                        }
                    );
                }

                return Ok(userList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表失败");
                return StatusCode(500, new { Message = "获取用户列表失败" });
            }
        }

        /// <summary>
        /// 根据ID获取用户详细信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户详细信息</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<UserDetailInfo>> GetUser(string id)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在" });
                }

                var roles = await _userManager.GetRolesAsync(user);

                return Ok(
                    new UserDetailInfo
                    {
                        Id = user.Id,
                        Email = user.Email!,
                        FullName = user.FullName,
                        EmployeeId = user.EmployeeId,
                        Department = user.Department,
                        Position = user.Position,
                        IsActive = user.IsActive,
                        LastLoginAt = user.LastLoginAt,
                        CreatedAt = user.CreatedAt,
                        UpdatedAt = user.UpdatedAt,
                        Remarks = user.Remarks,
                        Roles = roles.ToList(),
                    }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息失败 {UserId}", id);
                return StatusCode(500, new { Message = "获取用户信息失败" });
            }
        }

        /// <summary>
        /// 创建新用户
        /// </summary>
        /// <param name="request">创建用户请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<ActionResult<UserDetailInfo>> CreateUser(
            [FromBody] CreateUserRequest request
        )
        {
            try
            {
                // 检查邮箱是否已存在
                var existingUser = await _userManager.FindByEmailAsync(request.Email);
                if (existingUser != null)
                {
                    return BadRequest(new { Message = "邮箱已存在" });
                }

                // 检查员工工号是否已存在（如果提供了的话）
                if (!string.IsNullOrEmpty(request.EmployeeId))
                {
                    var existingEmployee = await _userManager.Users.FirstOrDefaultAsync(u =>
                        u.EmployeeId == request.EmployeeId
                    );
                    if (existingEmployee != null)
                    {
                        return BadRequest(new { Message = "员工工号已存在" });
                    }
                }

                var currentUser = await _userManager.GetUserAsync(User);

                var user = new ApplicationUser
                {
                    UserName = request.Email,
                    Email = request.Email,
                    FullName = request.FullName,
                    EmployeeId = request.EmployeeId,
                    Department = request.Department,
                    Position = request.Position,
                    EmailConfirmed = true,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedById = currentUser?.Id,
                    Remarks = request.Remarks,
                };

                var result = await _userManager.CreateAsync(user, request.Password);

                if (result.Succeeded)
                {
                    // 分配角色
                    if (request.Roles != null && request.Roles.Any())
                    {
                        foreach (var role in request.Roles)
                        {
                            if (RoleConstants.AllRoles.Contains(role))
                            {
                                await _userManager.AddToRoleAsync(user, role);
                            }
                        }
                    }

                    var roles = await _userManager.GetRolesAsync(user);

                    _logger.LogInformation(
                        "用户创建成功 {Email} by {CreatedBy}",
                        request.Email,
                        currentUser?.Email
                    );

                    return CreatedAtAction(
                        nameof(GetUser),
                        new { id = user.Id },
                        new UserDetailInfo
                        {
                            Id = user.Id,
                            Email = user.Email,
                            FullName = user.FullName,
                            EmployeeId = user.EmployeeId,
                            Department = user.Department,
                            Position = user.Position,
                            IsActive = user.IsActive,
                            CreatedAt = user.CreatedAt,
                            UpdatedAt = user.UpdatedAt,
                            Remarks = user.Remarks,
                            Roles = roles.ToList(),
                        }
                    );
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("用户创建失败 {Email}: {Errors}", request.Email, errors);
                    return BadRequest(
                        new
                        {
                            Message = "用户创建失败",
                            Errors = result.Errors.Select(e => e.Description),
                        }
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户过程中发生错误");
                return StatusCode(500, new { Message = "创建用户失败" });
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <param name="request">更新用户请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<UserDetailInfo>> UpdateUser(
            string id,
            [FromBody] UpdateUserRequest request
        )
        {
            try
            {
                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在" });
                }

                // 检查邮箱是否已被其他用户使用
                if (user.Email != request.Email)
                {
                    var existingUser = await _userManager.FindByEmailAsync(request.Email);
                    if (existingUser != null)
                    {
                        return BadRequest(new { Message = "邮箱已被其他用户使用" });
                    }
                }

                // 检查员工工号是否已被其他用户使用
                if (
                    !string.IsNullOrEmpty(request.EmployeeId)
                    && user.EmployeeId != request.EmployeeId
                )
                {
                    var existingEmployee = await _userManager.Users.FirstOrDefaultAsync(u =>
                        u.EmployeeId == request.EmployeeId
                    );
                    if (existingEmployee != null)
                    {
                        return BadRequest(new { Message = "员工工号已被其他用户使用" });
                    }
                }

                // 更新用户信息
                user.Email = request.Email;
                user.UserName = request.Email;
                user.FullName = request.FullName;
                user.EmployeeId = request.EmployeeId;
                user.Department = request.Department;
                user.Position = request.Position;
                user.IsActive = request.IsActive;
                user.UpdatedAt = DateTime.UtcNow;
                user.Remarks = request.Remarks;

                var updateResult = await _userManager.UpdateAsync(user);

                if (updateResult.Succeeded)
                {
                    // 更新角色
                    var currentRoles = await _userManager.GetRolesAsync(user);
                    var rolesToRemove = currentRoles
                        .Where(r => !request.Roles.Contains(r))
                        .ToList();
                    var rolesToAdd = request
                        .Roles.Where(r =>
                            !currentRoles.Contains(r) && RoleConstants.AllRoles.Contains(r)
                        )
                        .ToList();

                    if (rolesToRemove.Any())
                    {
                        await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                    }

                    if (rolesToAdd.Any())
                    {
                        await _userManager.AddToRolesAsync(user, rolesToAdd);
                    }

                    var updatedRoles = await _userManager.GetRolesAsync(user);

                    _logger.LogInformation("用户信息更新成功 {UserId}", id);

                    return Ok(
                        new UserDetailInfo
                        {
                            Id = user.Id,
                            Email = user.Email,
                            FullName = user.FullName,
                            EmployeeId = user.EmployeeId,
                            Department = user.Department,
                            Position = user.Position,
                            IsActive = user.IsActive,
                            LastLoginAt = user.LastLoginAt,
                            CreatedAt = user.CreatedAt,
                            UpdatedAt = user.UpdatedAt,
                            Remarks = user.Remarks,
                            Roles = updatedRoles.ToList(),
                        }
                    );
                }
                else
                {
                    var errors = string.Join(", ", updateResult.Errors.Select(e => e.Description));
                    _logger.LogWarning("用户信息更新失败 {UserId}: {Errors}", id, errors);
                    return BadRequest(
                        new
                        {
                            Message = "用户信息更新失败",
                            Errors = updateResult.Errors.Select(e => e.Description),
                        }
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户信息过程中发生错误 {UserId}", id);
                return StatusCode(500, new { Message = "更新用户信息失败" });
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        [Authorize(Policy = PolicyConstants.SuperAdminPolicy)]
        public async Task<ActionResult> DeleteUser(string id)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在" });
                }

                // 防止删除自己
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser?.Id == id)
                {
                    return BadRequest(new { Message = "不能删除自己的账户" });
                }

                var result = await _userManager.DeleteAsync(user);

                if (result.Succeeded)
                {
                    _logger.LogInformation(
                        "用户删除成功 {UserId} by {DeletedBy}",
                        id,
                        currentUser?.Email
                    );
                    return Ok(new { Message = "用户删除成功" });
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("用户删除失败 {UserId}: {Errors}", id, errors);
                    return BadRequest(
                        new
                        {
                            Message = "用户删除失败",
                            Errors = result.Errors.Select(e => e.Description),
                        }
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户过程中发生错误 {UserId}", id);
                return StatusCode(500, new { Message = "删除用户失败" });
            }
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <param name="request">重置密码请求</param>
        /// <returns>重置结果</returns>
        [HttpPost("{id}/reset-password")]
        public async Task<ActionResult> ResetPassword(
            string id,
            [FromBody] ResetPasswordRequest request
        )
        {
            try
            {
                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { Message = "用户不存在" });
                }

                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                var result = await _userManager.ResetPasswordAsync(
                    user,
                    token,
                    request.NewPassword
                );

                if (result.Succeeded)
                {
                    _logger.LogInformation("用户密码重置成功 {UserId}", id);
                    return Ok(new { Message = "密码重置成功" });
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("用户密码重置失败 {UserId}: {Errors}", id, errors);
                    return BadRequest(
                        new
                        {
                            Message = "密码重置失败",
                            Errors = result.Errors.Select(e => e.Description),
                        }
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置用户密码过程中发生错误 {UserId}", id);
                return StatusCode(500, new { Message = "重置密码失败" });
            }
        }

        /// <summary>
        /// 获取系统所有角色列表
        /// </summary>
        /// <returns>角色列表</returns>
        [HttpGet("roles")]
        public ActionResult<IEnumerable<string>> GetRoles()
        {
            return Ok(RoleConstants.AllRoles);
        }
    }

    #region DTO Classes

    /// <summary>
    /// 用户列表项
    /// </summary>
    public class UserListItem
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? EmployeeId { get; set; }
        public string? Department { get; set; }
        public string? Position { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<string> Roles { get; set; } = new();
    }

    /// <summary>
    /// 用户详细信息
    /// </summary>
    public class UserDetailInfo : UserListItem
    {
        public DateTime UpdatedAt { get; set; }
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 创建用户请求
    /// </summary>
    public class CreateUserRequest
    {
        [Required(ErrorMessage = "邮箱不能为空")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(100, ErrorMessage = "姓名长度不能超过100字符")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "员工工号长度不能超过50字符")]
        public string? EmployeeId { get; set; }

        [StringLength(100, ErrorMessage = "部门长度不能超过100字符")]
        public string? Department { get; set; }

        [StringLength(100, ErrorMessage = "职位长度不能超过100字符")]
        public string? Position { get; set; }

        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "密码长度必须在8-100字符之间")]
        public string Password { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "备注长度不能超过500字符")]
        public string? Remarks { get; set; }

        public List<string> Roles { get; set; } = new();
    }

    /// <summary>
    /// 更新用户请求
    /// </summary>
    public class UpdateUserRequest
    {
        [Required(ErrorMessage = "邮箱不能为空")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(100, ErrorMessage = "姓名长度不能超过100字符")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "员工工号长度不能超过50字符")]
        public string? EmployeeId { get; set; }

        [StringLength(100, ErrorMessage = "部门长度不能超过100字符")]
        public string? Department { get; set; }

        [StringLength(100, ErrorMessage = "职位长度不能超过100字符")]
        public string? Position { get; set; }

        public bool IsActive { get; set; } = true;

        [StringLength(500, ErrorMessage = "备注长度不能超过500字符")]
        public string? Remarks { get; set; }

        public List<string> Roles { get; set; } = new();
    }

    /// <summary>
    /// 重置密码请求
    /// </summary>
    public class ResetPasswordRequest
    {
        [Required(ErrorMessage = "新密码不能为空")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "密码长度必须在8-100字符之间")]
        public string NewPassword { get; set; } = string.Empty;
    }

    #endregion
}
