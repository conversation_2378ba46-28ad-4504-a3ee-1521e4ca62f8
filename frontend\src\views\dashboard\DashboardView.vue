<template>
  <div class="dashboard-page">
    <div class="page-header">
      <h1 class="page-title">系统概览</h1>
      <p class="page-subtitle">欢迎使用 WMS-VGL 智能仓储管理系统</p>
    </div>
    
    <div class="stats-grid">
      <a-card v-for="stat in stats" :key="stat.title" class="stat-card">
        <div class="stat-content">
          <div class="stat-icon" :class="stat.color">
            <component :is="stat.icon" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-trend" :class="stat.trend.type">
              <component :is="stat.trend.icon" />
              {{ stat.trend.value }}
            </div>
          </div>
        </div>
      </a-card>
    </div>
    
    <!-- 系统架构概览 -->
    <a-card title="系统架构概览" class="hierarchy-card">
      <div class="hierarchy-tree">
        <div class="tree-item">
          <HomeOutlined class="tree-icon" />
          <strong>主仓库 (WH001)</strong>
        </div>
        <div class="tree-node">
          <div class="tree-item">
            <AppstoreOutlined class="tree-icon" />
            A区域 (常温区)
          </div>
          <div class="tree-node">
            <div class="tree-item">
              <DatabaseOutlined class="tree-icon" />
              货架A01 (80个储位)
            </div>
            <div class="tree-item">
              <DatabaseOutlined class="tree-icon" />
              货架A02 (80个储位)
            </div>
          </div>
          <div class="tree-item">
            <AppstoreOutlined class="tree-icon" />
            B区域 (冷藏区)
          </div>
        </div>
      </div>
    </a-card>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="quick-action-card" @click="navigateTo('/warehouse')">
        <HomeOutlined class="quick-action-icon" />
        <div class="quick-action-title">仓库管理</div>
        <div class="quick-action-desc">管理仓库层级结构和配置</div>
      </div>
      <div class="quick-action-card" @click="navigateTo('/inventory')">
        <InboxOutlined class="quick-action-icon" />
        <div class="quick-action-title">库存查询</div>
        <div class="quick-action-desc">实时查看库存状态</div>
      </div>
      <div class="quick-action-card" @click="navigateTo('/operations/inbound')">
        <ImportOutlined class="quick-action-icon" />
        <div class="quick-action-title">入库作业</div>
        <div class="quick-action-desc">执行入库操作</div>
      </div>
    </div>

    <a-row :gutter="24" class="chart-row">
      <a-col :span="16">
        <a-card title="库存统计" class="chart-card">
          <div class="chart-placeholder">
            <BarChartOutlined />
            <p>库存统计图表</p>
            <p class="chart-desc">显示各区域库存分布情况</p>
          </div>
        </a-card>
      </a-col>

      <a-col :span="8">
        <a-card title="设备状态" class="chart-card">
          <div class="device-status">
            <div class="device-item" v-for="device in deviceStatus" :key="device.name">
              <div class="device-info">
                <span class="device-name">{{ device.name }}</span>
                <a-tag :color="device.status === 'online' ? 'green' : 'red'">
                  {{ device.status === 'online' ? '在线' : '离线' }}
                </a-tag>
              </div>
              <div class="device-progress">
                <a-progress
                  :percent="device.health"
                  :stroke-color="device.health > 80 ? '#52c41a' : device.health > 60 ? '#faad14' : '#f5222d'"
                  size="small"
                />
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="24" class="table-row">
      <a-col :span="12">
        <a-card title="最近操作" class="table-card">
          <a-table
            :columns="activityColumns"
            :data-source="recentActivities"
            :pagination="false"
            size="small"
          />
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="库存预警" class="table-card">
          <a-table
            :columns="alertColumns"
            :data-source="inventoryAlerts"
            :pagination="false"
            size="small"
          />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  InboxOutlined,
  ShopOutlined,
  SettingOutlined,
  TeamOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  BarChartOutlined,
  HomeOutlined,
  AppstoreOutlined,
  DatabaseOutlined,
  ImportOutlined,
} from '@ant-design/icons-vue'

const router = useRouter()

const stats = ref([
  {
    title: '总储位数',
    value: '1,248',
    icon: InboxOutlined,
    color: 'blue',
    trend: {
      type: 'up',
      value: '+12%',
      icon: ArrowUpOutlined,
    },
  },
  {
    title: '活跃设备',
    value: '24',
    icon: SettingOutlined,
    color: 'green',
    trend: {
      type: 'up',
      value: '+2',
      icon: ArrowUpOutlined,
    },
  },
  {
    title: '在线用户',
    value: '8',
    icon: TeamOutlined,
    color: 'orange',
    trend: {
      type: 'down',
      value: '-1',
      icon: ArrowDownOutlined,
    },
  },
  {
    title: '今日作业',
    value: '156',
    icon: ShopOutlined,
    color: 'purple',
    trend: {
      type: 'up',
      value: '+28%',
      icon: ArrowUpOutlined,
    },
  },
])

const deviceStatus = ref([
  { name: 'ESP32-001', status: 'online', health: 98 },
  { name: 'ESP32-002', status: 'online', health: 85 },
  { name: 'ESP32-003', status: 'online', health: 92 },
  { name: 'ESP32-004', status: 'offline', health: 0 },
  { name: 'ESP32-005', status: 'online', health: 76 },
])

const activityColumns = [
  { title: '时间', dataIndex: 'time', key: 'time', width: 80 },
  { title: '用户', dataIndex: 'user', key: 'user', width: 80 },
  { title: '操作', dataIndex: 'action', key: 'action' },
]

const recentActivities = ref([
  { key: '1', time: '15:30', user: '张三', action: '物料MAT001入库至A01-001' },
  { key: '2', time: '15:28', user: '李四', action: '物料MAT002从A01-002出库' },
  { key: '3', time: '15:25', user: '王五', action: '执行移库作业' },
  { key: '4', time: '15:20', user: '赵六', action: '完成盘点作业' },
  { key: '5', time: '15:15', user: '张三', action: '更新储位配置' },
])

const alertColumns = [
  { title: '物料', dataIndex: 'material', key: 'material', width: 100 },
  { title: '储位', dataIndex: 'location', key: 'location', width: 80 },
  { title: '状态', dataIndex: 'status', key: 'status' },
]

const inventoryAlerts = ref([
  { key: '1', material: 'MAT001', location: 'A01-001', status: '库存不足' },
  { key: '2', material: 'MAT002', location: 'A01-005', status: '即将过期' },
  { key: '3', material: 'MAT003', location: 'B02-010', status: '超出容量' },
  { key: '4', material: 'MAT004', location: 'C01-003', status: '温度异常' },
  { key: '5', material: 'MAT005', location: 'A02-008', status: '位置冲突' },
])

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped lang="less">
.dashboard-page {
  .page-header {
    margin-bottom: 24px;
    
    .page-title {
      font-size: 28px;
      font-weight: 600;
      color: #262626;
      margin: 0;
    }
    
    .page-subtitle {
      color: #8c8c8c;
      margin: 8px 0 0 0;
      font-size: 16px;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    
    &.blue {
      background: #e6f7ff;
      color: #1890ff;
    }
    
    &.green {
      background: #f6ffed;
      color: #52c41a;
    }
    
    &.orange {
      background: #fff7e6;
      color: #fa8c16;
    }
    
    &.purple {
      background: #f9f0ff;
      color: #722ed1;
    }
  }
  
  .stat-info {
    flex: 1;
  }
  
  .stat-value {
    font-size: 32px;
    font-weight: 600;
    color: #262626;
    line-height: 1;
  }
  
  .stat-title {
    color: #8c8c8c;
    margin: 4px 0;
  }
  
  .stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    
    &.up {
      color: #52c41a;
    }
    
    &.down {
      color: #f5222d;
    }
  }
}

.hierarchy-card {
  margin-bottom: 24px;
}

.hierarchy-tree {
  .tree-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
    cursor: pointer;

    &:hover {
      background: #f5f5f5;
    }
  }

  .tree-icon {
    color: #1890ff;
    font-size: 16px;
  }

  .tree-node {
    margin-left: 20px;
    border-left: 1px dashed #d9d9d9;
    padding-left: 20px;
    margin-bottom: 8px;
  }
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.quick-action-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  border: 1px solid #f0f0f0;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }

  .quick-action-icon {
    font-size: 32px;
    color: #1890ff;
    margin-bottom: 16px;
  }

  .quick-action-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 8px;
  }

  .quick-action-desc {
    color: #8c8c8c;
    margin-bottom: 16px;
  }
}

.chart-row {
  margin-bottom: 24px;
}

.chart-card {
  .chart-placeholder {
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
    
    .anticon {
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    p {
      margin: 4px 0;
      font-size: 16px;
      
      &.chart-desc {
        font-size: 14px;
        color: #bfbfbf;
      }
    }
  }
}

.device-status {
  .device-item {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .device-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .device-name {
      font-weight: 500;
    }
  }
}

.table-row {
  .table-card {
    :deep(.ant-table-tbody tr:last-child td) {
      border-bottom: none;
    }
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-row,
  .table-row {
    :deep(.ant-col) {
      margin-bottom: 16px;
    }
  }
}
</style>