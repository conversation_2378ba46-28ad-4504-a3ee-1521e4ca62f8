# WMS-VGL 仓储管理系统

基于WS2812灯带的视觉引导仓储管理系统后端API

## 数据库配置

### PostgreSQL Docker 部署

数据库使用Docker容器部署，配置信息如下：

```yaml
# docker-compose.yml 配置
services:
  wms-postgres:
    image: postgres:15-alpine
    container_name: wms-postgres
    environment:
      POSTGRES_DB: wms_vgl_db
      POSTGRES_USER: wms_admin
      POSTGRES_PASSWORD: WMS@2025!Secure
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C.UTF-8 --lc-ctype=C.UTF-8"
      LC_ALL: C.UTF-8
      LANG: C.UTF-8
    ports:
      - "5434:5432"  # 映射到5434端口避免与本机PG冲突
```

### 连接信息

- **主机**: localhost
- **端口**: 5434
- **数据库名**: wms_vgl_db
- **用户名**: wms_admin
- **密码**: WMS@2025!Secure
- **编码**: UTF-8

### 启动数据库

```bash
# 启动PostgreSQL容器
docker-compose up -d wms-postgres

# 应用数据库迁移
cd backend/WMS.API
dotnet ef database update
```

## API 服务配置

### 启动API服务

```bash
cd backend/WMS.API
dotnet run --urls http://localhost:5000
```

### API文档

启动后访问: http://localhost:5000/swagger

## 已实现功能

### 1. ESP32控制器管理
- ✅ 控制器注册和管理（CRUD）
- ✅ 设备连接测试
- ✅ 设备信息获取
- ✅ 健康检查和状态监控

### 2. 储位管理
- ✅ 储位信息管理（代码、描述、位置信息）
- ✅ LED灯条位置绑定（起始位置、结束位置）
- ✅ 储位状态管理（可用、锁定、维护中）
- ✅ 灯光测试功能

### 3. 硬件通信
- ✅ ESP32 HTTP API 集成
- ✅ LED灯光控制（颜色、亮度、指定位置）
- ✅ 设备状态查询
- ✅ 后台健康检查服务

## 数据库表结构

### ESP32Controllers 表
| 字段 | 类型 | 说明 |
|------|------|------|
| Id | int | 主键 |
| Name | varchar(100) | 控制器名称 |
| IpAddress | varchar(50) | IP地址 |
| Port | int | 端口号 |
| Description | varchar(200) | 描述信息 |
| IsOnline | boolean | 在线状态 |
| LastHeartbeat | timestamp | 最后心跳时间 |
| MdnsName | varchar(100) | mDNS名称 |

### StorageLocations 表
| 字段 | 类型 | 说明 |
|------|------|------|
| Id | int | 主键 |
| Code | varchar(50) | 储位编码 |
| Description | varchar(200) | 储位描述 |
| ESP32ControllerId | int | 关联的控制器ID |
| StartLedPosition | int | LED起始位置 |
| EndLedPosition | int | LED结束位置 |
| Status | int | 储位状态（0:可用，1:锁定，2:维护中） |
| Zone | varchar(20) | 区域 |
| Aisle | varchar(20) | 巷道 |
| Shelf | varchar(20) | 货架 |
| Level | varchar(20) | 层级 |

## API端点示例

### ESP32控制器
```http
# 获取所有控制器
GET /api/ESP32Controllers

# 创建控制器
POST /api/ESP32Controllers
{
  "name": "仓库主控制器",
  "ipAddress": "*************",
  "port": 80,
  "description": "A区货架LED控制器"
}

# 测试连接
POST /api/ESP32Controllers/{id}/test-connection
```

### 储位管理
```http
# 获取所有储位
GET /api/StorageLocations

# 创建储位
POST /api/StorageLocations
{
  "code": "A01-01-01",
  "description": "A区1排1层1号储位",
  "esp32ControllerId": 1,
  "startLedPosition": 0,
  "endLedPosition": 9,
  "zone": "A",
  "aisle": "01",
  "shelf": "01",
  "level": "01"
}

# 储位灯光测试
POST /api/StorageLocations/{id}/test-light
{
  "color": "yellow",
  "brightness": 255
}
```

## ESP32硬件要求

### 已测试的ESP32设备
- **IP地址**: *************
- **设备名称**: QR-Shelf Server
- **LED数量**: 600个（最大支持1200个）
- **固件版本**: Jun 24 2025 09:25:55
- **支持API**: `/controlLEDsV2`, `/setAllLeds`, `/getDeviceParams`

### LED控制参数
- **通道数**: 4个通道（0-3）
- **颜色格式**: 十六进制（如: FF0000为红色）
- **亮度范围**: 0-255
- **位置范围**: 0-4095（每个通道）

## 开发注意事项

1. **编码支持**: 系统完全支持UTF-8编码，可以正常处理中文数据
2. **端口配置**: PostgreSQL使用5434端口避免与本机数据库冲突
3. **健康检查**: 后台服务每5分钟自动检查ESP32设备状态
4. **错误处理**: 所有ESP32通信都有超时和错误处理机制

## 下一步开发计划

- 物料管理功能
- 库存管理功能
- 入库/出库作业流程
- 批量拣货功能
- 库存盘点功能