﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WMS.API.Migrations
{
    /// <inheritdoc />
    public partial class RemoveIPAddressUniqueConstraint : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ESP32Controllers_IpAddress",
                table: "ESP32Controllers");

            migrationBuilder.AddColumn<int>(
                name: "LedChannel",
                table: "StorageLocations",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LedChan<PERSON>",
                table: "StorageLocations");

            migrationBuilder.CreateIndex(
                name: "IX_ESP32Controllers_IpAddress",
                table: "ESP32Controllers",
                column: "IpAddress",
                unique: true);
        }
    }
}
