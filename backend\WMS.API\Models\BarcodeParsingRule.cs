using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 条码解析规则
    /// </summary>
    public class BarcodeParsingRule
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 关联的分类ID
        /// </summary>
        public int CategoryId { get; set; }

        /// <summary>
        /// 关联的分类实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual BarcodeRuleCategory? Category { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string RuleName { get; set; } = string.Empty;

        /// <summary>
        /// 正则表达式模式
        /// </summary>
        [Required]
        [Column(TypeName = "text")]
        public string RegexPattern { get; set; } = string.Empty;

        /// <summary>
        /// 字段映射配置（JSON格式）
        /// </summary>
        [Required]
        [Column(TypeName = "jsonb")]
        public string FieldMappings { get; set; } = "{}";

        /// <summary>
        /// 优先级（数字越小优先级越高）
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 客户ID（可选，用于特定客户的规则）
        /// </summary>
        [StringLength(50)]
        public string? ClientId { get; set; }

        /// <summary>
        /// 规则描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }
    }
}
