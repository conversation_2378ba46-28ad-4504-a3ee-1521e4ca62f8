<template>
  <a-breadcrumb class="app-breadcrumb">
    <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.title">
      <router-link v-if="item.path" :to="item.path">
        {{ item.title }}
      </router-link>
      <span v-else>{{ item.title }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import type { BreadcrumbItem } from '@/types'

const route = useRoute()

const breadcrumbMap: Record<string, BreadcrumbItem[]> = {
  '/': [{ title: '首页' }],
  
  // Warehouse Management
  '/warehouse': [
    { title: '首页', path: '/' },
    { title: '仓库管理' },
    { title: '仓库列表' }
  ],
  '/warehouse/zones': [
    { title: '首页', path: '/' },
    { title: '仓库管理' },
    { title: '区域管理' }
  ],
  '/warehouse/shelves': [
    { title: '首页', path: '/' },
    { title: '仓库管理' },
    { title: '货架管理' }
  ],
  '/warehouse/storage-locations': [
    { title: '首页', path: '/' },
    { title: '仓库管理' },
    { title: '储位管理' }
  ],
  
  // Inventory Management
  '/inventory': [
    { title: '首页', path: '/' },
    { title: '库存管理' },
    { title: '库存查询' }
  ],
  '/inventory/statistics': [
    { title: '首页', path: '/' },
    { title: '库存管理' },
    { title: '库存统计' }
  ],
  '/inventory/alerts': [
    { title: '首页', path: '/' },
    { title: '库存管理' },
    { title: '库存预警' }
  ],
  
  // Operations Management
  '/operations': [
    { title: '首页', path: '/' },
    { title: '作业管理' },
    { title: '作业总览' }
  ],
  '/operations/inbound': [
    { title: '首页', path: '/' },
    { title: '作业管理' },
    { title: '入库作业' }
  ],
  '/operations/outbound': [
    { title: '首页', path: '/' },
    { title: '作业管理' },
    { title: '出库作业' }
  ],
  '/operations/transfer': [
    { title: '首页', path: '/' },
    { title: '作业管理' },
    { title: '移库作业' }
  ],
  '/operations/picking': [
    { title: '首页', path: '/' },
    { title: '作业管理' },
    { title: '拣货作业' }
  ],
  '/operations/stocktaking': [
    { title: '首页', path: '/' },
    { title: '作业管理' },
    { title: '盘点作业' }
  ],
  
  // Hardware Management
  '/hardware': [
    { title: '首页', path: '/' },
    { title: '硬件管理' },
    { title: 'ESP32设备' }
  ],
  '/hardware/monitoring': [
    { title: '首页', path: '/' },
    { title: '硬件管理' },
    { title: '设备监控' }
  ],
  '/hardware/led-control': [
    { title: '首页', path: '/' },
    { title: '硬件管理' },
    { title: 'LED控制' }
  ],
  
  // User Management
  '/users': [
    { title: '首页', path: '/' },
    { title: '用户管理' },
    { title: '用户管理' }
  ],
  '/users/roles': [
    { title: '首页', path: '/' },
    { title: '用户管理' },
    { title: '角色权限' }
  ],
  '/users/logs': [
    { title: '首页', path: '/' },
    { title: '用户管理' },
    { title: '操作日志' }
  ],
}

const breadcrumbItems = computed(() => {
  return breadcrumbMap[route.path] || [{ title: '页面未找到' }]
})
</script>

<style scoped lang="less">
.app-breadcrumb {
  margin-bottom: 16px;
  
  :deep(.ant-breadcrumb-link) {
    color: #1890ff;
    
    &:hover {
      color: #40a9ff;
    }
  }
  
  :deep(.ant-breadcrumb-separator) {
    color: #8c8c8c;
  }
}
</style>