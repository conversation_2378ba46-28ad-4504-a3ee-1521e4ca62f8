# 🔍 HTTP 400 错误调试指南

## 📋 问题排查步骤

### 1. 🌐 检查浏览器开发者工具

#### 打开开发者工具：
- **Chrome/Edge**: 按 `F12` 或右键 → 检查
- **Firefox**: 按 `F12` 或右键 → 检查元素

#### 查看Network标签页：
1. 刷新页面或执行操作
2. 找到失败的API请求（通常显示为红色）
3. 点击该请求查看详细信息

#### 需要记录的信息：
- **请求URL**: 完整的API地址
- **请求方法**: GET/POST/PUT/DELETE
- **状态码**: 400 Bad Request
- **请求头**: Content-Type, Authorization等
- **请求体**: 发送的JSON数据
- **响应头**: 服务器返回的头信息
- **响应体**: 错误消息详情

### 2. 📊 检查Console控制台

#### 查看JavaScript错误：
1. 打开Console标签页
2. 查找红色错误信息
3. 记录完整的错误堆栈

#### 查看我们添加的调试日志：
- 🚀 开始加载仓库数据...
- 🆕 开始创建仓库，提交数据...
- ❌ API调用失败，详细错误信息...

### 3. 🔧 使用调试工具

#### 方法1: 使用HTML调试工具
1. 打开 `frontend/debug-warehouse.html` 文件
2. 在浏览器中直接打开该文件
3. 按顺序测试各项功能
4. 查看详细的请求和响应日志

#### 方法2: 使用PowerShell脚本
1. 以管理员身份打开PowerShell
2. 运行: `.\test-backend-api.ps1`
3. 查看测试结果

### 4. 🎯 常见HTTP 400错误原因

#### A. 请求数据格式错误
```json
// 错误示例：缺少必填字段
{
  "name": "测试仓库"
  // 缺少 code 字段
}

// 正确示例：
{
  "code": "WH001",
  "name": "测试仓库",
  "warehouseType": "distribution",
  "status": "Active"
}
```

#### B. 数据类型不匹配
```json
// 错误示例：
{
  "totalArea": "1000",  // 应该是数字，不是字符串
  "status": "active"    // 应该是 "Active"
}

// 正确示例：
{
  "totalArea": 1000.0,
  "status": "Active"
}
```

#### C. 字段名称不匹配
```json
// 前端发送：
{
  "type": "distribution"  // 错误的字段名
}

// 后端期望：
{
  "warehouseType": "distribution"  // 正确的字段名
}
```

### 5. 🔍 具体操作调试

#### 新增仓库调试：
1. 打开仓库管理页面
2. 点击"新增仓库"
3. 填写表单（只填必填字段）
4. 打开开发者工具Network标签
5. 点击"确定"提交
6. 查看POST请求的详细信息

#### 编辑仓库调试：
1. 点击任意仓库的"编辑"按钮
2. 修改任意字段
3. 查看PUT请求的详细信息

#### 删除仓库调试：
1. 点击任意仓库的"删除"按钮
2. 确认删除
3. 查看DELETE请求的详细信息

### 6. 🛠️ 后端服务检查

#### 检查服务状态：
```bash
# 检查端口是否被占用
netstat -an | findstr :8080

# 或使用PowerShell
Get-NetTCPConnection -LocalPort 8080
```

#### 启动后端服务：
```bash
cd backend/WMS.API
dotnet run
```

#### 查看后端日志：
- 检查控制台输出
- 查看是否有异常信息
- 确认数据库连接状态

### 7. 📝 错误信息收集模板

请按以下格式收集错误信息：

```
操作类型: [新增/编辑/删除/加载]仓库
错误时间: [具体时间]
浏览器: [Chrome/Firefox/Edge + 版本]

=== Network请求信息 ===
请求URL: 
请求方法: 
状态码: 
请求头: 
请求体: 
响应头: 
响应体: 

=== Console错误信息 ===
[粘贴完整的错误信息]

=== 操作步骤 ===
1. 
2. 
3. 

=== 期望结果 ===
[描述期望发生什么]

=== 实际结果 ===
[描述实际发生了什么]
```

### 8. 🚀 快速修复建议

#### 如果是网络连接问题：
- 确认后端服务已启动
- 检查防火墙设置
- 验证API地址配置

#### 如果是数据格式问题：
- 检查必填字段是否完整
- 验证数据类型是否正确
- 确认字段名称是否匹配

#### 如果是权限问题：
- 检查是否已登录
- 验证JWT token是否有效
- 确认用户权限是否足够

### 9. 📞 获取帮助

收集完上述信息后，请提供：
1. 完整的错误信息
2. 具体的操作步骤
3. 浏览器开发者工具的截图
4. Console控制台的错误日志

这样我就能更准确地帮你定位和解决问题！
