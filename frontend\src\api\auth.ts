import { apiClient } from './client'
import type { LoginRequest, LoginResponse, UserInfo } from '@/types'

export const authApi = {
  // Login
  login: (data: LoginRequest) => 
    apiClient.post<LoginResponse>('/auth/login', data),

  // Get current user profile
  getProfile: () => 
    apiClient.get<UserInfo>('/auth/profile'),

  // Refresh token
  refreshToken: (refreshToken: string) => 
    apiClient.post<LoginResponse>('/auth/refresh-token', { refreshToken }),

  // Logout
  logout: (refreshToken: string) => 
    apiClient.post('/auth/logout', { refreshToken }),

  // Change password
  changePassword: (data: { currentPassword: string; newPassword: string }) => 
    apiClient.post('/auth/change-password', data),
}