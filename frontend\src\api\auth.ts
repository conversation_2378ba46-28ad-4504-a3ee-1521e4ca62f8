import { apiClient } from './client'
import type { LoginRequest, LoginResponse, UserInfo } from '@/types'

// Mock login for development
const mockLogin = async (data: LoginRequest): Promise<LoginResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000))

  // Mock user data based on email
  const mockUsers: Record<string, UserInfo> = {
    '<EMAIL>': {
      id: '1',
      email: '<EMAIL>',
      firstName: '系统',
      lastName: '管理员',
      roles: ['SuperAdmin'],
      permissions: ['*'],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    '<EMAIL>': {
      id: '2',
      email: '<EMAIL>',
      firstName: '仓库',
      lastName: '经理',
      roles: ['WarehouseManager'],
      permissions: ['warehouse_management', 'inventory_management', 'operations_management', 'esp32_management'],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    '<EMAIL>': {
      id: '3',
      email: '<EMAIL>',
      firstName: '操作',
      lastName: '员',
      roles: ['Operator'],
      permissions: ['inventory_management', 'operations_management'],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  }

  const user = mockUsers[data.email]
  if (!user) {
    throw new Error('用户名或密码错误')
  }

  // Check password (for demo, accept the predefined passwords)
  const validPasswords: Record<string, string> = {
    '<EMAIL>': 'Admin@123456',
    '<EMAIL>': 'Manager@123456',
    '<EMAIL>': 'Operator@123456',
  }

  if (validPasswords[data.email] !== data.password) {
    throw new Error('用户名或密码错误')
  }

  return {
    accessToken: 'mock-access-token-' + Date.now(),
    refreshToken: 'mock-refresh-token-' + Date.now(),
    user,
  }
}

export const authApi = {
  // Login - use mock for development
  login: async (data: LoginRequest) => {
    try {
      // Try real API first, fallback to mock
      return await apiClient.post<LoginResponse>('/auth/login', data)
    } catch (error) {
      console.warn('API not available, using mock login:', error)
      return await mockLogin(data)
    }
  },

  // Get current user profile
  getProfile: () =>
    apiClient.get<UserInfo>('/auth/profile'),

  // Refresh token
  refreshToken: (refreshToken: string) =>
    apiClient.post<LoginResponse>('/auth/refresh-token', { refreshToken }),

  // Logout
  logout: (refreshToken: string) =>
    apiClient.post('/auth/logout', { refreshToken }),

  // Change password
  changePassword: (data: { currentPassword: string; newPassword: string }) =>
    apiClient.post('/auth/change-password', data),
}