import { defineStore } from 'pinia'
import { authApi } from '@/api/auth'
import type { LoginRequest, UserInfo } from '@/types'

interface AuthState {
  user: UserInfo | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  loading: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    accessToken: localStorage.getItem('accessToken'),
    refreshToken: localStorage.getItem('refreshToken'),
    isAuthenticated: !!localStorage.getItem('accessToken'),
    loading: false,
  }),

  getters: {
    hasRole: (state) => (role: string) => {
      return state.user?.roles?.includes(role) || false
    },
    
    hasPermission: (state) => (permission: string) => {
      // Permission mapping based on roles
      const permissions: Record<string, string[]> = {
        SuperAdmin: ['*'],
        Admin: ['user_management', 'system_config', 'warehouse_management', 'inventory_management', 'operations_management'],
        WarehouseManager: ['warehouse_management', 'inventory_management', 'operations_management', 'esp32_management'],
        Operator: ['inventory_management', 'operations_management'],
        ReadOnly: ['read_only'],
      }
      
      if (!state.user?.roles) return false
      
      return state.user.roles.some(role => {
        const rolePermissions = permissions[role] || []
        return rolePermissions.includes('*') || rolePermissions.includes(permission)
      })
    },
  },

  actions: {
    async login(credentials: LoginRequest) {
      this.loading = true
      try {
        const response = await authApi.login(credentials)
        const { accessToken, refreshToken, user } = response
        
        this.accessToken = accessToken
        this.refreshToken = refreshToken
        this.user = user
        this.isAuthenticated = true
        
        localStorage.setItem('accessToken', accessToken)
        localStorage.setItem('refreshToken', refreshToken)
        
        return { success: true }
      } catch (error: any) {
        return { success: false, message: error.message }
      } finally {
        this.loading = false
      }
    },

    async fetchProfile() {
      try {
        const response = await authApi.getProfile()
        this.user = response
      } catch (error) {
        console.error('Failed to fetch profile:', error)
      }
    },

    async logout() {
      try {
        if (this.refreshToken) {
          await authApi.logout(this.refreshToken)
        }
      } catch (error) {
        console.error('Logout error:', error)
      } finally {
        this.clearAuth()
      }
    },

    clearAuth() {
      this.user = null
      this.accessToken = null
      this.refreshToken = null
      this.isAuthenticated = false
      
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
    },

    async refreshTokens() {
      if (!this.refreshToken) {
        this.clearAuth()
        return false
      }

      try {
        const response = await authApi.refreshToken(this.refreshToken)
        const { accessToken, refreshToken } = response.data
        
        this.accessToken = accessToken
        this.refreshToken = refreshToken
        
        localStorage.setItem('accessToken', accessToken)
        localStorage.setItem('refreshToken', refreshToken)
        
        return true
      } catch (error) {
        this.clearAuth()
        return false
      }
    },
  },
})