# WMS仓库管理系统出入库功能测试文档

## 业务场景背景介绍

### 什么是WMS仓库管理系统？

WMS（Warehouse Management System）仓库管理系统是一个专门用于管理和优化仓库操作的企业级软件系统。它就像是仓库的"大脑"，负责协调和控制所有的仓库活动。

**简单来说，WMS帮助您：**
- 📦 **知道什么东西在哪里** - 准确记录每个商品的位置
- 📊 **知道有多少库存** - 实时追踪库存数量和状态
- 🔄 **管理货物进出** - 控制商品的入库和出库流程
- 🎯 **提高效率** - 减少人工错误，加快作业速度
- 📋 **保证准确性** - 通过条码扫描确保操作准确无误

### 核心业务流程

#### 1. 入库流程（商品进仓库）

**现实场景：** 就像您在网上购物后，商品需要先入库到仓库一样。

```
供应商送货 → 检查商品 → 扫描条码 → 放到指定位置 → 系统记录库存
```

**详细步骤：**
1. **供应商送货** - 供应商将商品送到仓库
2. **创建入库单** - 系统生成入库订单（如：IB20250105001）
3. **验收商品** - 工作人员检查商品数量和质量
4. **扫描条码** - 扫描商品条码，系统自动识别商品信息
5. **LED引导** - 系统点亮LED灯，指示商品应该放在哪个位置
6. **确认入库** - 放好商品后，系统自动更新库存数量
7. **完成入库** - 入库单状态变为"已完成"

#### 2. 出库流程（商品出仓库）

**现实场景：** 就像您下单后，仓库需要找到您的商品并发货一样。

```
客户下单 → 系统分配库存 → LED指示取货位置 → 扫描确认 → 包装发货
```

**详细步骤：**
1. **客户下单** - 客户或系统生成出库需求
2. **创建出库单** - 系统生成出库订单（如：OB20250105001）
3. **分配库存** - 系统自动找到最合适的库存位置（先进先出）
4. **生成拣选任务** - 系统告诉工作人员去哪里拿什么商品
5. **LED引导拣选** - 系统点亮LED灯，指示拣选位置
6. **扫描确认** - 工作人员扫描条码，确认拣选的商品正确
7. **更新库存** - 系统自动扣减库存数量
8. **完成出库** - 出库单状态变为"已完成"

### 系统特色功能

#### 1. 条码扫描技术

**为什么要用条码？**
- 🎯 **准确性** - 避免人工输入错误
- ⚡ **速度快** - 一秒钟就能识别商品
- 📊 **信息丰富** - 包含商品代码、批次、数量等信息

**条码格式示例：**
```
基础格式: TEST001|100|BATCH20250105|LPN001
物料料号|数量|批次号|托盘号

带唯一码格式: TEST001|100|BATCH20250105|LPN001|UNIQUE001
物料料号|数量|批次号|托盘号|唯一码

制造业示例: CHIP-ARM-M4|1|20250105|PALLET001|CHIP001234567
料号|数量|批次|托盘|唯一码
```

**条码包含的信息：**
- **料号** - 告诉系统这是什么类型的物料
- **数量** - 这个包装/托盘中有多少个
- **批次** - 用于质量追溯和有效期管理
- **托盘号** - 物理包装的标识
- **唯一码** - 每个具体物料的身份标识（可选）

#### 2. LED视觉引导系统

**什么是LED引导？**
- 💡 **智能指示** - 系统自动点亮对应储位的LED灯
- 🌈 **颜色区分** - 不同颜色代表不同的操作（绿色入库、红色出库等）
- 📍 **精准定位** - 工作人员一眼就能找到正确位置

**LED颜色含义：**
- 🟢 **绿色** - 入库位置
- 🔴 **红色** - 出库位置
- 🟡 **黄色** - 需要注意的位置
- 🔵 **蓝色** - 盘点位置

#### 3. 智能库存管理

**FIFO原则（先进先出）：**
- 就像超市管理牛奶一样，先到期的先卖出去
- 系统自动选择最早入库的商品进行出库
- 防止商品过期和损坏

**库存状态管理：**
- ✅ **可用** - 可以正常出库
- ⏳ **预留** - 已分配但未出库
- 🚫 **冻结** - 有问题，不能出库
- 📋 **盘点中** - 正在盘点，暂停操作

### 系统架构简介

#### 1. 数据层次结构

```
仓库（Warehouse）
├── 区域（Zone）- 如：常温区、冷冻区
    ├── 货架（Shelf）- 如：A货架、B货架
        ├── 储位（StorageLocation）- 如：A-01-01-01
```

**层次化管理的好处：**
- 🏢 **仓库级** - 整个仓库的统一配置
- 🌡️ **区域级** - 温度、湿度等环境控制
- 📦 **货架级** - 承重、尺寸等物理限制
- 📍 **储位级** - 具体的存储位置

#### 2. 核心数据模型

**物料（Material）：**
- 商品的基本信息（名称、规格、单位等）
- 就像商品的"身份证"

**料号与唯一码概念：**
- **料号（SKU）** - 相同规格物料的统一编码
  - 例如：所有iPhone14-128GB-蓝色都是同一个料号"IPHONE14-128-BLUE"
  - 用于库存汇总、采购计划、销售统计等
- **唯一码（UniqueCode）** - 同一料号下每个具体物料的唯一标识
  - 例如：每个iPhone的序列号"SN123456789"都是唯一的
  - 用于质量追溯、保修管理、精确定位等

**举例说明：**
```
料号: IPHONE14-128-BLUE (表示iPhone14 128GB 蓝色这个产品类型)
├── 唯一码: SN123456789 (具体的第一台iPhone)
├── 唯一码: SN123456790 (具体的第二台iPhone)  
└── 唯一码: SN123456791 (具体的第三台iPhone)
```

**库存（Inventory）：**
- 记录每个储位上有什么商品、多少数量
- 支持按料号汇总和按唯一码精确追踪两种模式
- 就像仓库的"账本"

**事务（Transaction）：**
- 记录所有的库存变化
- 包含料号和唯一码信息，支持完整的追溯链
- 就像银行的"流水账"

### 系统使用场景

#### 场景1：电商仓库
- **特点** - 商品种类多，订单量大，要求快速发货
- **优势** - 批量拣选，路径优化，减少拣选时间

#### 场景2：制造业仓库
- **特点** - 原材料管理，半成品流转，严格的质量追溯要求
- **优势** - 批次追溯，质量控制，生产配套

**制造业中的料号与唯一码应用：**

**原材料管理：**
```
料号: CHIP-ARM-M4 (ARM M4芯片)
├── 唯一码: CHIP001234567 (具体的芯片1) → 用于产品A
├── 唯一码: CHIP001234568 (具体的芯片2) → 用于产品B
└── 唯一码: CHIP001234569 (具体的芯片3) → 库存中
```

**质量追溯场景：**
1. **问题发现** - 客户反馈产品X有质量问题
2. **追溯唯一码** - 查找产品X的序列号SN999888777
3. **查找原料** - 找到该产品使用的所有原材料唯一码
4. **批次分析** - 检查同批次其他产品是否有问题
5. **精确召回** - 只召回使用了问题原料的具体产品

**生产配套流程：**
1. **生产计划** - 需要生产100台产品A
2. **料号需求** - 每台需要芯片CHIP-ARM-M4 × 1
3. **唯一码分配** - 系统分配100个具体的芯片唯一码
4. **生产追踪** - 记录每个产品使用了哪些具体原料
5. **成品编码** - 生产出的成品也获得唯一码

**优势体现：**
- 🔍 **精确追溯** - 从成品可以追溯到每个原材料
- 🎯 **精准召回** - 只召回有问题的具体产品
- 📊 **质量分析** - 统计不同批次/供应商的质量情况
- 🔧 **维修管理** - 知道每个产品的具体配置和历史

#### 场景3：医药仓库
- **特点** - 严格的温度控制，有效期管理
- **优势** - 温度监控，效期预警，合规管理

### 开始使用前的准备

**您需要了解的基本概念：**
1. **料号（SKU）** - 相同规格物料的统一编码（如：IPHONE14-128-BLUE）
2. **唯一码（UniqueCode）** - 同一料号下每个具体物料的唯一标识（如：序列号SN123456789）
3. **批次（Batch）** - 同一时间生产的商品组（如：20250105批次）
4. **LPN（托盘号）** - 用于标识一个托盘上的商品组合
5. **储位编码** - 每个储位的唯一地址（如：A-01-01-01）

**料号与唯一码的区别：**
- 料号是"类别"，唯一码是"个体"
- 料号用于统计和计划，唯一码用于追溯和精确操作
- 一个料号可以对应成千上万个唯一码

**实际应用举例：**
```
采购: 我要采购100个 CHIP-ARM-M4 (按料号下单)
入库: 收到100个芯片，每个都有唯一码 CHIP001~CHIP100
生产: 产品A使用了芯片CHIP001，产品B使用了芯片CHIP002
追溯: 产品A有问题，立即找到是芯片CHIP001的问题
召回: 只召回使用了CHIP001的产品A，其他产品不受影响
```

**系统操作流程：**
1. 先登录系统获取权限
2. 创建基础数据（物料、储位）
3. 进行入库操作
4. 进行出库操作
5. 查看库存和报表

现在让我们开始实际操作测试！

---

## 1. 测试环境准备

### 1.1 系统启动

首先启动系统服务：

```bash
# 1. 启动数据库
cd /mnt/d/ProjestFiles/ESP/WMS-VGL
powershell.exe -Command "docker-compose up -d wms-postgres"

# 2. 启动API服务  
cd backend/WMS.API
powershell.exe -Command "dotnet run --urls http://0.0.0.0:5000"
```

**预期结果：**
- 数据库服务正常启动
- API服务在 http://localhost:5000 运行
- 看到 "Application started" 消息

### 1.2 验证API服务

在浏览器访问：http://localhost:5000/swagger

**预期结果：**
- 看到Swagger API文档界面
- 可以看到新增的 Inbound、Outbound、Picking 控制器

## 2. 认证准备

### 2.1 获取访问令牌

使用Postman或curl获取JWT令牌：

```bash
# 管理员登录
curl -X POST "http://localhost:5000/api/Auth/login" \
-H "Content-Type: application/json" \
-d '{
  "email": "<EMAIL>",
  "password": "Admin@123456",
  "rememberMe": true
}'
```

**预期结果：**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "xxx",
  "expiresIn": 3600,
  "user": {
    "id": "xxx",
    "email": "<EMAIL>",
    "fullName": "系统管理员"
  }
}
```

**重要：** 复制 `accessToken` 的值，后续所有API调用都需要在Header中添加：
```
Authorization: Bearer <你的accessToken>
```

## 3. 基础数据准备

### 3.1 创建测试物料

```bash
curl -X POST "http://localhost:5000/api/Materials" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "sku": "TEST001",
  "name": "测试商品A",
  "description": "用于测试的商品",
  "category": "电子产品",
  "unit": "个",
  "weight": 0.5,
  "dimensions": "10x10x5",
  "isActive": true
}'
```

**预期结果：**
```json
{
  "id": 1,
  "sku": "TEST001",
  "name": "测试商品A",
  "category": "电子产品",
  "unit": "个"
}
```

### 3.2 验证储位数据

```bash
curl -X GET "http://localhost:5000/api/StorageLocations" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
- 返回储位列表
- 记录一个储位的ID（如：1）用于后续测试

## 4. 入库流程测试

### 4.1 创建入库订单

```bash
curl -X POST "http://localhost:5000/api/Inbound" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "supplierName": "测试供应商",
  "supplierCode": "SUP001",
  "expectedDate": "2025-01-10T10:00:00Z",
  "remarks": "测试入库订单",
  "items": [
    {
      "materialId": 1,
      "expectedQuantity": 100,
      "batchNumber": "BATCH20250105",
      "lpnCode": "LPN001"
    }
  ]
}'
```

**预期结果：**
```json
{
  "id": 1,
  "orderNumber": "IB20250105xxxx",
  "status": 0,
  "supplierName": "测试供应商",
  "items": [
    {
      "id": 1,
      "materialId": 1,
      "expectedQuantity": 100,
      "batchNumber": "BATCH20250105"
    }
  ]
}
```

**说明：**
- `status: 0` 表示待处理状态
- 订单号自动生成（格式：IB+日期+序号）

### 4.2 查看入库订单

```bash
curl -X GET "http://localhost:5000/api/Inbound/1" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
- 返回完整的入库订单信息
- 包含订单项目和物料信息

### 4.3 条码入库处理

```bash
curl -X POST "http://localhost:5000/api/Inbound/items/1/process-barcode" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "barcodeData": "TEST001|100|BATCH20250105|LPN001|UNIQUE001",
  "storageLocationCode": "A-01-01-01",
  "actualQuantity": 100
}'
```

**说明：**
- 条码现在包含了唯一码信息`UNIQUE001`
- 系统会同时记录料号`TEST001`和唯一码`UNIQUE001`
- 这样可以实现精确的物料追踪

**预期结果：**
```json
{
  "message": "条码入库处理成功"
}
```

### 4.4 验证入库结果

**检查库存事务：**
```bash
curl -X GET "http://localhost:5000/api/InventoryTransactions" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
- 看到一条 `type: 0`（入库）的事务记录
- 状态为已执行
- 包含条码数据

**检查库存：**
```bash
curl -X GET "http://localhost:5000/api/Inventories" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
- 看到新增的库存记录
- 数量为100
- 状态为可用

## 5. 出库流程测试

### 5.1 创建出库订单

```bash
curl -X POST "http://localhost:5000/api/Outbound" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "type": 0,
  "customerName": "测试客户",
  "customerCode": "CUS001",
  "expectedDate": "2025-01-06T14:00:00Z",
  "remarks": "测试出库订单",
  "items": [
    {
      "materialId": 1,
      "requiredQuantity": 50,
      "batchNumber": "BATCH20250105"
    }
  ]
}'
```

**预期结果：**
```json
{
  "id": 1,
  "orderNumber": "OB20250105xxxx",
  "type": 0,
  "status": 0,
  "customerName": "测试客户",
  "items": [
    {
      "id": 1,
      "materialId": 1,
      "requiredQuantity": 50
    }
  ]
}
```

### 5.2 开始拣选作业

```bash
curl -X POST "http://localhost:5000/api/Outbound/1/start-picking" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
```json
{
  "message": "拣选作业已开始"
}
```

### 5.3 查看拣选任务

```bash
curl -X GET "http://localhost:5000/api/Picking/tasks?orderId=1" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
```json
[
  {
    "id": 1,
    "outboundOrderItemId": 1,
    "storageLocationId": 1,
    "requiredQuantity": 50,
    "status": 0,
    "storageLocation": {
      "code": "A-01-01-01",
      "name": "储位A-01-01-01"
    }
  }
]
```

### 5.4 执行条码拣选

```bash
curl -X POST "http://localhost:5000/api/Picking/tasks/1/process-barcode" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "barcodeData": "TEST001|50|BATCH20250105",
  "pickedQuantity": 50
}'
```

**预期结果：**
```json
{
  "message": "条码拣选处理成功"
}
```

### 5.5 完成出库订单

```bash
curl -X POST "http://localhost:5000/api/Outbound/1/complete" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
```json
{
  "message": "出库订单已完成"
}
```

### 5.6 验证出库结果

**检查库存事务：**
```bash
curl -X GET "http://localhost:5000/api/InventoryTransactions" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
- 看到新的 `type: 1`（出库）事务记录
- 数量为-50（负数表示出库）

**检查剩余库存：**
```bash
curl -X GET "http://localhost:5000/api/Inventories" \
-H "Authorization: Bearer <你的accessToken>"
```

**预期结果：**
- 库存数量减少到50
- 状态仍为可用

## 6. 高级功能测试

### 6.1 批量入库处理

```bash
curl -X POST "http://localhost:5000/api/Inbound/1/process" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "items": [
    {
      "itemId": 1,
      "storageLocationId": 1,
      "actualQuantity": 95,
      "batchNumber": "BATCH20250105",
      "uniqueCode": "UNIQUE001"
    }
  ]
}'
```

### 6.2 订单状态管理

**更新订单状态：**
```bash
curl -X PATCH "http://localhost:5000/api/Inbound/1/status" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "status": 2
}'
```

**取消订单：**
```bash
curl -X POST "http://localhost:5000/api/Inbound/1/cancel" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "reason": "测试取消功能"
}'
```

## 7. 数据验证和报表

### 7.1 查看完整的业务数据

**所有入库订单：**
```bash
curl -X GET "http://localhost:5000/api/Inbound" \
-H "Authorization: Bearer <你的accessToken>"
```

**所有出库订单：**
```bash
curl -X GET "http://localhost:5000/api/Outbound" \
-H "Authorization: Bearer <你的accessToken>"
```

**所有库存事务：**
```bash
curl -X GET "http://localhost:5000/api/InventoryTransactions" \
-H "Authorization: Bearer <你的accessToken>"
```

### 7.2 按状态筛选

**查看待处理的订单：**
```bash
curl -X GET "http://localhost:5000/api/Inbound?status=0" \
-H "Authorization: Bearer <你的accessToken>"
```

**查看已完成的拣选任务：**
```bash
curl -X GET "http://localhost:5000/api/Picking/tasks?status=2" \
-H "Authorization: Bearer <你的accessToken>"
```

## 8. 错误场景测试

### 8.1 库存不足测试

尝试出库超过库存数量：
```bash
curl -X POST "http://localhost:5000/api/Outbound" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "type": 0,
  "customerName": "测试客户2",
  "items": [
    {
      "materialId": 1,
      "requiredQuantity": 1000
    }
  ]
}'
```

**预期结果：**
- 创建订单成功，但开始拣选时会失败
- 提示库存不足

### 8.2 条码验证测试

使用错误的物料条码：
```bash
curl -X POST "http://localhost:5000/api/Inbound/items/1/process-barcode" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <你的accessToken>" \
-d '{
  "barcodeData": "WRONG001|100|BATCH001",
  "storageLocationCode": "A-01-01-01",
  "actualQuantity": 100
}'
```

**预期结果：**
- 返回400错误
- 提示物料不匹配

## 9. 性能和数据完整性验证

### 9.1 并发测试

同时创建多个订单，验证订单号生成的唯一性：
```bash
# 可以同时运行多个创建订单的命令
# 验证订单号不会重复
```

### 9.2 事务完整性测试

在入库/出库过程中检查数据一致性：
- 库存数量变化是否正确
- 事务记录是否完整
- 订单状态变化是否正确

## 10. 系统状态监控

### 10.1 检查系统健康

```bash
curl -X GET "http://localhost:5000/api/ESP32Controllers" \
-H "Authorization: Bearer <你的accessToken>"
```

### 10.2 查看日志

观察控制台输出的日志信息：
- 入库/出库操作日志
- 条码解析日志
- 库存变化日志
- ESP32通信日志（如果有设备）

## 11. Postman测试集合

为了更方便的测试，建议导入项目中的Postman集合：

1. 打开Postman
2. 导入 `WMS-VGL-API.postman_collection.json`
3. 导入 `WMS-VGL-Environment.postman_environment.json`
4. 选择 "WMS-VGL Development Environment"
5. 运行 "01 - 用户认证 > 管理员登录" 获取token
6. 按顺序运行出入库测试用例

## 12. 常见问题排查

### 12.1 API服务无法启动

**问题：** `dotnet run` 失败
**解决：**
```bash
# 检查.NET版本
dotnet --version
# 应该是9.0.x

# 清理并重建
dotnet clean
dotnet build
```

### 12.2 数据库连接失败

**问题：** 无法连接PostgreSQL
**解决：**
```bash
# 检查Docker容器状态
docker ps | grep postgres

# 重启数据库容器
docker-compose restart wms-postgres
```

### 12.3 认证失败

**问题：** 401 Unauthorized
**解决：**
- 检查JWT token是否正确
- Token是否已过期（1小时有效期）
- 重新登录获取新token

### 12.4 条码解析失败

**问题：** 条码入库/拣选失败
**解决：**
- 检查条码格式是否正确
- 验证物料SKU是否匹配
- 确认储位代码存在

## 测试总结

通过以上测试，你应该能够验证：

✅ **核心业务流程**
- 完整的入库流程：订单创建 → 条码入库 → 库存更新
- 完整的出库流程：订单创建 → 拣选任务 → 条码拣选 → 库存扣减

✅ **数据完整性**
- 库存数量准确性
- 事务记录完整性
- 订单状态正确性

✅ **错误处理**
- 库存不足处理
- 条码验证
- 业务规则约束

✅ **系统集成**
- 条码解析系统集成
- LED控制系统准备（API已就绪）
- 用户认证和权限控制

✅ **API功能验证**
- RESTful接口完整性
- 数据序列化正确性
- HTTP状态码规范性

这个测试文档确保了WMS系统的出入库功能完全可用，为后续的高级功能开发奠定了坚实基础。

## 下一步计划

基于当前实现的基础功能，后续可以扩展：

1. **高级拣选策略** - 波次拣选、路径优化
2. **批次和效期管理** - FIFO/FEFO策略
3. **库存预警和补货** - 安全库存管理
4. **移库和盘点** - 库存调整功能
5. **报表和分析** - 业务数据统计
6. **移动端应用** - 手持终端集成
7. **ESP32硬件集成** - LED灯条实物测试

每个功能模块都可以基于现有的架构和数据模型进行扩展开发。