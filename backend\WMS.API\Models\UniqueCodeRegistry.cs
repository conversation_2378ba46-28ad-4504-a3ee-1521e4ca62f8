using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 唯一码状态枚举
    /// </summary>
    public enum UniqueCodeStatus
    {
        /// <summary>
        /// 已注册
        /// </summary>
        REGISTERED,

        /// <summary>
        /// 已入库
        /// </summary>
        IN_STOCK,

        /// <summary>
        /// 已出库
        /// </summary>
        OUT_STOCK,

        /// <summary>
        /// 已过期
        /// </summary>
        EXPIRED,
    }

    /// <summary>
    /// 唯一码注册表
    /// </summary>
    public class UniqueCodeRegistry
    {
        /// <summary>
        /// 注册记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 唯一码
        /// </summary>
        [Required]
        [StringLength(100)]
        public string UniqueCode { get; set; } = string.Empty;

        /// <summary>
        /// 关联的物料ID
        /// </summary>
        public int MaterialId { get; set; }

        /// <summary>
        /// 关联的物料实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual Material? Material { get; set; }

        /// <summary>
        /// 关联的储位ID（可选）
        /// </summary>
        public int? StorageLocationId { get; set; }

        /// <summary>
        /// 关联的储位实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual StorageLocation? StorageLocation { get; set; }

        /// <summary>
        /// 唯一码状态
        /// </summary>
        public UniqueCodeStatus Status { get; set; } = UniqueCodeStatus.REGISTERED;

        /// <summary>
        /// 注册日期
        /// </summary>
        public DateTime RegistrationDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后操作日期
        /// </summary>
        public DateTime LastOperationDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
}
