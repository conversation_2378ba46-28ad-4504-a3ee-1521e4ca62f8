namespace WMS.API.Services
{
    /// <summary>
    /// 数据种子服务接口
    /// 用于初始化系统默认数据
    /// </summary>
    public interface IDataSeedService
    {
        /// <summary>
        /// 初始化系统默认数据
        /// </summary>
        /// <returns></returns>
        Task SeedAsync();

        /// <summary>
        /// 初始化默认角色
        /// </summary>
        /// <returns></returns>
        Task SeedRolesAsync();

        /// <summary>
        /// 初始化默认超级管理员用户
        /// </summary>
        /// <returns></returns>
        Task SeedSuperAdminAsync();
    }
}