using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 分类维度枚举
    /// </summary>
    public enum ClassificationDimension
    {
        /// <summary>
        /// 物理维度 - 基于物理位置特征
        /// </summary>
        Physical = 0,

        /// <summary>
        /// 业务维度 - 基于业务需求
        /// </summary>
        Business = 1,

        /// <summary>
        /// 管理维度 - 基于管理需要
        /// </summary>
        Management = 2,

        /// <summary>
        /// 技术维度 - 基于技术特性
        /// </summary>
        Technical = 3,

        /// <summary>
        /// 安全维度 - 基于安全要求
        /// </summary>
        Security = 4,

        /// <summary>
        /// 合规维度 - 基于合规要求
        /// </summary>
        Compliance = 5,

        /// <summary>
        /// 客户维度 - 基于客户分类
        /// </summary>
        Customer = 6,

        /// <summary>
        /// 产品维度 - 基于产品特性
        /// </summary>
        Product = 7
    }

    /// <summary>
    /// 储位分类实体类
    /// 用于多维度分类和管理储位
    /// </summary>
    public class LocationClassification
    {
        /// <summary>
        /// 分类记录唯一标识ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 关联的储位ID
        /// </summary>
        public int StorageLocationId { get; set; }

        /// <summary>
        /// 关联的储位实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual StorageLocation? StorageLocation { get; set; }

        /// <summary>
        /// 分类维度
        /// </summary>
        public ClassificationDimension Dimension { get; set; }

        /// <summary>
        /// 分类类别
        /// 例如：Customer, ProductType, SecurityLevel, TemperatureZone
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 分类值
        /// 例如：华康医药, 管制药品, High, ColdChain
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 分类显示名称（用于界面显示）
        /// </summary>
        [StringLength(100)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 分类描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 分类标签（JSON数组格式）
        /// 例如：["VIP", "HighValue", "Restricted"]
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// 分类属性（JSON格式）
        /// 存储该分类的详细属性信息
        /// </summary>
        public string? Properties { get; set; }

        /// <summary>
        /// 分类配置（JSON格式）
        /// 存储该分类的配置策略
        /// </summary>
        public string? Configuration { get; set; }

        /// <summary>
        /// 优先级（数字越小优先级越高）
        /// 用于配置冲突时的优先级判断
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否系统自动分配
        /// </summary>
        public bool IsAutoAssigned { get; set; } = false;

        /// <summary>
        /// 分类生效开始时间
        /// </summary>
        public DateTime? EffectiveFrom { get; set; }

        /// <summary>
        /// 分类生效结束时间
        /// </summary>
        public DateTime? EffectiveTo { get; set; }

        /// <summary>
        /// 继承自父级分类
        /// 例如货架级或区域级的分类
        /// </summary>
        public bool IsInherited { get; set; } = false;

        /// <summary>
        /// 继承来源（当IsInherited为true时）
        /// 例如：Shelf:A01, Zone:A
        /// </summary>
        [StringLength(100)]
        public string? InheritedFrom { get; set; }

        /// <summary>
        /// 验证规则（JSON格式）
        /// 定义该分类的验证规则
        /// </summary>
        public string? ValidationRules { get; set; }

        /// <summary>
        /// 业务规则（JSON格式）
        /// 定义该分类相关的业务规则
        /// </summary>
        public string? BusinessRules { get; set; }

        /// <summary>
        /// 关联的外部系统标识
        /// 例如ERP系统中的分类代码
        /// </summary>
        [StringLength(100)]
        public string? ExternalSystemRef { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }
    }
}