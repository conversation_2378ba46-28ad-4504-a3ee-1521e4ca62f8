using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WMS.API.Models
{
    /// <summary>
    /// 货架状态枚举
    /// </summary>
    public enum ShelfStatus
    {
        /// <summary>
        /// 运营中 - 货架正常使用
        /// </summary>
        Active = 0,

        /// <summary>
        /// 维护中 - 货架维护暂停使用
        /// </summary>
        Maintenance = 1,

        /// <summary>
        /// 已停用 - 货架已停用
        /// </summary>
        Inactive = 2,

        /// <summary>
        /// 锁定中 - 货架被锁定
        /// </summary>
        Locked = 3,

        /// <summary>
        /// 损坏 - 货架损坏待修复
        /// </summary>
        Damaged = 4
    }

    /// <summary>
    /// 货架类型枚举
    /// </summary>
    public enum ShelfType
    {
        /// <summary>
        /// 标准货架
        /// </summary>
        Standard = 0,

        /// <summary>
        /// 高密度货架
        /// </summary>
        HighDensity = 1,

        /// <summary>
        /// 悬臂式货架
        /// </summary>
        Cantilever = 2,

        /// <summary>
        /// 阁楼式货架
        /// </summary>
        Mezzanine = 3,

        /// <summary>
        /// 流利式货架
        /// </summary>
        FlowRack = 4,

        /// <summary>
        /// 自动化货架
        /// </summary>
        Automated = 5,

        /// <summary>
        /// 冷链专用货架
        /// </summary>
        ColdChain = 6,

        /// <summary>
        /// 安全保险柜
        /// </summary>
        SecurityVault = 7
    }

    /// <summary>
    /// 货架实体类
    /// 用于管理区域内的货架及其储位
    /// </summary>
    public class Shelf
    {
        /// <summary>
        /// 货架唯一标识ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 货架编码，如：A01, A02, B01, COLD-A01
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 货架名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 货架描述信息
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 关联的区域ID
        /// </summary>
        public int ZoneId { get; set; }

        /// <summary>
        /// 关联的区域实体
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual Zone? Zone { get; set; }

        /// <summary>
        /// 货架类型
        /// </summary>
        public ShelfType ShelfType { get; set; } = ShelfType.Standard;

        /// <summary>
        /// 货架状态
        /// </summary>
        public ShelfStatus Status { get; set; } = ShelfStatus.Active;

        /// <summary>
        /// 货架长度（米）
        /// </summary>
        public decimal? Length { get; set; }

        /// <summary>
        /// 货架宽度（米）
        /// </summary>
        public decimal? Width { get; set; }

        /// <summary>
        /// 货架高度（米）
        /// </summary>
        public decimal? Height { get; set; }

        /// <summary>
        /// 层数
        /// </summary>
        public int? Levels { get; set; }

        /// <summary>
        /// 每层储位数量
        /// </summary>
        public int? PositionsPerLevel { get; set; }

        /// <summary>
        /// 最大载重（公斤）
        /// </summary>
        public decimal? MaxWeight { get; set; }

        /// <summary>
        /// 每个储位最大载重（公斤）
        /// </summary>
        public decimal? MaxWeightPerPosition { get; set; }

        /// <summary>
        /// 材质（如：钢制、铝合金等）
        /// </summary>
        [StringLength(50)]
        public string? Material { get; set; }

        /// <summary>
        /// 制造商
        /// </summary>
        [StringLength(100)]
        public string? Manufacturer { get; set; }

        /// <summary>
        /// 制造日期
        /// </summary>
        public DateTime? ManufactureDate { get; set; }

        /// <summary>
        /// 安装日期
        /// </summary>
        public DateTime? InstallationDate { get; set; }

        /// <summary>
        /// 最后检查日期
        /// </summary>
        public DateTime? LastInspectionDate { get; set; }

        /// <summary>
        /// 下次检查日期
        /// </summary>
        public DateTime? NextInspectionDate { get; set; }

        /// <summary>
        /// 安全认证信息（JSON格式）
        /// 例如：{"certifications": ["CE", "ISO"], "fireRating": "A1"}
        /// </summary>
        public string? SafetyCertifications { get; set; }

        /// <summary>
        /// 存储策略（JSON格式）
        /// 例如：{"allowMixedSku": false, "fifoEnforced": true}
        /// </summary>
        public string? StoragePolicy { get; set; }

        /// <summary>
        /// 访问限制（JSON格式）
        /// 例如：{"authorizedRoles": ["manager", "operator"], "timeRestrictions": {...}}
        /// </summary>
        public string? AccessRestrictions { get; set; }

        /// <summary>
        /// 配置信息（JSON格式）
        /// 存储货架级别的配置策略
        /// </summary>
        public string? Configuration { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        [StringLength(50)]
        public string? ResponsiblePerson { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 关联的储位集合
        /// 一个货架可以包含多个储位
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public virtual ICollection<StorageLocation> StorageLocations { get; set; } = new List<StorageLocation>();
    }
}