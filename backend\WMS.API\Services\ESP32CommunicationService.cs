using System.Text;
using System.Text.Json;
using WMS.API.DTOs.ESP32;
using WMS.API.Models;

namespace WMS.API.Services
{
    public interface IESP32CommunicationService
    {
        Task<bool> TestConnectionAsync(ESP32Controller controller);
        Task<bool> SetLightAsync(
            ESP32Controller controller,
            int channel,
            int startPosition,
            int endPosition,
            string color,
            int brightness = 255
        );
        Task<bool> SetAllLightsAsync(
            ESP32Controller controller,
            int channel,
            string color,
            int brightness = 255
        );
        Task<bool> TurnOffAllLightsAsync(ESP32Controller controller, int channel);
        Task<bool> ControlBigLedAsync(
            ESP32Controller controller,
            int indicatorIndex,
            bool turnOn
        );
        Task<DeviceInfoResponse?> GetDeviceInfoAsync(ESP32Controller controller);
    }

    public class ESP32CommunicationService : IESP32CommunicationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ESP32CommunicationService> _logger;

        public ESP32CommunicationService(
            HttpClient httpClient,
            ILogger<ESP32CommunicationService> logger
        )
        {
            _httpClient = httpClient;
            _logger = logger;

            // 设置超时时间
            _httpClient.Timeout = TimeSpan.FromSeconds(10);
        }

        public async Task<bool> TestConnectionAsync(ESP32Controller controller)
        {
            try
            {
                var url = $"http://{controller.IpAddress}:{controller.Port}/";
                var response = await _httpClient.GetAsync(url);

                // ESP32根路径返回404状态码但是会有响应内容
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return content.Contains("This is QR-Shelf Server");
                }

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to test connection to ESP32 controller {ControllerId} at {IpAddress}:{Port}",
                    controller.Id,
                    controller.IpAddress,
                    controller.Port
                );
                return false;
            }
        }

        public async Task<bool> SetLightAsync(
            ESP32Controller controller,
            int channel,
            int startPosition,
            int endPosition,
            string color,
            int brightness = 255
        )
        {
            try
            {
                var ledConfig = new LedControlRequest
                {
                    Leds = new List<LedConfiguration>
                    {
                        new LedConfiguration
                        {
                            Channel = channel,
                            Start = startPosition,
                            Count = endPosition - startPosition + 1,
                            Brightness = brightness,
                            Color = ConvertColorToHex(color),
                        },
                    },
                };

                return await SendLedControlRequestAsync(controller, ledConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to set light for ESP32 controller {ControllerId}",
                    controller.Id
                );
                return false;
            }
        }

        public async Task<bool> SetAllLightsAsync(
            ESP32Controller controller,
            int channel,
            string color,
            int brightness = 255
        )
        {
            try
            {
                var url = $"http://{controller.IpAddress}:{controller.Port}/setAllLeds";
                var colorValue = ConvertColorToColorValue(color);
                var brightnessValue = (int)(brightness / 255.0 * 31); // 转换到0-31范围

                var requestUrl =
                    $"{url}?strip={channel}&color={colorValue}&brightness={brightnessValue}";
                var response = await _httpClient.PostAsync(requestUrl, null);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to set all lights for ESP32 controller {ControllerId}",
                    controller.Id
                );
                return false;
            }
        }

        public async Task<bool> TurnOffAllLightsAsync(ESP32Controller controller, int channel)
        {
            return await SetAllLightsAsync(controller, channel, "black", 0);
        }

        public async Task<bool> ControlBigLedAsync(
            ESP32Controller controller,
            int indicatorIndex,
            bool turnOn
        )
        {
            try
            {
                var url = $"http://{controller.IpAddress}:{controller.Port}/controlBigLed";
                var status = turnOn ? "on" : "off";
                var requestUrl = $"{url}?y{indicatorIndex}={status}";

                _logger.LogInformation(
                    "Sending big LED control request to {Url} for controller {ControllerId}, indicator {IndicatorIndex}, status {Status}",
                    requestUrl,
                    controller.Id,
                    indicatorIndex,
                    status
                );

                var response = await _httpClient.GetAsync(requestUrl);

                _logger.LogInformation(
                    "ESP32 big LED response status: {StatusCode}",
                    response.StatusCode
                );

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(
                        "ESP32 big LED response content: {ResponseContent}",
                        responseContent
                    );
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError(
                        "ESP32 big LED request failed with status {StatusCode}: {ErrorContent}",
                        response.StatusCode,
                        errorContent
                    );
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to control big LED for ESP32 controller {ControllerId}, indicator {IndicatorIndex}",
                    controller.Id,
                    indicatorIndex
                );
                return false;
            }
        }

        public async Task<DeviceInfoResponse?> GetDeviceInfoAsync(ESP32Controller controller)
        {
            try
            {
                var url = $"http://{controller.IpAddress}:{controller.Port}/getDeviceParams";
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var deviceInfo = JsonSerializer.Deserialize<DeviceInfoResponse>(
                        jsonContent,
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
                    );

                    return deviceInfo;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to get device info from ESP32 controller {ControllerId}",
                    controller.Id
                );
                return null;
            }
        }

        private async Task<bool> SendLedControlRequestAsync(
            ESP32Controller controller,
            LedControlRequest request
        )
        {
            try
            {
                var url = $"http://{controller.IpAddress}:{controller.Port}/controlLEDsV2";
                var json = JsonSerializer.Serialize(
                    request,
                    new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }
                );
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _logger.LogInformation("Sending LED control request to {Url}: {Json}", url, json);

                var response = await _httpClient.PostAsync(url, content);

                _logger.LogInformation("ESP32 response status: {StatusCode}", response.StatusCode);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(
                        "ESP32 response content: {ResponseContent}",
                        responseContent
                    );

                    var ledResponse = JsonSerializer.Deserialize<LedControlResponse>(
                        responseContent,
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
                    );

                    return ledResponse?.Status == "success";
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError(
                        "ESP32 request failed with status {StatusCode}: {ErrorContent}",
                        response.StatusCode,
                        errorContent
                    );
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to send LED control request to ESP32 controller {ControllerId}",
                    controller.Id
                );
                return false;
            }
        }

        private static string ConvertColorToHex(string color)
        {
            return color.ToLower() switch
            {
                "red" => "FF0000",
                "green" => "00FF00",
                "blue" => "0000FF",
                "yellow" => "FFFF00",
                "white" => "FFFFFF",
                "black" => "000000",
                "cyan" => "00FFFF",
                "magenta" => "FF00FF",
                _ => color.StartsWith("#") ? color[1..] : color,
            };
        }

        private static int ConvertColorToColorValue(string color)
        {
            return color.ToLower() switch
            {
                "black" => 0,
                "red" => 1,
                "green" => 2,
                "yellow" => 3,
                "blue" => 4,
                "magenta" => 5,
                "cyan" => 6,
                "white" => 7,
                _ => 0,
            };
        }
    }
}
