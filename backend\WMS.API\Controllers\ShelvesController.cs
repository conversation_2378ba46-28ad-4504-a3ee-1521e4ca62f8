using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using WMS.API.Constants;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ShelvesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ShelvesController> _logger;

        public ShelvesController(ApplicationDbContext context, ILogger<ShelvesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有货架列表
        /// </summary>
        /// <returns>货架列表</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Shelf>>> GetShelves()
        {
            try
            {
                var shelves = await _context.Shelves
                    .Include(s => s.Zone)
                        .ThenInclude(z => z.Warehouse)
                    .Include(s => s.StorageLocations)
                    .OrderBy(s => s.Zone.WarehouseId)
                    .ThenBy(s => s.ZoneId)
                    .ThenBy(s => s.Code)
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} shelves", shelves.Count);
                return Ok(shelves);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving shelves");
                return StatusCode(500, "Internal server error while retrieving shelves");
            }
        }

        /// <summary>
        /// 获取指定货架详情
        /// </summary>
        /// <param name="id">货架ID</param>
        /// <returns>货架详情</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<Shelf>> GetShelf(int id)
        {
            try
            {
                var shelf = await _context.Shelves
                    .Include(s => s.Zone)
                        .ThenInclude(z => z.Warehouse)
                    .Include(s => s.StorageLocations)
                        .ThenInclude(sl => sl.ESP32Controller)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (shelf == null)
                {
                    _logger.LogWarning("Shelf with ID {Id} not found", id);
                    return NotFound($"Shelf with ID {id} not found");
                }

                _logger.LogInformation("Retrieved shelf {Id}: {Name}", id, shelf.Name);
                return Ok(shelf);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving shelf {Id}", id);
                return StatusCode(500, "Internal server error while retrieving shelf");
            }
        }

        /// <summary>
        /// 根据区域ID获取货架列表
        /// </summary>
        /// <param name="zoneId">区域ID</param>
        /// <returns>指定区域的货架列表</returns>
        [HttpGet("by-zone/{zoneId}")]
        public async Task<ActionResult<IEnumerable<Shelf>>> GetShelvesByZone(int zoneId)
        {
            try
            {
                var shelves = await _context.Shelves
                    .Include(s => s.StorageLocations)
                    .Where(s => s.ZoneId == zoneId)
                    .OrderBy(s => s.Code)
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} shelves for zone {ZoneId}", 
                    shelves.Count, zoneId);
                return Ok(shelves);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving shelves for zone {ZoneId}", zoneId);
                return StatusCode(500, "Internal server error while retrieving shelves");
            }
        }

        /// <summary>
        /// 根据货架类型查询货架
        /// </summary>
        /// <param name="shelfType">货架类型</param>
        /// <param name="zoneId">区域ID（可选）</param>
        /// <returns>指定类型的货架列表</returns>
        [HttpGet("by-type")]
        public async Task<ActionResult<IEnumerable<Shelf>>> GetShelvesByType(ShelfType shelfType, int? zoneId = null)
        {
            try
            {
                var query = _context.Shelves
                    .Include(s => s.Zone)
                        .ThenInclude(z => z.Warehouse)
                    .Include(s => s.StorageLocations)
                    .Where(s => s.ShelfType == shelfType);

                if (zoneId.HasValue)
                {
                    query = query.Where(s => s.ZoneId == zoneId.Value);
                }

                var shelves = await query
                    .OrderBy(s => s.Zone.WarehouseId)
                    .ThenBy(s => s.ZoneId)
                    .ThenBy(s => s.Code)
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} shelves of type {ShelfType}", 
                    shelves.Count, shelfType);
                return Ok(shelves);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving shelves by type {ShelfType}", shelfType);
                return StatusCode(500, "Internal server error while retrieving shelves");
            }
        }

        /// <summary>
        /// 创建新货架
        /// </summary>
        /// <param name="shelf">货架信息</param>
        /// <returns>创建的货架</returns>
        [HttpPost]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<ActionResult<Shelf>> CreateShelf(Shelf shelf)
        {
            try
            {
                // 检查区域是否存在
                var zone = await _context.Zones.FindAsync(shelf.ZoneId);
                if (zone == null)
                {
                    return BadRequest($"Zone with ID {shelf.ZoneId} not found");
                }

                // 检查同一区域内编码是否已存在
                var existingShelf = await _context.Shelves
                    .FirstOrDefaultAsync(s => s.Code == shelf.Code && s.ZoneId == shelf.ZoneId);

                if (existingShelf != null)
                {
                    return BadRequest($"Shelf with code '{shelf.Code}' already exists in zone {shelf.ZoneId}");
                }

                // 设置创建时间
                shelf.CreatedAt = DateTime.UtcNow;
                shelf.UpdatedAt = DateTime.UtcNow;

                _context.Shelves.Add(shelf);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created new shelf {Id}: {Name} with code {Code} in zone {ZoneId}", 
                    shelf.Id, shelf.Name, shelf.Code, shelf.ZoneId);

                return CreatedAtAction(nameof(GetShelf), new { id = shelf.Id }, shelf);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating shelf");
                return StatusCode(500, "Internal server error while creating shelf");
            }
        }

        /// <summary>
        /// 更新货架信息
        /// </summary>
        /// <param name="id">货架ID</param>
        /// <param name="shelf">更新的货架信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<IActionResult> UpdateShelf(int id, Shelf shelf)
        {
            if (id != shelf.Id)
            {
                return BadRequest("Shelf ID mismatch");
            }

            try
            {
                var existingShelf = await _context.Shelves.FindAsync(id);
                if (existingShelf == null)
                {
                    return NotFound($"Shelf with ID {id} not found");
                }

                // 检查区域是否存在
                var zone = await _context.Zones.FindAsync(shelf.ZoneId);
                if (zone == null)
                {
                    return BadRequest($"Zone with ID {shelf.ZoneId} not found");
                }

                // 检查编码冲突（排除自己）
                var duplicateCode = await _context.Shelves
                    .AnyAsync(s => s.Code == shelf.Code && s.ZoneId == shelf.ZoneId && s.Id != id);

                if (duplicateCode)
                {
                    return BadRequest($"Shelf with code '{shelf.Code}' already exists in zone {shelf.ZoneId}");
                }

                // 更新字段
                existingShelf.Code = shelf.Code;
                existingShelf.Name = shelf.Name;
                existingShelf.Description = shelf.Description;
                existingShelf.ZoneId = shelf.ZoneId;
                existingShelf.ShelfType = shelf.ShelfType;
                existingShelf.Status = shelf.Status;
                existingShelf.Length = shelf.Length;
                existingShelf.Width = shelf.Width;
                existingShelf.Height = shelf.Height;
                existingShelf.Levels = shelf.Levels;
                existingShelf.PositionsPerLevel = shelf.PositionsPerLevel;
                existingShelf.MaxWeight = shelf.MaxWeight;
                existingShelf.MaxWeightPerPosition = shelf.MaxWeightPerPosition;
                existingShelf.Material = shelf.Material;
                existingShelf.Manufacturer = shelf.Manufacturer;
                existingShelf.ManufactureDate = shelf.ManufactureDate;
                existingShelf.InstallationDate = shelf.InstallationDate;
                existingShelf.LastInspectionDate = shelf.LastInspectionDate;
                existingShelf.NextInspectionDate = shelf.NextInspectionDate;
                existingShelf.SafetyCertifications = shelf.SafetyCertifications;
                existingShelf.StoragePolicy = shelf.StoragePolicy;
                existingShelf.AccessRestrictions = shelf.AccessRestrictions;
                existingShelf.Configuration = shelf.Configuration;
                existingShelf.ResponsiblePerson = shelf.ResponsiblePerson;
                existingShelf.Remarks = shelf.Remarks;
                existingShelf.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated shelf {Id}: {Name}", id, shelf.Name);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating shelf {Id}", id);
                return StatusCode(500, "Internal server error while updating shelf");
            }
        }

        /// <summary>
        /// 删除货架
        /// </summary>
        /// <param name="id">货架ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        [Authorize(Policy = PolicyConstants.SystemConfigPolicy)]
        public async Task<IActionResult> DeleteShelf(int id)
        {
            try
            {
                var shelf = await _context.Shelves
                    .Include(s => s.StorageLocations)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (shelf == null)
                {
                    return NotFound($"Shelf with ID {id} not found");
                }

                // 检查是否有关联的储位
                if (shelf.StorageLocations.Any())
                {
                    return BadRequest("Cannot delete shelf with existing storage locations. Please delete storage locations first.");
                }

                _context.Shelves.Remove(shelf);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted shelf {Id}: {Name}", id, shelf.Name);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting shelf {Id}", id);
                return StatusCode(500, "Internal server error while deleting shelf");
            }
        }

        /// <summary>
        /// 获取货架统计信息
        /// </summary>
        /// <param name="id">货架ID</param>
        /// <returns>统计信息</returns>
        [HttpGet("{id}/statistics")]
        public async Task<ActionResult<object>> GetShelfStatistics(int id)
        {
            try
            {
                var shelf = await _context.Shelves
                    .Include(s => s.Zone)
                        .ThenInclude(z => z.Warehouse)
                    .Include(s => s.StorageLocations)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (shelf == null)
                {
                    return NotFound($"Shelf with ID {id} not found");
                }

                var statistics = new
                {
                    ShelfId = id,
                    ShelfName = shelf.Name,
                    ShelfCode = shelf.Code,
                    ShelfType = shelf.ShelfType.ToString(),
                    ZoneName = shelf.Zone.Name,
                    WarehouseName = shelf.Zone.Warehouse.Name,
                    TotalStorageLocations = shelf.StorageLocations.Count,
                    AvailableStorageLocations = shelf.StorageLocations
                        .Count(sl => sl.Status == StorageLocationStatus.Available),
                    OccupiedStorageLocations = shelf.StorageLocations
                        .Count(sl => sl.Status == StorageLocationStatus.Occupied),
                    MaintenanceStorageLocations = shelf.StorageLocations
                        .Count(sl => sl.Status == StorageLocationStatus.Maintenance),
                    UtilizationRate = shelf.StorageLocations.Any() ? 
                        shelf.StorageLocations.Count(sl => sl.Status == StorageLocationStatus.Occupied) / 
                        (double)shelf.StorageLocations.Count : 0,
                    PhysicalInfo = new
                    {
                        Length = shelf.Length,
                        Width = shelf.Width,
                        Height = shelf.Height,
                        Levels = shelf.Levels,
                        PositionsPerLevel = shelf.PositionsPerLevel,
                        MaxWeight = shelf.MaxWeight,
                        MaxWeightPerPosition = shelf.MaxWeightPerPosition
                    }
                };

                _logger.LogInformation("Retrieved statistics for shelf {Id}", id);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving shelf statistics {Id}", id);
                return StatusCode(500, "Internal server error while retrieving shelf statistics");
            }
        }

        /// <summary>
        /// 记录货架检查（简化版）
        /// </summary>
        /// <param name="id">货架ID</param>
        /// <param name="inspectionData">检查数据</param>
        /// <returns>检查结果</returns>
        [HttpPost("{id}/inspection")]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<IActionResult> RecordShelfInspection(int id, [FromBody] JsonElement inspectionData)
        {
            string? nextInspectionDateStr = null; // 声明在方法顶部以确保作用域正确
            
            try
            {
                _logger.LogInformation("Starting inspection record for shelf {Id}", id);
                
                var shelf = await _context.Shelves.FindAsync(id);
                if (shelf == null)
                {
                    _logger.LogWarning("Shelf with ID {Id} not found", id);
                    return NotFound($"Shelf with ID {id} not found");
                }

                _logger.LogInformation("Found shelf {Id}: {Name}", id, shelf.Name);

                // 只更新检查日期，不处理复杂的备注逻辑
                shelf.LastInspectionDate = DateTime.UtcNow;
                
                // 如果提供了下次检查日期，则更新
                if (inspectionData.TryGetProperty("nextInspectionDate", out var nextInspectionDateElement))
                {
                    try
                    {
                        nextInspectionDateStr = nextInspectionDateElement.GetString();
                        if (!string.IsNullOrEmpty(nextInspectionDateStr))
                        {
                            if (DateTime.TryParse(nextInspectionDateStr, out var parsedDate))
                            {
                                // 确保使用UTC时间
                                shelf.NextInspectionDate = parsedDate.Kind == DateTimeKind.Utc ? 
                                    parsedDate : 
                                    DateTime.SpecifyKind(parsedDate, DateTimeKind.Utc);
                                _logger.LogInformation("Set next inspection date to {Date}", shelf.NextInspectionDate);
                            }
                        }
                    }
                    catch (Exception parseEx)
                    {
                        _logger.LogWarning(parseEx, "Failed to parse nextInspectionDate: {DateString}", nextInspectionDateStr ?? "null");
                    }
                }

                shelf.UpdatedAt = DateTime.UtcNow;
                
                _logger.LogInformation("Attempting to save changes for shelf {Id}", id);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully recorded inspection for shelf {Id}", id);
                return Ok(new { 
                    Message = "Inspection recorded successfully", 
                    InspectionTime = DateTime.UtcNow,
                    ShelfId = id,
                    ShelfName = shelf.Name
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording inspection for shelf {Id}. Inner exception: {InnerException}. NextInspectionDateStr: {DateStr}", 
                    id, ex.InnerException?.Message ?? "No inner exception", nextInspectionDateStr ?? "null");
                
                // Return more detailed error information
                var errorDetails = new
                {
                    Message = "Internal server error while recording inspection",
                    Error = ex.Message,
                    InnerError = ex.InnerException?.Message,
                    StackTrace = ex.StackTrace,
                    InputData = new
                    {
                        ShelfId = id,
                        NextInspectionDateString = nextInspectionDateStr
                    }
                };
                
                return StatusCode(500, errorDetails);
            }
        }

        /// <summary>
        /// 获取货架配置
        /// </summary>
        /// <param name="id">货架ID</param>
        /// <returns>货架配置</returns>
        [HttpGet("{id}/configuration")]
        public async Task<ActionResult<object>> GetShelfConfiguration(int id)
        {
            try
            {
                var shelf = await _context.Shelves.FindAsync(id);
                if (shelf == null)
                {
                    return NotFound($"Shelf with ID {id} not found");
                }

                return Ok(new { 
                    ShelfId = id,
                    Configuration = shelf.Configuration,
                    StoragePolicy = shelf.StoragePolicy,
                    AccessRestrictions = shelf.AccessRestrictions,
                    SafetyCertifications = shelf.SafetyCertifications
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving shelf configuration {Id}", id);
                return StatusCode(500, "Internal server error while retrieving shelf configuration");
            }
        }

        /// <summary>
        /// 更新货架配置
        /// </summary>
        /// <param name="id">货架ID</param>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}/configuration")]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<IActionResult> UpdateShelfConfiguration(int id, [FromBody] string configuration)
        {
            try
            {
                var shelf = await _context.Shelves.FindAsync(id);
                if (shelf == null)
                {
                    return NotFound($"Shelf with ID {id} not found");
                }

                shelf.Configuration = configuration;
                shelf.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated configuration for shelf {Id}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating shelf configuration {Id}", id);
                return StatusCode(500, "Internal server error while updating shelf configuration");
            }
        }
    }
}