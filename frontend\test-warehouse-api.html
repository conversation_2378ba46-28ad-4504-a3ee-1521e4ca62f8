<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库API测试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .button { padding: 10px 20px; margin: 5px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #40a9ff; }
        .button.danger { background: #ff4d4f; }
        .button.danger:hover { background: #ff7875; }
        .error { color: #ff4d4f; background: #fff2f0; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { color: #52c41a; background: #f6ffed; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #1890ff; background: #e6f7ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        input, select, textarea { padding: 8px; margin: 5px; border: 1px solid #d9d9d9; border-radius: 4px; width: 200px; }
        .form-row { display: flex; align-items: center; margin: 10px 0; }
        .form-row label { width: 120px; display: inline-block; }
        .status { padding: 5px 10px; border-radius: 4px; color: white; font-weight: bold; }
        .status.online { background: #52c41a; }
        .status.offline { background: #ff4d4f; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 仓库API测试工具</h1>
        
        <div class="section">
            <h2>📊 系统状态</h2>
            <div class="form-row">
                <label>API服务:</label>
                <span id="apiStatus" class="status offline">检测中...</span>
            </div>
            <div class="form-row">
                <label>认证状态:</label>
                <span id="authStatus" class="status offline">未认证</span>
            </div>
            <div class="form-row">
                <label>当前用户:</label>
                <span id="currentUser">未知</span>
            </div>
        </div>

        <div class="section">
            <h2>🔐 认证测试</h2>
            <div class="form-row">
                <label>邮箱:</label>
                <input type="email" id="loginEmail" value="<EMAIL>" />
            </div>
            <div class="form-row">
                <label>密码:</label>
                <input type="password" id="loginPassword" value="Admin@123456" />
            </div>
            <button class="button" onclick="testLogin()">登录测试</button>
            <button class="button danger" onclick="clearAuth()">清除认证</button>
            <div id="authResult"></div>
        </div>

        <div class="section">
            <h2>📦 仓库API测试</h2>
            
            <h3>获取仓库列表</h3>
            <button class="button" onclick="testGetWarehouses()">GET /api/warehouses</button>
            <div id="getWarehousesResult"></div>

            <h3>创建仓库</h3>
            <div class="form-row">
                <label>仓库编码:</label>
                <input type="text" id="warehouseCode" value="TEST001" />
            </div>
            <div class="form-row">
                <label>仓库名称:</label>
                <input type="text" id="warehouseName" value="测试仓库" />
            </div>
            <div class="form-row">
                <label>仓库类型:</label>
                <select id="warehouseType">
                    <option value="distribution">配送中心</option>
                    <option value="storage">存储仓库</option>
                    <option value="production">生产仓库</option>
                </select>
            </div>
            <div class="form-row">
                <label>状态:</label>
                <select id="warehouseStatus">
                    <option value="Active">运营中</option>
                    <option value="Maintenance">维护中</option>
                    <option value="Inactive">已停用</option>
                </select>
            </div>
            <button class="button" onclick="testCreateWarehouse()">POST /api/warehouses</button>
            <div id="createWarehouseResult"></div>
        </div>

        <div class="section">
            <h2>📋 请求日志</h2>
            <button class="button" onclick="clearLogs()">清空日志</button>
            <pre id="requestLogs"></pre>
        </div>
    </div>

    <script>
        let logs = [];
        const API_BASE = 'http://localhost:8080/api';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logElement = document.getElementById('requestLogs');
            logElement.textContent = logs.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLogs() {
            logs = [];
            document.getElementById('requestLogs').textContent = '';
        }

        function getAuthHeaders() {
            const token = localStorage.getItem('accessToken');
            return token ? { 'Authorization': `Bearer ${token}` } : {};
        }

        async function makeRequest(url, options = {}) {
            const fullUrl = `${API_BASE}${url}`;
            
            log(`🚀 发起请求: ${options.method || 'GET'} ${fullUrl}`);
            
            const headers = {
                'Content-Type': 'application/json',
                ...getAuthHeaders(),
                ...options.headers
            };
            
            log(`📤 请求头: ${JSON.stringify(headers, null, 2)}`);
            
            if (options.body) {
                log(`📤 请求体: ${options.body}`);
            }
            
            try {
                const response = await fetch(fullUrl, {
                    ...options,
                    headers
                });
                
                log(`📥 响应状态: ${response.status} ${response.statusText}`);
                
                const responseText = await response.text();
                log(`📥 响应体: ${responseText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }
                
                return responseText ? JSON.parse(responseText) : null;
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
                throw error;
            }
        }

        async function checkApiStatus() {
            try {
                await fetch(`${API_BASE}/health`, { method: 'GET' });
                document.getElementById('apiStatus').textContent = '在线';
                document.getElementById('apiStatus').className = 'status online';
            } catch (error) {
                document.getElementById('apiStatus').textContent = '离线';
                document.getElementById('apiStatus').className = 'status offline';
            }
        }

        function checkAuthStatus() {
            const token = localStorage.getItem('accessToken');
            if (token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    document.getElementById('authStatus').textContent = '已认证';
                    document.getElementById('authStatus').className = 'status online';
                    document.getElementById('currentUser').textContent = payload.email || payload.sub || '未知';
                } catch (error) {
                    document.getElementById('authStatus').textContent = 'Token无效';
                    document.getElementById('authStatus').className = 'status offline';
                }
            } else {
                document.getElementById('authStatus').textContent = '未认证';
                document.getElementById('authStatus').className = 'status offline';
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const resultDiv = document.getElementById('authResult');
            
            resultDiv.innerHTML = '<div class="info">正在登录...</div>';
            
            try {
                const response = await makeRequest('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({ email, password })
                });
                
                if (response.accessToken) {
                    localStorage.setItem('accessToken', response.accessToken);
                    localStorage.setItem('refreshToken', response.refreshToken);
                    resultDiv.innerHTML = '<div class="success">✅ 登录成功</div>';
                    checkAuthStatus();
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 登录失败：未返回token</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 登录失败: ${error.message}</div>`;
            }
        }

        function clearAuth() {
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            document.getElementById('authResult').innerHTML = '<div class="info">认证信息已清除</div>';
            checkAuthStatus();
        }

        async function testGetWarehouses() {
            const resultDiv = document.getElementById('getWarehousesResult');
            resultDiv.innerHTML = '<div class="info">正在获取仓库列表...</div>';
            
            try {
                const data = await makeRequest('/warehouses');
                resultDiv.innerHTML = `<div class="success">✅ 获取成功</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 获取失败: ${error.message}</div>`;
            }
        }

        async function testCreateWarehouse() {
            const resultDiv = document.getElementById('createWarehouseResult');
            resultDiv.innerHTML = '<div class="info">正在创建仓库...</div>';
            
            const warehouseData = {
                code: document.getElementById('warehouseCode').value,
                name: document.getElementById('warehouseName').value,
                warehouseType: document.getElementById('warehouseType').value,
                status: document.getElementById('warehouseStatus').value,
                description: "通过测试工具创建的仓库",
                address: "测试地址123号",
                contactPerson: "测试联系人",
                contactPhone: "13800138000",
                totalArea: 1000.0,
                totalVolume: 3000.0,
                configuration: "{}"
            };
            
            try {
                const data = await makeRequest('/warehouses', {
                    method: 'POST',
                    body: JSON.stringify(warehouseData)
                });
                resultDiv.innerHTML = `<div class="success">✅ 创建成功</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 创建失败: ${error.message}</div>`;
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            checkApiStatus();
            checkAuthStatus();
            
            // 每30秒检查一次API状态
            setInterval(checkApiStatus, 30000);
        };
    </script>
</body>
</html>
