using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Constants;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class WarehousesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WarehousesController> _logger;

        public WarehousesController(
            ApplicationDbContext context,
            ILogger<WarehousesController> logger
        )
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有仓库列表
        /// </summary>
        /// <returns>仓库列表</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Warehouse>>> GetWarehouses()
        {
            try
            {
                var warehouses = await _context
                    .Warehouses.Include(w => w.Zones)
                    .OrderBy(w => w.Code)
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} warehouses", warehouses.Count);
                return Ok(warehouses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving warehouses");
                return StatusCode(500, "Internal server error while retrieving warehouses");
            }
        }

        /// <summary>
        /// 获取指定仓库详情
        /// </summary>
        /// <param name="id">仓库ID</param>
        /// <returns>仓库详情</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<Warehouse>> GetWarehouse(int id)
        {
            try
            {
                var warehouse = await _context
                    .Warehouses.Include(w => w.Zones)
                    .ThenInclude(z => z.Shelves)
                    .ThenInclude(s => s.StorageLocations)
                    .FirstOrDefaultAsync(w => w.Id == id);

                if (warehouse == null)
                {
                    _logger.LogWarning("Warehouse with ID {Id} not found", id);
                    return NotFound($"Warehouse with ID {id} not found");
                }

                _logger.LogInformation("Retrieved warehouse {Id}: {Name}", id, warehouse.Name);
                return Ok(warehouse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving warehouse {Id}", id);
                return StatusCode(500, "Internal server error while retrieving warehouse");
            }
        }

        /// <summary>
        /// 创建新仓库
        /// </summary>
        /// <param name="warehouse">仓库信息</param>
        /// <returns>创建的仓库</returns>
        [HttpPost]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<ActionResult<Warehouse>> CreateWarehouse(Warehouse warehouse)
        {
            try
            {
                // 检查编码是否已存在
                var existingWarehouse = await _context.Warehouses.FirstOrDefaultAsync(w =>
                    w.Code == warehouse.Code
                );

                if (existingWarehouse != null)
                {
                    return BadRequest($"Warehouse with code '{warehouse.Code}' already exists");
                }

                // 设置创建时间
                warehouse.CreatedAt = DateTime.UtcNow;
                warehouse.UpdatedAt = DateTime.UtcNow;

                _context.Warehouses.Add(warehouse);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Created new warehouse {Id}: {Name} with code {Code}",
                    warehouse.Id,
                    warehouse.Name,
                    warehouse.Code
                );

                return CreatedAtAction(nameof(GetWarehouse), new { id = warehouse.Id }, warehouse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating warehouse");
                return StatusCode(500, "Internal server error while creating warehouse");
            }
        }

        /// <summary>
        /// 更新仓库信息
        /// </summary>
        /// <param name="id">仓库ID</param>
        /// <param name="warehouse">更新的仓库信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<IActionResult> UpdateWarehouse(int id, Warehouse warehouse)
        {
            if (id != warehouse.Id)
            {
                return BadRequest("Warehouse ID mismatch");
            }

            try
            {
                var existingWarehouse = await _context.Warehouses.FindAsync(id);
                if (existingWarehouse == null)
                {
                    return NotFound($"Warehouse with ID {id} not found");
                }

                // 检查编码冲突（排除自己）
                var duplicateCode = await _context.Warehouses.AnyAsync(w =>
                    w.Code == warehouse.Code && w.Id != id
                );

                if (duplicateCode)
                {
                    return BadRequest($"Warehouse with code '{warehouse.Code}' already exists");
                }

                // 更新字段
                existingWarehouse.Code = warehouse.Code;
                existingWarehouse.Name = warehouse.Name;
                existingWarehouse.Description = warehouse.Description;
                existingWarehouse.Address = warehouse.Address;
                existingWarehouse.WarehouseType = warehouse.WarehouseType;
                existingWarehouse.Status = warehouse.Status;
                existingWarehouse.TotalArea = warehouse.TotalArea;
                existingWarehouse.TotalVolume = warehouse.TotalVolume;
                existingWarehouse.ContactPerson = warehouse.ContactPerson;
                existingWarehouse.ContactPhone = warehouse.ContactPhone;
                existingWarehouse.Configuration = warehouse.Configuration;
                existingWarehouse.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated warehouse {Id}: {Name}", id, warehouse.Name);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating warehouse {Id}", id);
                return StatusCode(500, "Internal server error while updating warehouse");
            }
        }

        /// <summary>
        /// 删除仓库
        /// </summary>
        /// <param name="id">仓库ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        [Authorize(Policy = PolicyConstants.SystemConfigPolicy)]
        public async Task<IActionResult> DeleteWarehouse(int id)
        {
            try
            {
                var warehouse = await _context
                    .Warehouses.Include(w => w.Zones)
                    .FirstOrDefaultAsync(w => w.Id == id);

                if (warehouse == null)
                {
                    return NotFound($"Warehouse with ID {id} not found");
                }

                // 检查是否有关联的区域
                if (warehouse.Zones.Any())
                {
                    return BadRequest(
                        "Cannot delete warehouse with existing zones. Please delete zones first."
                    );
                }

                _context.Warehouses.Remove(warehouse);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted warehouse {Id}: {Name}", id, warehouse.Name);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting warehouse {Id}", id);
                return StatusCode(500, "Internal server error while deleting warehouse");
            }
        }

        /// <summary>
        /// 获取仓库统计信息
        /// </summary>
        /// <param name="id">仓库ID</param>
        /// <returns>统计信息</returns>
        [HttpGet("{id}/statistics")]
        public async Task<ActionResult<object>> GetWarehouseStatistics(int id)
        {
            try
            {
                var warehouse = await _context
                    .Warehouses.Include(w => w.Zones)
                    .ThenInclude(z => z.Shelves)
                    .ThenInclude(s => s.StorageLocations)
                    .FirstOrDefaultAsync(w => w.Id == id);

                if (warehouse == null)
                {
                    return NotFound($"Warehouse with ID {id} not found");
                }

                var statistics = new
                {
                    WarehouseId = id,
                    WarehouseName = warehouse.Name,
                    TotalZones = warehouse.Zones.Count,
                    TotalShelves = warehouse.Zones.Sum(z => z.Shelves.Count),
                    TotalStorageLocations = warehouse.Zones.Sum(z =>
                        z.Shelves.Sum(s => s.StorageLocations.Count)
                    ),
                    AvailableStorageLocations = warehouse.Zones.Sum(z =>
                        z.Shelves.Sum(s =>
                            s.StorageLocations.Count(sl =>
                                sl.Status == StorageLocationStatus.Available
                            )
                        )
                    ),
                    UtilizationRate = warehouse.Zones.Any()
                        ? warehouse
                            .Zones.SelectMany(z => z.Shelves)
                            .SelectMany(s => s.StorageLocations)
                            .Count(sl => sl.Status == StorageLocationStatus.Occupied)
                            / (double)
                                warehouse
                                    .Zones.SelectMany(z => z.Shelves)
                                    .SelectMany(s => s.StorageLocations)
                                    .Count()
                        : 0,
                    ZoneBreakdown = warehouse
                        .Zones.Select(z => new
                        {
                            ZoneId = z.Id,
                            ZoneName = z.Name,
                            ZoneCode = z.Code,
                            ZoneType = z.ZoneType.ToString(),
                            Shelves = z.Shelves.Count,
                            StorageLocations = z.Shelves.Sum(s => s.StorageLocations.Count),
                            AvailableLocations = z.Shelves.Sum(s =>
                                s.StorageLocations.Count(sl =>
                                    sl.Status == StorageLocationStatus.Available
                                )
                            ),
                        })
                        .ToList(),
                };

                _logger.LogInformation("Retrieved statistics for warehouse {Id}", id);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving warehouse statistics {Id}", id);
                return StatusCode(
                    500,
                    "Internal server error while retrieving warehouse statistics"
                );
            }
        }

        /// <summary>
        /// 获取仓库配置
        /// </summary>
        /// <param name="id">仓库ID</param>
        /// <returns>仓库配置</returns>
        [HttpGet("{id}/configuration")]
        public async Task<ActionResult<object>> GetWarehouseConfiguration(int id)
        {
            try
            {
                var warehouse = await _context.Warehouses.FindAsync(id);
                if (warehouse == null)
                {
                    return NotFound($"Warehouse with ID {id} not found");
                }

                return Ok(new { WarehouseId = id, Configuration = warehouse.Configuration });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving warehouse configuration {Id}", id);
                return StatusCode(
                    500,
                    "Internal server error while retrieving warehouse configuration"
                );
            }
        }

        /// <summary>
        /// 更新仓库配置
        /// </summary>
        /// <param name="id">仓库ID</param>
        /// <param name="configuration">新配置</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}/configuration")]
        [Authorize(Policy = PolicyConstants.ESP32ManagementPolicy)]
        public async Task<IActionResult> UpdateWarehouseConfiguration(
            int id,
            [FromBody] string configuration
        )
        {
            try
            {
                var warehouse = await _context.Warehouses.FindAsync(id);
                if (warehouse == null)
                {
                    return NotFound($"Warehouse with ID {id} not found");
                }

                warehouse.Configuration = configuration;
                warehouse.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated configuration for warehouse {Id}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating warehouse configuration {Id}", id);
                return StatusCode(
                    500,
                    "Internal server error while updating warehouse configuration"
                );
            }
        }
    }
}
