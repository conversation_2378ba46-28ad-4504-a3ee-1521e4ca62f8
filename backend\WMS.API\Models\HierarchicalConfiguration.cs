using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WMS.API.Models
{
    /// <summary>
    /// 配置层级枚举
    /// </summary>
    public enum ConfigLevel
    {
        /// <summary>
        /// 系统级配置
        /// </summary>
        SYSTEM,

        /// <summary>
        /// 客户级配置
        /// </summary>
        CLIENT,

        /// <summary>
        /// 区域级配置
        /// </summary>
        ZONE,

        /// <summary>
        /// 货架级配置
        /// </summary>
        SHELF,

        /// <summary>
        /// 储位级配置
        /// </summary>
        LOCATION,

        /// <summary>
        /// 物料类别级配置
        /// </summary>
        MATERIAL_CATEGORY,
    }

    /// <summary>
    /// 分层配置表
    /// </summary>
    public class HierarchicalConfiguration
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 配置层级
        /// </summary>
        public ConfigLevel ConfigLevel { get; set; }

        /// <summary>
        /// 目标类型（ZONE_CODE, SHELF_CODE, LOCATION_CODE, MATERIAL_CATEGORY）
        /// </summary>
        [StringLength(50)]
        public string? TargetType { get; set; }

        /// <summary>
        /// 具体的目标对象标识
        /// </summary>
        [StringLength(100)]
        public string? TargetId { get; set; }

        /// <summary>
        /// 配置键（uniqueCodeControl, storageLocationPolicy等）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ConfigKey { get; set; } = string.Empty;

        /// <summary>
        /// 配置值（JSON格式）
        /// </summary>
        [Required]
        [Column(TypeName = "jsonb")]
        public string ConfigValue { get; set; } = "{}";

        /// <summary>
        /// 优先级：1=最高，100=最低
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 生效开始时间
        /// </summary>
        public DateTime EffectiveFrom { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 生效结束时间
        /// </summary>
        public DateTime? EffectiveTo { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }
    }
}
