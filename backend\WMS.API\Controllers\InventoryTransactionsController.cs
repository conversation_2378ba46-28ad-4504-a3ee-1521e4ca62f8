using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class InventoryTransactionsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<InventoryTransactionsController> _logger;

        public InventoryTransactionsController(ApplicationDbContext context, ILogger<InventoryTransactionsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有库存事务
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetTransactions(
            int page = 1,
            int pageSize = 50,
            TransactionType? type = null,
            TransactionStatus? status = null)
        {
            try
            {
                var query = _context.InventoryTransactions
                    .Include(t => t.Material)
                    .Include(t => t.StorageLocation)
                    .Include(t => t.TargetStorageLocation)
                    .AsQueryable();

                if (type.HasValue)
                {
                    query = query.Where(t => t.Type == type.Value);
                }

                if (status.HasValue)
                {
                    query = query.Where(t => t.Status == status.Value);
                }

                var totalCount = await query.CountAsync();
                var transactions = await query
                    .OrderByDescending(t => t.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(t => new
                    {
                        t.Id,
                        t.TransactionNumber,
                        t.Type,
                        t.Status,
                        t.StorageLocationId,
                        StorageLocation = new
                        {
                            t.StorageLocation!.Id,
                            t.StorageLocation.Code,
                            t.StorageLocation.Name
                        },
                        t.MaterialId,
                        Material = new
                        {
                            t.Material!.Id,
                            t.Material.Sku,
                            t.Material.Name,
                            t.Material.Unit
                        },
                        t.Quantity,
                        t.BeforeQuantity,
                        t.AfterQuantity,
                        t.BatchNumber,
                        t.LpnCode,
                        t.UniqueCode,
                        t.ExpiryDate,
                        t.InboundOrderId,
                        t.OutboundOrderId,
                        t.TargetStorageLocationId,
                        TargetStorageLocation = t.TargetStorageLocation != null ? new
                        {
                            t.TargetStorageLocation.Id,
                            t.TargetStorageLocation.Code,
                            t.TargetStorageLocation.Name
                        } : null,
                        t.Reason,
                        t.Remarks,
                        t.OperatedBy,
                        t.OperatedAt,
                        t.ApprovedBy,
                        t.ApprovedAt,
                        t.CreatedAt,
                        t.UpdatedAt,
                        t.BarcodeData,
                        t.ParsedBy,
                        t.ParsedAt,
                        t.LedActivated,
                        t.LedActivatedAt,
                        t.LedColor
                    })
                    .ToListAsync();

                var result = new
                {
                    Data = transactions,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存事务列表失败");
                return StatusCode(500, "获取库存事务列表失败");
            }
        }

        /// <summary>
        /// 根据ID获取库存事务
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetTransaction(int id)
        {
            try
            {
                var transaction = await _context.InventoryTransactions
                    .Include(t => t.Material)
                    .Include(t => t.StorageLocation)
                    .Include(t => t.TargetStorageLocation)
                    .Include(t => t.InboundOrder)
                    .Include(t => t.OutboundOrder)
                    .Where(t => t.Id == id)
                    .Select(t => new
                    {
                        t.Id,
                        t.TransactionNumber,
                        t.Type,
                        t.Status,
                        t.StorageLocationId,
                        StorageLocation = new
                        {
                            t.StorageLocation!.Id,
                            t.StorageLocation.Code,
                            t.StorageLocation.Name
                        },
                        t.MaterialId,
                        Material = new
                        {
                            t.Material!.Id,
                            t.Material.Sku,
                            t.Material.Name,
                            t.Material.Unit
                        },
                        t.Quantity,
                        t.BeforeQuantity,
                        t.AfterQuantity,
                        t.BatchNumber,
                        t.LpnCode,
                        t.UniqueCode,
                        t.ExpiryDate,
                        t.InboundOrderId,
                        InboundOrder = t.InboundOrder != null ? new
                        {
                            t.InboundOrder.Id,
                            t.InboundOrder.OrderNumber,
                            t.InboundOrder.Status
                        } : null,
                        t.OutboundOrderId,
                        OutboundOrder = t.OutboundOrder != null ? new
                        {
                            t.OutboundOrder.Id,
                            t.OutboundOrder.OrderNumber,
                            t.OutboundOrder.Status
                        } : null,
                        t.TargetStorageLocationId,
                        TargetStorageLocation = t.TargetStorageLocation != null ? new
                        {
                            t.TargetStorageLocation.Id,
                            t.TargetStorageLocation.Code,
                            t.TargetStorageLocation.Name
                        } : null,
                        t.Reason,
                        t.Remarks,
                        t.OperatedBy,
                        t.OperatedAt,
                        t.ApprovedBy,
                        t.ApprovedAt,
                        t.CreatedAt,
                        t.UpdatedAt,
                        t.BarcodeData,
                        t.ParsedBy,
                        t.ParsedAt,
                        t.LedActivated,
                        t.LedActivatedAt,
                        t.LedColor
                    })
                    .FirstOrDefaultAsync();

                if (transaction == null)
                {
                    return NotFound("库存事务不存在");
                }

                return Ok(transaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存事务详情失败，ID: {Id}", id);
                return StatusCode(500, "获取库存事务详情失败");
            }
        }

        /// <summary>
        /// 根据事务类型获取事务列表
        /// </summary>
        [HttpGet("by-type/{type}")]
        public async Task<ActionResult<IEnumerable<object>>> GetTransactionsByType(TransactionType type)
        {
            try
            {
                var transactions = await _context.InventoryTransactions
                    .Include(t => t.Material)
                    .Include(t => t.StorageLocation)
                    .Where(t => t.Type == type)
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new
                    {
                        t.Id,
                        t.TransactionNumber,
                        t.Type,
                        t.Status,
                        t.StorageLocationId,
                        StorageLocation = new
                        {
                            t.StorageLocation!.Id,
                            t.StorageLocation.Code,
                            t.StorageLocation.Name
                        },
                        t.MaterialId,
                        Material = new
                        {
                            t.Material!.Id,
                            t.Material.Sku,
                            t.Material.Name,
                            t.Material.Unit
                        },
                        t.Quantity,
                        t.BatchNumber,
                        t.LpnCode,
                        t.UniqueCode,
                        t.CreatedAt,
                        t.OperatedBy,
                        t.OperatedAt
                    })
                    .Take(100)
                    .ToListAsync();

                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据类型获取库存事务失败，Type: {Type}", type);
                return StatusCode(500, "获取库存事务失败");
            }
        }

        /// <summary>
        /// 根据物料ID获取事务历史
        /// </summary>
        [HttpGet("by-material/{materialId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetTransactionsByMaterial(int materialId)
        {
            try
            {
                var transactions = await _context.InventoryTransactions
                    .Include(t => t.Material)
                    .Include(t => t.StorageLocation)
                    .Where(t => t.MaterialId == materialId)
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new
                    {
                        t.Id,
                        t.TransactionNumber,
                        t.Type,
                        t.Status,
                        t.StorageLocationId,
                        StorageLocation = new
                        {
                            t.StorageLocation!.Id,
                            t.StorageLocation.Code,
                            t.StorageLocation.Name
                        },
                        t.Quantity,
                        t.BeforeQuantity,
                        t.AfterQuantity,
                        t.BatchNumber,
                        t.LpnCode,
                        t.UniqueCode,
                        t.CreatedAt,
                        t.OperatedBy,
                        t.OperatedAt
                    })
                    .Take(100)
                    .ToListAsync();

                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据物料ID获取库存事务失败，MaterialId: {MaterialId}", materialId);
                return StatusCode(500, "获取库存事务失败");
            }
        }

        /// <summary>
        /// 根据储位ID获取事务历史
        /// </summary>
        [HttpGet("by-location/{locationId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetTransactionsByLocation(int locationId)
        {
            try
            {
                var transactions = await _context.InventoryTransactions
                    .Include(t => t.Material)
                    .Include(t => t.StorageLocation)
                    .Where(t => t.StorageLocationId == locationId || t.TargetStorageLocationId == locationId)
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new
                    {
                        t.Id,
                        t.TransactionNumber,
                        t.Type,
                        t.Status,
                        t.StorageLocationId,
                        StorageLocation = new
                        {
                            t.StorageLocation!.Id,
                            t.StorageLocation.Code,
                            t.StorageLocation.Name
                        },
                        t.MaterialId,
                        Material = new
                        {
                            t.Material!.Id,
                            t.Material.Sku,
                            t.Material.Name,
                            t.Material.Unit
                        },
                        t.Quantity,
                        t.BeforeQuantity,
                        t.AfterQuantity,
                        t.BatchNumber,
                        t.LpnCode,
                        t.UniqueCode,
                        t.CreatedAt,
                        t.OperatedBy,
                        t.OperatedAt
                    })
                    .Take(100)
                    .ToListAsync();

                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据储位ID获取库存事务失败，LocationId: {LocationId}", locationId);
                return StatusCode(500, "获取库存事务失败");
            }
        }

        /// <summary>
        /// 根据唯一码获取事务历史
        /// </summary>
        [HttpGet("by-unique-code/{uniqueCode}")]
        public async Task<ActionResult<IEnumerable<object>>> GetTransactionsByUniqueCode(string uniqueCode)
        {
            try
            {
                var transactions = await _context.InventoryTransactions
                    .Include(t => t.Material)
                    .Include(t => t.StorageLocation)
                    .Where(t => t.UniqueCode == uniqueCode)
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new
                    {
                        t.Id,
                        t.TransactionNumber,
                        t.Type,
                        t.Status,
                        t.StorageLocationId,
                        StorageLocation = new
                        {
                            t.StorageLocation!.Id,
                            t.StorageLocation.Code,
                            t.StorageLocation.Name
                        },
                        t.MaterialId,
                        Material = new
                        {
                            t.Material!.Id,
                            t.Material.Sku,
                            t.Material.Name,
                            t.Material.Unit
                        },
                        t.Quantity,
                        t.BeforeQuantity,
                        t.AfterQuantity,
                        t.BatchNumber,
                        t.LpnCode,
                        t.UniqueCode,
                        t.ExpiryDate,
                        t.InboundOrderId,
                        t.OutboundOrderId,
                        t.CreatedAt,
                        t.OperatedBy,
                        t.OperatedAt,
                        t.BarcodeData
                    })
                    .ToListAsync();

                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据唯一码获取库存事务失败，UniqueCode: {UniqueCode}", uniqueCode);
                return StatusCode(500, "获取库存事务失败");
            }
        }

        /// <summary>
        /// 根据批次号获取事务历史
        /// </summary>
        [HttpGet("by-batch/{batchNumber}")]
        public async Task<ActionResult<IEnumerable<object>>> GetTransactionsByBatch(string batchNumber)
        {
            try
            {
                var transactions = await _context.InventoryTransactions
                    .Include(t => t.Material)
                    .Include(t => t.StorageLocation)
                    .Where(t => t.BatchNumber == batchNumber)
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new
                    {
                        t.Id,
                        t.TransactionNumber,
                        t.Type,
                        t.Status,
                        t.StorageLocationId,
                        StorageLocation = new
                        {
                            t.StorageLocation!.Id,
                            t.StorageLocation.Code,
                            t.StorageLocation.Name
                        },
                        t.MaterialId,
                        Material = new
                        {
                            t.Material!.Id,
                            t.Material.Sku,
                            t.Material.Name,
                            t.Material.Unit
                        },
                        t.Quantity,
                        t.BeforeQuantity,
                        t.AfterQuantity,
                        t.BatchNumber,
                        t.LpnCode,
                        t.UniqueCode,
                        t.CreatedAt,
                        t.OperatedBy,
                        t.OperatedAt
                    })
                    .ToListAsync();

                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据批次号获取库存事务失败，BatchNumber: {BatchNumber}", batchNumber);
                return StatusCode(500, "获取库存事务失败");
            }
        }

        /// <summary>
        /// 获取事务统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<object>> GetTransactionStatistics()
        {
            try
            {
                var today = DateTime.UtcNow.Date;
                var thisMonth = new DateTime(today.Year, today.Month, 1);
                var thisYear = new DateTime(today.Year, 1, 1);

                var statistics = await _context.InventoryTransactions
                    .GroupBy(t => new { t.Type, t.Status })
                    .Select(g => new
                    {
                        Type = g.Key.Type,
                        Status = g.Key.Status,
                        Count = g.Count(),
                        TotalQuantity = g.Sum(t => Math.Abs(t.Quantity))
                    })
                    .ToListAsync();

                var todayCount = await _context.InventoryTransactions
                    .Where(t => t.CreatedAt >= today)
                    .CountAsync();

                var thisMonthCount = await _context.InventoryTransactions
                    .Where(t => t.CreatedAt >= thisMonth)
                    .CountAsync();

                var thisYearCount = await _context.InventoryTransactions
                    .Where(t => t.CreatedAt >= thisYear)
                    .CountAsync();

                var result = new
                {
                    TodayCount = todayCount,
                    ThisMonthCount = thisMonthCount,
                    ThisYearCount = thisYearCount,
                    TypeAndStatusStatistics = statistics
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取事务统计信息失败");
                return StatusCode(500, "获取事务统计信息失败");
            }
        }

        /// <summary>
        /// 搜索库存事务
        /// </summary>
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<object>>> SearchTransactions(
            string? transactionNumber = null,
            string? sku = null,
            string? batchNumber = null,
            string? uniqueCode = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            TransactionType? type = null,
            TransactionStatus? status = null)
        {
            try
            {
                var query = _context.InventoryTransactions
                    .Include(t => t.Material)
                    .Include(t => t.StorageLocation)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(transactionNumber))
                {
                    query = query.Where(t => t.TransactionNumber.Contains(transactionNumber));
                }

                if (!string.IsNullOrWhiteSpace(sku))
                {
                    query = query.Where(t => t.Material!.Sku.Contains(sku));
                }

                if (!string.IsNullOrWhiteSpace(batchNumber))
                {
                    query = query.Where(t => t.BatchNumber == batchNumber);
                }

                if (!string.IsNullOrWhiteSpace(uniqueCode))
                {
                    query = query.Where(t => t.UniqueCode == uniqueCode);
                }

                if (startDate.HasValue)
                {
                    query = query.Where(t => t.CreatedAt >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(t => t.CreatedAt <= endDate.Value);
                }

                if (type.HasValue)
                {
                    query = query.Where(t => t.Type == type.Value);
                }

                if (status.HasValue)
                {
                    query = query.Where(t => t.Status == status.Value);
                }

                var transactions = await query
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new
                    {
                        t.Id,
                        t.TransactionNumber,
                        t.Type,
                        t.Status,
                        t.StorageLocationId,
                        StorageLocation = new
                        {
                            t.StorageLocation!.Id,
                            t.StorageLocation.Code,
                            t.StorageLocation.Name
                        },
                        t.MaterialId,
                        Material = new
                        {
                            t.Material!.Id,
                            t.Material.Sku,
                            t.Material.Name,
                            t.Material.Unit
                        },
                        t.Quantity,
                        t.BatchNumber,
                        t.LpnCode,
                        t.UniqueCode,
                        t.CreatedAt,
                        t.OperatedBy,
                        t.OperatedAt
                    })
                    .Take(200)
                    .ToListAsync();

                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索库存事务失败");
                return StatusCode(500, "搜索库存事务失败");
            }
        }
    }
}