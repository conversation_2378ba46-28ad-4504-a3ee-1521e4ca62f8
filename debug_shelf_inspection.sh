#!/bin/bash

# 获取token
echo "=== 获取认证Token ==="
TOKEN_RESPONSE=$(curl -s -X POST "http://************:5000/api/Auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin@123456",
    "rememberMe": false
  }')

TOKEN=$(echo "$TOKEN_RESPONSE" < /dev/null | grep -o '"accessToken":"[^"]*"' | sed 's/"accessToken":"\(.*\)"/\1/')
echo "Token获取成功: ${TOKEN:0:20}..."

# 首先获取可用的货架列表
echo -e "\n=== 获取货架列表 ==="
SHELVES_RESPONSE=$(curl -s -X GET "http://************:5000/api/Shelves" \
  -H "Authorization: Bearer $TOKEN" \
  -w "HTTP_STATUS:%{http_code}")

SHELVES_HTTP_STATUS=$(echo "$SHELVES_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
SHELVES_BODY=$(echo "$SHELVES_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "HTTP状态码: $SHELVES_HTTP_STATUS"
echo "货架列表响应: $SHELVES_BODY"

# 如果没有货架，先创建一个
if [ "$SHELVES_HTTP_STATUS" = "200" ] && [ "$SHELVES_BODY" = "[]" ]; then
    echo -e "\n=== 没有货架，先创建一个 ==="
    
    # 先检查是否有区域，如果没有先创建区域
    ZONES_RESPONSE=$(curl -s -X GET "http://************:5000/api/Zones" \
      -H "Authorization: Bearer $TOKEN")
    
    if [ "$ZONES_RESPONSE" = "[]" ]; then
        echo "没有区域，先创建区域..."
        # 检查是否有仓库
        WAREHOUSES_RESPONSE=$(curl -s -X GET "http://************:5000/api/Warehouses" \
          -H "Authorization: Bearer $TOKEN")
        
        if [ "$WAREHOUSES_RESPONSE" = "[]" ]; then
            echo "没有仓库，先创建仓库..."
            CREATE_WAREHOUSE_RESPONSE=$(curl -s -X POST "http://************:5000/api/Warehouses" \
              -H "Content-Type: application/json" \
              -H "Authorization: Bearer $TOKEN" \
              -w "HTTP_STATUS:%{http_code}" \
              -d '{
                "code": "TEST-WH",
                "name": "测试仓库",
                "description": "用于测试的仓库",
                "address": "测试地址",
                "warehouseType": "配送中心",
                "status": 0,
                "totalArea": 1000.0,
                "totalVolume": 3000.0,
                "contactPerson": "测试人员",
                "contactPhone": "13800138000",
                "configuration": "{\"operatingHours\":\"06:00-22:00\",\"temperatureMonitoring\":true}"
              }')
            
            CREATE_WH_STATUS=$(echo "$CREATE_WAREHOUSE_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
            echo "创建仓库状态: $CREATE_WH_STATUS"
        fi
        
        # 创建区域
        CREATE_ZONE_RESPONSE=$(curl -s -X POST "http://************:5000/api/Zones" \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $TOKEN" \
          -w "HTTP_STATUS:%{http_code}" \
          -d '{
            "code": "TEST-ZONE",
            "name": "测试区域",
            "description": "用于测试的区域",
            "warehouseId": 1,
            "zoneType": 0,
            "status": 0,
            "area": 500.0,
            "volume": 1500.0,
            "maxWeight": 10000.0,
            "securityLevel": 1,
            "configuration": "{\"temperatureAlerts\":false}"
          }')
        
        CREATE_ZONE_STATUS=$(echo "$CREATE_ZONE_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
        echo "创建区域状态: $CREATE_ZONE_STATUS"
    fi
    
    # 创建货架
    CREATE_SHELF_RESPONSE=$(curl -s -X POST "http://************:5000/api/Shelves" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $TOKEN" \
      -w "HTTP_STATUS:%{http_code}" \
      -d '{
        "code": "TEST-SHELF",
        "name": "测试货架",
        "description": "用于测试检查功能的货架",
        "zoneId": 1,
        "shelfType": 1,
        "status": 1,
        "length": 2.0,
        "width": 1.0,
        "height": 2.0,
        "levels": 3,
        "positionsPerLevel": 2,
        "maxWeight": 1000.0,
        "maxWeightPerPosition": 50.0,
        "material": "钢制",
        "configuration": "{\"allowMixedSKU\":true}"
      }')
    
    CREATE_SHELF_STATUS=$(echo "$CREATE_SHELF_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
    CREATE_SHELF_BODY=$(echo "$CREATE_SHELF_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')
    echo "创建货架状态: $CREATE_SHELF_STATUS"
    echo "创建货架响应: $CREATE_SHELF_BODY"
fi

# 测试货架安全检查 - 使用最简单的JSON
echo -e "\n=== 货架安全检查测试（简化版） ==="
SIMPLE_INSPECTION_RESPONSE=$(curl -s -X POST "http://************:5000/api/Shelves/1/inspection" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -w "HTTP_STATUS:%{http_code}" \
  -d '{
    "inspectionType": "safety",
    "result": "passed",
    "inspectorName": "测试员"
  }')

SIMPLE_HTTP_STATUS=$(echo "$SIMPLE_INSPECTION_RESPONSE" | grep -o "HTTP_STATUS:[0-9]*" | cut -d: -f2)
SIMPLE_RESPONSE_BODY=$(echo "$SIMPLE_INSPECTION_RESPONSE" | sed 's/HTTP_STATUS:[0-9]*$//')

echo "HTTP状态码: $SIMPLE_HTTP_STATUS"
echo "响应内容: $SIMPLE_RESPONSE_BODY"

echo -e "\n=== 调试完成 ==="