using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WMS.API.Constants;
using WMS.API.Models;
using WMS.API.Services;

namespace WMS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = PolicyConstants.InventoryManagementPolicy)]
    public class OutboundController : ControllerBase
    {
        private readonly IOutboundService _outboundService;
        private readonly ILogger<OutboundController> _logger;

        public OutboundController(
            IOutboundService outboundService,
            ILogger<OutboundController> logger
        )
        {
            _outboundService = outboundService;
            _logger = logger;
        }

        /// <summary>
        /// 获取出库订单列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<OutboundOrder>>> GetOutboundOrders(
            [FromQuery] OutboundOrderStatus? status = null
        )
        {
            try
            {
                var orders = await _outboundService.GetOutboundOrdersAsync(status);
                return Ok(orders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving outbound orders");
                return StatusCode(500, "获取出库订单失败");
            }
        }

        /// <summary>
        /// 根据ID获取出库订单详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<OutboundOrder>> GetOutboundOrder(int id)
        {
            try
            {
                var order = await _outboundService.GetOutboundOrderByIdAsync(id);
                if (order == null)
                {
                    return NotFound($"出库订单 {id} 不存在");
                }

                return Ok(order);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving outbound order {OrderId}", id);
                return StatusCode(500, "获取出库订单详情失败");
            }
        }

        /// <summary>
        /// 根据订单号获取出库订单详情
        /// </summary>
        [HttpGet("by-number/{orderNumber}")]
        public async Task<ActionResult<OutboundOrder>> GetOutboundOrderByNumber(string orderNumber)
        {
            try
            {
                var order = await _outboundService.GetOutboundOrderByNumberAsync(orderNumber);
                if (order == null)
                {
                    return NotFound($"出库订单 {orderNumber} 不存在");
                }

                return Ok(order);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving outbound order {OrderNumber}", orderNumber);
                return StatusCode(500, "获取出库订单详情失败");
            }
        }

        /// <summary>
        /// 创建出库订单
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<OutboundOrder>> CreateOutboundOrder(
            [FromBody] OutboundOrderCreateRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var order = await _outboundService.CreateOutboundOrderAsync(request, currentUser);

                return CreatedAtAction(nameof(GetOutboundOrder), new { id = order.Id }, order);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating outbound order");
                return StatusCode(500, "创建出库订单失败");
            }
        }

        /// <summary>
        /// 向出库订单添加项目
        /// </summary>
        [HttpPost("{id}/items")]
        public async Task<ActionResult<OutboundOrderItem>> AddItemToOrder(
            int id,
            [FromBody] OutboundOrderItemRequest request
        )
        {
            try
            {
                var item = await _outboundService.AddItemToOrderAsync(id, request);
                return Ok(item);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to outbound order {OrderId}", id);
                return StatusCode(500, "添加订单项目失败");
            }
        }

        /// <summary>
        /// 开始拣选作业
        /// </summary>
        [HttpPost("{id}/start-picking")]
        public async Task<ActionResult> StartPicking(int id)
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _outboundService.StartPickingAsync(id, currentUser);

                if (!success)
                {
                    return BadRequest("开始拣选失败，可能是订单状态不正确或库存不足");
                }

                return Ok(new { message = "拣选作业已开始" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting picking for order {OrderId}", id);
                return StatusCode(500, "开始拣选作业失败");
            }
        }

        /// <summary>
        /// 完成出库订单
        /// </summary>
        [HttpPost("{id}/complete")]
        public async Task<ActionResult> CompleteOutboundOrder(int id)
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _outboundService.CompleteOutboundOrderAsync(id, currentUser);

                if (!success)
                {
                    return BadRequest("完成出库订单失败，可能是拣选任务未完成");
                }

                return Ok(new { message = "出库订单已完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing outbound order {OrderId}", id);
                return StatusCode(500, "完成出库订单失败");
            }
        }

        /// <summary>
        /// 更新出库订单状态
        /// </summary>
        [HttpPatch("{id}/status")]
        public async Task<ActionResult> UpdateOrderStatus(
            int id,
            [FromBody] UpdateOutboundOrderStatusRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _outboundService.UpdateOutboundOrderStatusAsync(
                    id,
                    request.Status,
                    currentUser
                );

                if (!success)
                {
                    return NotFound($"出库订单 {id} 不存在");
                }

                return Ok(new { message = "订单状态更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating outbound order {OrderId} status", id);
                return StatusCode(500, "更新订单状态失败");
            }
        }

        /// <summary>
        /// 取消出库订单
        /// </summary>
        [HttpPost("{id}/cancel")]
        public async Task<ActionResult> CancelOutboundOrder(
            int id,
            [FromBody] CancelOrderRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _outboundService.CancelOutboundOrderAsync(
                    id,
                    request.Reason,
                    currentUser
                );

                if (!success)
                {
                    return NotFound($"出库订单 {id} 不存在");
                }

                return Ok(new { message = "出库订单已取消" });
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling outbound order {OrderId}", id);
                return StatusCode(500, "取消出库订单失败");
            }
        }

        /// <summary>
        /// 生成出库订单号
        /// </summary>
        [HttpGet("generate-order-number")]
        public async Task<ActionResult<string>> GenerateOrderNumber()
        {
            try
            {
                var orderNumber = await _outboundService.GenerateOutboundOrderNumberAsync();
                return Ok(new { orderNumber });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating outbound order number");
                return StatusCode(500, "生成订单号失败");
            }
        }
    }

    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = PolicyConstants.InventoryManagementPolicy)]
    public class PickingController : ControllerBase
    {
        private readonly IOutboundService _outboundService;
        private readonly ILogger<PickingController> _logger;

        public PickingController(
            IOutboundService outboundService,
            ILogger<PickingController> logger
        )
        {
            _outboundService = outboundService;
            _logger = logger;
        }

        /// <summary>
        /// 获取拣选任务列表
        /// </summary>
        [HttpGet("tasks")]
        public async Task<ActionResult<IEnumerable<PickingTask>>> GetPickingTasks(
            [FromQuery] int? orderId = null,
            [FromQuery] PickingTaskStatus? status = null,
            [FromQuery] string? assignedTo = null
        )
        {
            try
            {
                var tasks = await _outboundService.GetPickingTasksAsync(
                    orderId,
                    status,
                    assignedTo
                );
                return Ok(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving picking tasks");
                return StatusCode(500, "获取拣选任务失败");
            }
        }

        /// <summary>
        /// 生成拣选任务
        /// </summary>
        [HttpPost("orders/{orderId}/generate-tasks")]
        public async Task<ActionResult<IEnumerable<PickingTask>>> GeneratePickingTasks(int orderId)
        {
            try
            {
                var tasks = await _outboundService.GeneratePickingTasksAsync(orderId);
                return Ok(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating picking tasks for order {OrderId}", orderId);
                return StatusCode(500, "生成拣选任务失败");
            }
        }

        /// <summary>
        /// 处理拣选任务
        /// </summary>
        [HttpPost("tasks/{taskId}/process")]
        public async Task<ActionResult> ProcessPickingTask(
            int taskId,
            [FromBody] PickingTaskProcessRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _outboundService.ProcessPickingTaskAsync(
                    taskId,
                    request,
                    currentUser
                );

                if (!success)
                {
                    return BadRequest("处理拣选任务失败，可能是库存不足或任务状态不正确");
                }

                return Ok(new { message = "拣选任务处理成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing picking task {TaskId}", taskId);
                return StatusCode(500, "处理拣选任务失败");
            }
        }

        /// <summary>
        /// 使用条码处理拣选任务
        /// </summary>
        [HttpPost("tasks/{taskId}/process-barcode")]
        public async Task<ActionResult> ProcessPickingWithBarcode(
            int taskId,
            [FromBody] BarcodePickingRequest request
        )
        {
            try
            {
                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _outboundService.ProcessPickingWithBarcodeAsync(
                    taskId,
                    request,
                    currentUser
                );

                if (!success)
                {
                    return BadRequest("条码拣选处理失败");
                }

                return Ok(new { message = "条码拣选处理成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing barcode picking for task {TaskId}", taskId);
                return StatusCode(500, "条码拣选处理失败");
            }
        }
    }

    // DTOs for controller
    public class UpdateOutboundOrderStatusRequest
    {
        public OutboundOrderStatus Status { get; set; }
    }
}
