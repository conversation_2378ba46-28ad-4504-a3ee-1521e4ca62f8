using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using WMS.API.Constants;
using WMS.API.Data;
using WMS.API.Models;
using WMS.API.Services;

namespace WMS.API.Controllers
{
    /// <summary>
    /// 储位管理接口
    /// 提供仓库储位的增删改查及灯光控制功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = PolicyConstants.StorageLocationManagementPolicy)]
    public class StorageLocationsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IESP32CommunicationService _communicationService;
        private readonly ILogger<StorageLocationsController> _logger;

        public StorageLocationsController(
            ApplicationDbContext context,
            IESP32CommunicationService communicationService,
            ILogger<StorageLocationsController> logger
        )
        {
            _context = context;
            _communicationService = communicationService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有储位列表
        /// </summary>
        /// <returns>储位列表，包含关联的控制器和库存信息</returns>
        [HttpGet]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<ActionResult<IEnumerable<StorageLocation>>> GetStorageLocations()
        {
            return await _context
                .StorageLocations.Include(s => s.ESP32Controller)
                .Include(s => s.Inventories)
                .Include(s => s.Shelf)
                .ThenInclude(sh => sh.Zone)
                .ThenInclude(z => z.Warehouse)
                .Include(s => s.Capabilities)
                .Include(s => s.Classifications)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取指定储位信息
        /// </summary>
        /// <param name="id">储位ID</param>
        /// <returns>储位详细信息，包含关联的控制器和库存信息</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<StorageLocation>> GetStorageLocation(int id)
        {
            try
            {
                var storageLocation = await _context
                    .StorageLocations.Include(s => s.ESP32Controller)
                    .Include(s => s.Inventories)
                    .ThenInclude(i => i.Material)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (storageLocation == null)
                {
                    return NotFound($"储位ID {id} 不存在");
                }

                return storageLocation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取储位信息时发生错误: {Id}", id);
                return StatusCode(500, $"获取储位信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据储位编码获取储位信息
        /// </summary>
        /// <param name="code">储位编码</param>
        /// <returns>储位详细信息</returns>
        [HttpGet("by-code/{code}")]
        public async Task<ActionResult<StorageLocation>> GetStorageLocationByCode(string code)
        {
            var storageLocation = await _context
                .StorageLocations.Include(s => s.ESP32Controller)
                .Include(s => s.Inventories)
                .ThenInclude(i => i.Material)
                .FirstOrDefaultAsync(s => s.Code == code);

            if (storageLocation == null)
            {
                return NotFound();
            }

            return storageLocation;
        }

        /// <summary>
        /// 创建新的储位
        /// </summary>
        /// <param name="storageLocation">储位信息</param>
        /// <returns>创建成功的储位信息</returns>
        [HttpPost]
        public async Task<ActionResult<StorageLocation>> CreateStorageLocation(
            StorageLocation storageLocation
        )
        {
            // 基本数据验证
            if (string.IsNullOrWhiteSpace(storageLocation.Code))
            {
                return BadRequest("储位编码不能为空");
            }

            // 检查储位编码是否已存在
            var codeExists = await _context.StorageLocations.AnyAsync(s =>
                s.Code == storageLocation.Code
            );
            if (codeExists)
            {
                return BadRequest($"储位编码 '{storageLocation.Code}' 已存在");
            }

            // 验证ESP32控制器是否存在
            var controllerExists = await _context.ESP32Controllers.AnyAsync(c =>
                c.Id == storageLocation.ESP32ControllerId
            );

            if (!controllerExists)
            {
                return BadRequest("ESP32 Controller not found");
            }

            // 验证LED位置参数
            if (storageLocation.StartLedPosition < 0 || storageLocation.EndLedPosition < 0)
            {
                return BadRequest("LED位置不能为负数");
            }

            if (storageLocation.StartLedPosition >= storageLocation.EndLedPosition)
            {
                return BadRequest("LED起始位置必须小于LED结束位置");
            }

            // 验证LED通道号
            if (storageLocation.LedChannel < 0 || storageLocation.LedChannel > 3)
            {
                return BadRequest("LED通道号必须在0-3之间");
            }

            storageLocation.CreatedAt = DateTime.UtcNow;
            storageLocation.UpdatedAt = DateTime.UtcNow;

            try
            {
                _context.StorageLocations.Add(storageLocation);
                await _context.SaveChangesAsync();

                return CreatedAtAction(
                    nameof(GetStorageLocation),
                    new { id = storageLocation.Id },
                    storageLocation
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建储位时发生错误: {Code}", storageLocation.Code);
                return StatusCode(500, "创建储位失败，请稍后重试");
            }
        }

        /// <summary>
        /// 更新储位信息
        /// </summary>
        /// <param name="id">储位ID</param>
        /// <param name="storageLocation">更新的储位数据</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateStorageLocation(
            int id,
            StorageLocation storageLocation
        )
        {
            if (id != storageLocation.Id)
            {
                return BadRequest("请求参数ID与数据中ID不一致");
            }

            var existingLocation = await _context.StorageLocations.FindAsync(id);
            if (existingLocation == null)
            {
                return NotFound("储位不存在");
            }

            // 基本数据验证
            if (string.IsNullOrWhiteSpace(storageLocation.Code))
            {
                return BadRequest("储位编码不能为空");
            }

            // 检查储位编码是否已存在（排除当前记录）
            var codeExists = await _context.StorageLocations.AnyAsync(s =>
                s.Code == storageLocation.Code && s.Id != id
            );
            if (codeExists)
            {
                return BadRequest($"储位编码 '{storageLocation.Code}' 已存在");
            }

            // 验证ESP32控制器是否存在
            var controllerExists = await _context.ESP32Controllers.AnyAsync(c =>
                c.Id == storageLocation.ESP32ControllerId
            );

            if (!controllerExists)
            {
                return BadRequest("ESP32 Controller not found");
            }

            // 验证LED位置参数
            if (storageLocation.StartLedPosition < 0 || storageLocation.EndLedPosition < 0)
            {
                return BadRequest("LED位置不能为负数");
            }

            if (storageLocation.StartLedPosition >= storageLocation.EndLedPosition)
            {
                return BadRequest("LED起始位置必须小于LED结束位置");
            }

            // 验证LED通道号
            if (storageLocation.LedChannel < 0 || storageLocation.LedChannel > 3)
            {
                return BadRequest("LED通道号必须在0-3之间");
            }

            existingLocation.Code = storageLocation.Code;
            existingLocation.Description = storageLocation.Description;
            existingLocation.ESP32ControllerId = storageLocation.ESP32ControllerId;
            existingLocation.StartLedPosition = storageLocation.StartLedPosition;
            existingLocation.EndLedPosition = storageLocation.EndLedPosition;
            existingLocation.LedChannel = storageLocation.LedChannel;
            existingLocation.Status = storageLocation.Status;
            existingLocation.Zone = storageLocation.Zone;
            existingLocation.Aisle = storageLocation.Aisle;
            existingLocation.ShelfCode = storageLocation.ShelfCode;
            existingLocation.Level = storageLocation.Level;
            existingLocation.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StorageLocationExists(id))
                {
                    return NotFound("储位不存在");
                }
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新储位时发生错误: {Id}", id);
                return StatusCode(500, "更新储位失败，请稍后重试");
            }
        }

        /// <summary>
        /// 删除储位
        /// </summary>
        /// <param name="id">储位ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteStorageLocation(int id)
        {
            var storageLocation = await _context.StorageLocations.FindAsync(id);
            if (storageLocation == null)
            {
                return NotFound();
            }

            // 检查是否有库存
            var hasInventory = await _context.Inventories.AnyAsync(i => i.StorageLocationId == id);

            if (hasInventory)
            {
                return BadRequest("Cannot delete storage location with existing inventory");
            }

            _context.StorageLocations.Remove(storageLocation);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// 测试储位的LED灯条显示
        /// </summary>
        /// <param name="id">储位ID</param>
        /// <param name="request">灯光测试参数</param>
        /// <returns>测试结果</returns>
        [HttpPost("{id}/test-light")]
        public async Task<ActionResult<object>> TestLight(
            int id,
            [FromBody] TestLightRequest request
        )
        {
            var storageLocation = await _context
                .StorageLocations.Include(s => s.ESP32Controller)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (storageLocation == null)
            {
                return NotFound();
            }

            var success = await _communicationService.SetLightAsync(
                storageLocation.ESP32Controller!,
                storageLocation.LedChannel,
                storageLocation.StartLedPosition,
                storageLocation.EndLedPosition,
                request.Color,
                request.Brightness
            );

            return Ok(
                new
                {
                    Success = success,
                    StorageLocationCode = storageLocation.Code,
                    ControllerName = storageLocation.ESP32Controller.Name,
                    Message = success ? "Light test successful" : "Light test failed",
                }
            );
        }

        /// <summary>
        /// 关闭储位的LED灯条
        /// </summary>
        /// <param name="id">储位ID</param>
        /// <returns>关灯结果</returns>
        [HttpPost("{id}/turn-off-light")]
        public async Task<ActionResult<object>> TurnOffLight(int id)
        {
            var storageLocation = await _context
                .StorageLocations.Include(s => s.ESP32Controller)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (storageLocation == null)
            {
                return NotFound();
            }

            // 通过设置黑色和0亮度来关闭灯光
            var success = await _communicationService.SetLightAsync(
                storageLocation.ESP32Controller!,
                storageLocation.LedChannel,
                storageLocation.StartLedPosition,
                storageLocation.EndLedPosition,
                "black",
                0
            );

            return Ok(
                new
                {
                    Success = success,
                    StorageLocationCode = storageLocation.Code,
                    Message = success
                        ? "Light turned off successfully"
                        : "Failed to turn off light",
                }
            );
        }

        /// <summary>
        /// 获取所有可用的储位列表
        /// </summary>
        /// <returns>可用储位列表</returns>
        [HttpGet("available")]
        public async Task<ActionResult<IEnumerable<StorageLocation>>> GetAvailableStorageLocations()
        {
            return await _context
                .StorageLocations.Include(s => s.ESP32Controller)
                .Where(s => s.Status == StorageLocationStatus.Available)
                .ToListAsync();
        }

        /// <summary>
        /// 下载储位批量导入模板
        /// </summary>
        /// <returns>Excel模板文件</returns>
        [HttpGet("template")]
        public ActionResult DownloadTemplate()
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("储位导入模板");

            // 设置表头
            worksheet.Cells[1, 1].Value = "储位编码*";
            worksheet.Cells[1, 2].Value = "描述";
            worksheet.Cells[1, 3].Value = "ESP32控制器ID*";
            worksheet.Cells[1, 4].Value = "LED起始位置*";
            worksheet.Cells[1, 5].Value = "LED结束位置*";
            worksheet.Cells[1, 6].Value = "LED通道号*";
            worksheet.Cells[1, 7].Value = "状态";
            worksheet.Cells[1, 8].Value = "区域代码";
            worksheet.Cells[1, 9].Value = "巷道编号";
            worksheet.Cells[1, 10].Value = "货架编号";
            worksheet.Cells[1, 11].Value = "层级编号";

            // 设置表头样式
            using (var range = worksheet.Cells[1, 1, 1, 11])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
            }

            // 添加示例数据
            worksheet.Cells[2, 1].Value = "A01-01-01";
            worksheet.Cells[2, 2].Value = "示例储位描述";
            worksheet.Cells[2, 3].Value = 1;
            worksheet.Cells[2, 4].Value = 0;
            worksheet.Cells[2, 5].Value = 10;
            worksheet.Cells[2, 6].Value = 0;
            worksheet.Cells[2, 7].Value = "Available";
            worksheet.Cells[2, 8].Value = "A区";
            worksheet.Cells[2, 9].Value = "01";
            worksheet.Cells[2, 10].Value = "01";
            worksheet.Cells[2, 11].Value = "01";

            // 在右侧空白列添加说明
            worksheet.Cells[1, 13].Value = "填写说明：";
            worksheet.Cells[2, 13].Value = "1. 带*号的字段为必填项";
            worksheet.Cells[3, 13].Value = "2. ESP32控制器ID必须是系统中已存在的控制器";
            worksheet.Cells[4, 13].Value = "3. LED通道号范围：0-3";
            worksheet.Cells[5, 13].Value = "4. 状态可选值：Available, Locked, Maintenance";
            worksheet.Cells[6, 13].Value = "5. LED起始位置必须小于结束位置";
            worksheet.Cells[7, 13].Value = "6. 请从第3行开始填写数据，第2行为示例";
            worksheet.Cells[8, 13].Value = "7. 删除示例数据后再导入";

            // 设置说明列样式
            using (var range = worksheet.Cells[1, 13, 8, 13])
            {
                range.Style.Font.Color.SetColor(System.Drawing.Color.Gray);
                range.Style.Font.Size = 10;
            }

            // 设置说明标题样式
            worksheet.Cells[1, 13].Style.Font.Bold = true;
            worksheet.Cells[1, 13].Style.Font.Color.SetColor(System.Drawing.Color.DarkBlue);

            // 自动调整列宽
            worksheet.Cells.AutoFitColumns();

            var content = package.GetAsByteArray();
            return File(
                content,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "储位导入模板.xlsx"
            );
        }

        /// <summary>
        /// 批量导入储位
        /// </summary>
        /// <param name="file">Excel文件</param>
        /// <returns>导入结果</returns>
        [HttpPost("batch-import")]
        public async Task<ActionResult<BatchImportResult>> BatchImport(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("请选择要导入的Excel文件");
            }

            if (!file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest("只支持.xlsx格式的Excel文件");
            }

            var result = new BatchImportResult();

            try
            {
                using var stream = file.OpenReadStream();
                using var package = new ExcelPackage(stream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    return BadRequest("Excel文件中没有找到工作表");
                }

                // 获取数据行数
                var rowCount = worksheet.Dimension?.Rows ?? 0;
                if (rowCount <= 1)
                {
                    return BadRequest("Excel文件中没有数据行");
                }

                // 预先获取所有控制器ID用于验证
                var existingControllerIds = await _context
                    .ESP32Controllers.Select(c => c.Id)
                    .ToHashSetAsync();

                // 获取已存在的储位编码
                var existingCodes = await _context
                    .StorageLocations.Select(s => s.Code)
                    .ToHashSetAsync();

                var storageLocationsToAdd = new List<StorageLocation>();

                // 从第2行开始读取数据（第1行是表头）
                for (int row = 2; row <= rowCount; row++)
                {
                    try
                    {
                        var storageLocation = await ParseStorageLocationFromRow(
                            worksheet,
                            row,
                            existingControllerIds,
                            existingCodes
                        );
                        if (storageLocation != null)
                        {
                            storageLocationsToAdd.Add(storageLocation);
                            existingCodes.Add(storageLocation.Code); // 防止同一批次中重复
                            result.SuccessCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"第{row}行: {ex.Message}");
                        result.ErrorCount++;
                    }
                }

                // 批量保存
                if (storageLocationsToAdd.Any())
                {
                    _context.StorageLocations.AddRange(storageLocationsToAdd);
                    await _context.SaveChangesAsync();
                }

                result.TotalCount = rowCount - 1; // 减去表头行
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量导入储位时发生错误");
                return StatusCode(500, $"导入失败: {ex.Message}");
            }
        }

        private async Task<StorageLocation?> ParseStorageLocationFromRow(
            ExcelWorksheet worksheet,
            int row,
            HashSet<int> existingControllerIds,
            HashSet<string> existingCodes
        )
        {
            // 读取必填字段
            var code = worksheet.Cells[row, 1].Text?.Trim();
            if (string.IsNullOrWhiteSpace(code))
            {
                throw new ValidationException("储位编码不能为空");
            }

            if (code.Length > 50)
            {
                throw new ValidationException("储位编码长度不能超过50个字符");
            }

            if (existingCodes.Contains(code))
            {
                throw new ValidationException($"储位编码 '{code}' 已存在");
            }

            // ESP32控制器ID
            if (!int.TryParse(worksheet.Cells[row, 3].Text, out int controllerId))
            {
                throw new ValidationException("ESP32控制器ID必须是数字");
            }

            if (!existingControllerIds.Contains(controllerId))
            {
                throw new ValidationException($"ESP32控制器ID {controllerId} 不存在");
            }

            // LED位置
            if (!int.TryParse(worksheet.Cells[row, 4].Text, out int startLedPosition))
            {
                throw new ValidationException("LED起始位置必须是数字");
            }

            if (!int.TryParse(worksheet.Cells[row, 5].Text, out int endLedPosition))
            {
                throw new ValidationException("LED结束位置必须是数字");
            }

            if (startLedPosition < 0 || endLedPosition < 0)
            {
                throw new ValidationException("LED位置不能为负数");
            }

            if (startLedPosition >= endLedPosition)
            {
                throw new ValidationException("LED起始位置必须小于结束位置");
            }

            // LED通道号
            if (!int.TryParse(worksheet.Cells[row, 6].Text, out int ledChannel))
            {
                throw new ValidationException("LED通道号必须是数字");
            }

            if (ledChannel < 0 || ledChannel > 3)
            {
                throw new ValidationException("LED通道号必须在0-3之间");
            }

            // 状态（可选）
            var statusText = worksheet.Cells[row, 7].Text?.Trim();
            var status = StorageLocationStatus.Available;
            if (!string.IsNullOrWhiteSpace(statusText))
            {
                if (!Enum.TryParse<StorageLocationStatus>(statusText, true, out status))
                {
                    throw new ValidationException(
                        $"状态值 '{statusText}' 无效，可选值：Available, Locked, Maintenance"
                    );
                }
            }

            // 可选字段
            var description = worksheet.Cells[row, 2].Text?.Trim();
            if (!string.IsNullOrWhiteSpace(description) && description.Length > 200)
            {
                throw new ValidationException("描述长度不能超过200个字符");
            }

            var zone = worksheet.Cells[row, 8].Text?.Trim();
            if (!string.IsNullOrWhiteSpace(zone) && zone.Length > 20)
            {
                throw new ValidationException("区域代码长度不能超过20个字符");
            }

            var aisle = worksheet.Cells[row, 9].Text?.Trim();
            if (!string.IsNullOrWhiteSpace(aisle) && aisle.Length > 20)
            {
                throw new ValidationException("巷道编号长度不能超过20个字符");
            }

            var shelf = worksheet.Cells[row, 10].Text?.Trim();
            if (!string.IsNullOrWhiteSpace(shelf) && shelf.Length > 20)
            {
                throw new ValidationException("货架编号长度不能超过20个字符");
            }

            var level = worksheet.Cells[row, 11].Text?.Trim();
            if (!string.IsNullOrWhiteSpace(level) && level.Length > 20)
            {
                throw new ValidationException("层级编号长度不能超过20个字符");
            }

            return new StorageLocation
            {
                Code = code,
                Description = string.IsNullOrWhiteSpace(description) ? null : description,
                ESP32ControllerId = controllerId,
                StartLedPosition = startLedPosition,
                EndLedPosition = endLedPosition,
                LedChannel = ledChannel,
                Status = status,
                Zone = string.IsNullOrWhiteSpace(zone) ? null : zone,
                Aisle = string.IsNullOrWhiteSpace(aisle) ? null : aisle,
                ShelfCode = string.IsNullOrWhiteSpace(shelf) ? null : shelf,
                Level = string.IsNullOrWhiteSpace(level) ? null : level,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
            };
        }

        #region 层级结构相关API

        /// <summary>
        /// 根据货架ID获取储位列表
        /// </summary>
        /// <param name="shelfId">货架ID</param>
        /// <returns>指定货架的储位列表</returns>
        [HttpGet("by-shelf/{shelfId}")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<ActionResult<IEnumerable<StorageLocation>>> GetStorageLocationsByShelf(
            int shelfId
        )
        {
            try
            {
                var storageLocations = await _context
                    .StorageLocations.Include(s => s.ESP32Controller)
                    .Include(s => s.Capabilities)
                    .Include(s => s.Classifications)
                    .Where(s => s.ShelfId == shelfId)
                    .OrderBy(s => s.Code)
                    .ToListAsync();

                _logger.LogInformation(
                    "Retrieved {Count} storage locations for shelf {ShelfId}",
                    storageLocations.Count,
                    shelfId
                );
                return Ok(storageLocations);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving storage locations for shelf {ShelfId}",
                    shelfId
                );
                return StatusCode(500, "Internal server error while retrieving storage locations");
            }
        }

        #endregion

        #region 储位能力管理API

        /// <summary>
        /// 为储位添加能力
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <param name="capability">能力信息</param>
        /// <returns>创建的能力</returns>
        [HttpPost("{storageLocationId}/capabilities")]
        public async Task<ActionResult<LocationCapability>> AddLocationCapability(
            int storageLocationId,
            LocationCapability capability
        )
        {
            try
            {
                // 检查储位是否存在
                var storageLocation = await _context.StorageLocations.FindAsync(storageLocationId);
                if (storageLocation == null)
                {
                    return NotFound($"Storage location with ID {storageLocationId} not found");
                }

                // 检查该储位是否已有相同类型的能力
                var existingCapability = await _context.LocationCapabilities.FirstOrDefaultAsync(
                    c =>
                        c.StorageLocationId == storageLocationId
                        && c.CapabilityType == capability.CapabilityType
                );

                if (existingCapability != null)
                {
                    return BadRequest(
                        $"Storage location already has capability of type {capability.CapabilityType}"
                    );
                }

                capability.StorageLocationId = storageLocationId;
                capability.CreatedAt = DateTime.UtcNow;
                capability.UpdatedAt = DateTime.UtcNow;

                _context.LocationCapabilities.Add(capability);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Added capability {Type} to storage location {Id}",
                    capability.CapabilityType,
                    storageLocationId
                );

                return CreatedAtAction(
                    nameof(GetLocationCapabilities),
                    new { storageLocationId = storageLocationId },
                    capability
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error adding capability to storage location {Id}",
                    storageLocationId
                );
                return StatusCode(500, "Internal server error while adding capability");
            }
        }

        /// <summary>
        /// 获取储位的所有能力
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <returns>储位能力列表</returns>
        [HttpGet("{storageLocationId}/capabilities")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<ActionResult<IEnumerable<LocationCapability>>> GetLocationCapabilities(
            int storageLocationId
        )
        {
            try
            {
                var capabilities = await _context
                    .LocationCapabilities.Where(c => c.StorageLocationId == storageLocationId)
                    .OrderBy(c => c.Priority)
                    .ToListAsync();

                return Ok(capabilities);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving capabilities for storage location {Id}",
                    storageLocationId
                );
                return StatusCode(500, "Internal server error while retrieving capabilities");
            }
        }

        /// <summary>
        /// 根据能力类型查询储位
        /// </summary>
        /// <param name="capabilityType">能力类型</param>
        /// <param name="warehouseId">仓库ID（可选）</param>
        /// <returns>具有指定能力的储位列表</returns>
        [HttpGet("by-capability/{capabilityType}")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<
            ActionResult<IEnumerable<StorageLocation>>
        > GetStorageLocationsByCapability(CapabilityType capabilityType, int? warehouseId = null)
        {
            try
            {
                var query = _context
                    .StorageLocations.Include(s => s.ESP32Controller)
                    .Include(s => s.Capabilities)
                    .Include(s => s.Shelf)
                    .ThenInclude(sh => sh.Zone)
                    .ThenInclude(z => z.Warehouse)
                    .Where(s =>
                        s.Capabilities.Any(c => c.CapabilityType == capabilityType && c.IsEnabled)
                    );

                if (warehouseId.HasValue)
                {
                    query = query.Where(s => s.Shelf.Zone.WarehouseId == warehouseId.Value);
                }

                var storageLocations = await query
                    .OrderBy(s => s.Shelf.Zone.WarehouseId)
                    .ThenBy(s => s.Shelf.ZoneId)
                    .ThenBy(s => s.ShelfId)
                    .ThenBy(s => s.Code)
                    .ToListAsync();

                _logger.LogInformation(
                    "Found {Count} storage locations with capability {Type}",
                    storageLocations.Count,
                    capabilityType
                );
                return Ok(storageLocations);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving storage locations by capability {Type}",
                    capabilityType
                );
                return StatusCode(500, "Internal server error while retrieving storage locations");
            }
        }

        /// <summary>
        /// 更新储位能力
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <param name="capabilityId">能力ID</param>
        /// <param name="capability">更新的能力信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{storageLocationId}/capabilities/{capabilityId}")]
        public async Task<IActionResult> UpdateLocationCapability(
            int storageLocationId,
            int capabilityId,
            LocationCapability capability
        )
        {
            if (capabilityId != capability.Id || storageLocationId != capability.StorageLocationId)
            {
                return BadRequest("ID mismatch");
            }

            try
            {
                var existingCapability = await _context.LocationCapabilities.FindAsync(
                    capabilityId
                );
                if (existingCapability == null)
                {
                    return NotFound($"Capability with ID {capabilityId} not found");
                }

                existingCapability.CapabilityLevel = capability.CapabilityLevel;
                existingCapability.CapabilityName = capability.CapabilityName;
                existingCapability.Description = capability.Description;
                existingCapability.Parameters = capability.Parameters;
                existingCapability.Configuration = capability.Configuration;
                existingCapability.IsEnabled = capability.IsEnabled;
                existingCapability.Priority = capability.Priority;
                existingCapability.ValidationRules = capability.ValidationRules;
                existingCapability.EffectiveFrom = capability.EffectiveFrom;
                existingCapability.EffectiveTo = capability.EffectiveTo;
                existingCapability.Certifications = capability.Certifications;
                existingCapability.LastVerificationDate = capability.LastVerificationDate;
                existingCapability.NextVerificationDate = capability.NextVerificationDate;
                existingCapability.Remarks = capability.Remarks;
                existingCapability.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Updated capability {Id} for storage location {StorageLocationId}",
                    capabilityId,
                    storageLocationId
                );
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating capability {Id}", capabilityId);
                return StatusCode(500, "Internal server error while updating capability");
            }
        }

        /// <summary>
        /// 删除储位能力
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <param name="capabilityId">能力ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{storageLocationId}/capabilities/{capabilityId}")]
        public async Task<IActionResult> DeleteLocationCapability(
            int storageLocationId,
            int capabilityId
        )
        {
            try
            {
                var capability = await _context.LocationCapabilities.FirstOrDefaultAsync(c =>
                    c.Id == capabilityId && c.StorageLocationId == storageLocationId
                );

                if (capability == null)
                {
                    return NotFound(
                        $"Capability with ID {capabilityId} not found for storage location {storageLocationId}"
                    );
                }

                _context.LocationCapabilities.Remove(capability);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Deleted capability {Id} from storage location {StorageLocationId}",
                    capabilityId,
                    storageLocationId
                );
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting capability {Id}", capabilityId);
                return StatusCode(500, "Internal server error while deleting capability");
            }
        }

        #endregion

        #region 储位分类管理API

        /// <summary>
        /// 为储位添加分类
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <param name="classification">分类信息</param>
        /// <returns>创建的分类</returns>
        [HttpPost("{storageLocationId}/classifications")]
        public async Task<ActionResult<LocationClassification>> AddLocationClassification(
            int storageLocationId,
            LocationClassification classification
        )
        {
            try
            {
                // 检查储位是否存在
                var storageLocation = await _context.StorageLocations.FindAsync(storageLocationId);
                if (storageLocation == null)
                {
                    return NotFound($"Storage location with ID {storageLocationId} not found");
                }

                // 检查该储位是否已有相同维度和类别的分类
                var existingClassification =
                    await _context.LocationClassifications.FirstOrDefaultAsync(c =>
                        c.StorageLocationId == storageLocationId
                        && c.Dimension == classification.Dimension
                        && c.Category == classification.Category
                    );

                if (existingClassification != null)
                {
                    return BadRequest(
                        $"Storage location already has classification for dimension {classification.Dimension} and category {classification.Category}"
                    );
                }

                classification.StorageLocationId = storageLocationId;
                classification.CreatedAt = DateTime.UtcNow;
                classification.UpdatedAt = DateTime.UtcNow;

                _context.LocationClassifications.Add(classification);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Added classification {Category}:{Value} to storage location {Id}",
                    classification.Category,
                    classification.Value,
                    storageLocationId
                );

                return CreatedAtAction(
                    nameof(GetLocationClassifications),
                    new { storageLocationId = storageLocationId },
                    classification
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error adding classification to storage location {Id}",
                    storageLocationId
                );
                return StatusCode(500, "Internal server error while adding classification");
            }
        }

        /// <summary>
        /// 获取储位的所有分类
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <returns>储位分类列表</returns>
        [HttpGet("{storageLocationId}/classifications")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<
            ActionResult<IEnumerable<LocationClassification>>
        > GetLocationClassifications(int storageLocationId)
        {
            try
            {
                var classifications = await _context
                    .LocationClassifications.Where(c => c.StorageLocationId == storageLocationId)
                    .OrderBy(c => c.Dimension)
                    .ThenBy(c => c.Category)
                    .ToListAsync();

                return Ok(classifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving classifications for storage location {Id}",
                    storageLocationId
                );
                return StatusCode(500, "Internal server error while retrieving classifications");
            }
        }

        /// <summary>
        /// 根据分类查询储位
        /// </summary>
        /// <param name="category">分类类别</param>
        /// <param name="value">分类值</param>
        /// <returns>具有指定分类的储位列表</returns>
        [HttpGet("by-classification/{category}/{value}")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<
            ActionResult<IEnumerable<StorageLocation>>
        > GetStorageLocationsByClassification(string category, string value)
        {
            try
            {
                var storageLocations = await _context
                    .StorageLocations.Include(s => s.ESP32Controller)
                    .Include(s => s.Classifications)
                    .Include(s => s.Shelf)
                    .ThenInclude(sh => sh.Zone)
                    .ThenInclude(z => z.Warehouse)
                    .Where(s =>
                        s.Classifications.Any(c =>
                            c.Category == category && c.Value == value && c.IsEnabled
                        )
                    )
                    .OrderBy(s => s.Shelf.Zone.WarehouseId)
                    .ThenBy(s => s.Shelf.ZoneId)
                    .ThenBy(s => s.ShelfId)
                    .ThenBy(s => s.Code)
                    .ToListAsync();

                _logger.LogInformation(
                    "Found {Count} storage locations with classification {Category}:{Value}",
                    storageLocations.Count,
                    category,
                    value
                );
                return Ok(storageLocations);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving storage locations by classification {Category}:{Value}",
                    category,
                    value
                );
                return StatusCode(500, "Internal server error while retrieving storage locations");
            }
        }

        /// <summary>
        /// 更新储位分类
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <param name="classificationId">分类ID</param>
        /// <param name="classification">更新的分类信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{storageLocationId}/classifications/{classificationId}")]
        public async Task<IActionResult> UpdateLocationClassification(
            int storageLocationId,
            int classificationId,
            LocationClassification classification
        )
        {
            if (
                classificationId != classification.Id
                || storageLocationId != classification.StorageLocationId
            )
            {
                return BadRequest("ID mismatch");
            }

            try
            {
                var existingClassification = await _context.LocationClassifications.FindAsync(
                    classificationId
                );
                if (existingClassification == null)
                {
                    return NotFound($"Classification with ID {classificationId} not found");
                }

                existingClassification.Dimension = classification.Dimension;
                existingClassification.Category = classification.Category;
                existingClassification.Value = classification.Value;
                existingClassification.DisplayName = classification.DisplayName;
                existingClassification.Description = classification.Description;
                existingClassification.Tags = classification.Tags;
                existingClassification.Properties = classification.Properties;
                existingClassification.Configuration = classification.Configuration;
                existingClassification.Priority = classification.Priority;
                existingClassification.IsEnabled = classification.IsEnabled;
                existingClassification.IsAutoAssigned = classification.IsAutoAssigned;
                existingClassification.EffectiveFrom = classification.EffectiveFrom;
                existingClassification.EffectiveTo = classification.EffectiveTo;
                existingClassification.IsInherited = classification.IsInherited;
                existingClassification.InheritedFrom = classification.InheritedFrom;
                existingClassification.ValidationRules = classification.ValidationRules;
                existingClassification.BusinessRules = classification.BusinessRules;
                existingClassification.ExternalSystemRef = classification.ExternalSystemRef;
                existingClassification.Remarks = classification.Remarks;
                existingClassification.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Updated classification {Id} for storage location {StorageLocationId}",
                    classificationId,
                    storageLocationId
                );
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating classification {Id}", classificationId);
                return StatusCode(500, "Internal server error while updating classification");
            }
        }

        /// <summary>
        /// 删除储位分类
        /// </summary>
        /// <param name="storageLocationId">储位ID</param>
        /// <param name="classificationId">分类ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{storageLocationId}/classifications/{classificationId}")]
        public async Task<IActionResult> DeleteLocationClassification(
            int storageLocationId,
            int classificationId
        )
        {
            try
            {
                var classification = await _context.LocationClassifications.FirstOrDefaultAsync(c =>
                    c.Id == classificationId && c.StorageLocationId == storageLocationId
                );

                if (classification == null)
                {
                    return NotFound(
                        $"Classification with ID {classificationId} not found for storage location {storageLocationId}"
                    );
                }

                _context.LocationClassifications.Remove(classification);
                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "Deleted classification {Id} from storage location {StorageLocationId}",
                    classificationId,
                    storageLocationId
                );
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting classification {Id}", classificationId);
                return StatusCode(500, "Internal server error while deleting classification");
            }
        }

        #endregion

        #region 智能储位推荐API

        /// <summary>
        /// 智能储位推荐
        /// </summary>
        /// <param name="request">推荐请求参数</param>
        /// <returns>推荐的储位列表</returns>
        [HttpPost("recommend")]
        [Authorize(Policy = PolicyConstants.ReadOnlyPolicy)]
        public async Task<ActionResult<IEnumerable<StorageLocation>>> RecommendStorageLocations(
            [FromBody] StorageLocationRecommendationRequest request
        )
        {
            try
            {
                var query = _context
                    .StorageLocations.Include(s => s.ESP32Controller)
                    .Include(s => s.Capabilities)
                    .Include(s => s.Classifications)
                    .Include(s => s.Shelf)
                    .ThenInclude(sh => sh.Zone)
                    .ThenInclude(z => z.Warehouse)
                    .Where(s => s.Status == StorageLocationStatus.Available);

                // 根据材料类型过滤
                if (!string.IsNullOrEmpty(request.MaterialType))
                {
                    query = query.Where(s =>
                        s.Classifications.Any(c =>
                            c.Category == "MaterialType"
                            && c.Value == request.MaterialType
                            && c.IsEnabled
                        )
                    );
                }

                // 根据温度要求过滤
                if (!string.IsNullOrEmpty(request.TemperatureRequirement))
                {
                    if (request.TemperatureRequirement == "Refrigerated")
                    {
                        query = query.Where(s =>
                            s.Capabilities.Any(c =>
                                c.CapabilityType == CapabilityType.Temperature && c.IsEnabled
                            )
                        );
                    }
                }

                // 根据特殊要求过滤
                if (request.SpecialRequirements != null && request.SpecialRequirements.Any())
                {
                    foreach (var requirement in request.SpecialRequirements)
                    {
                        query = query.Where(s =>
                            s.Classifications.Any(c => c.Tags.Contains(requirement))
                        );
                    }
                }

                // 根据重量限制过滤
                if (request.Weight.HasValue)
                {
                    query = query.Where(s =>
                        !s.MaxWeight.HasValue || s.MaxWeight >= request.Weight.Value
                    );
                }

                // 优先选择指定区域
                var recommendations = await query.ToListAsync();

                if (!string.IsNullOrEmpty(request.PreferredZone))
                {
                    recommendations = recommendations
                        .OrderByDescending(s => s.Shelf.Zone.Code == request.PreferredZone)
                        .ThenBy(s => s.Code)
                        .Take(10)
                        .ToList();
                }
                else
                {
                    recommendations = recommendations
                        .OrderBy(s => s.Shelf.Zone.WarehouseId)
                        .ThenBy(s => s.Shelf.ZoneId)
                        .ThenBy(s => s.Code)
                        .Take(10)
                        .ToList();
                }

                _logger.LogInformation(
                    "Recommended {Count} storage locations for material type {MaterialType}",
                    recommendations.Count,
                    request.MaterialType
                );
                return Ok(recommendations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating storage location recommendations");
                return StatusCode(500, "Internal server error while generating recommendations");
            }
        }

        #endregion

        private bool StorageLocationExists(int id)
        {
            return _context.StorageLocations.Any(e => e.Id == id);
        }
    }

    /// <summary>
    /// 灯光测试请求参数
    /// </summary>
    public class TestLightRequest
    {
        /// <summary>
        /// LED颜色（如：red, green, blue, yellow等）
        /// </summary>
        public string Color { get; set; } = "yellow";

        /// <summary>
        /// LED亮度（0-255）
        /// </summary>
        public int Brightness { get; set; } = 255;
    }

    /// <summary>
    /// 批量导入结果
    /// </summary>
    public class BatchImportResult
    {
        /// <summary>
        /// 总处理记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功导入记录数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 错误记录数
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 错误详情列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 是否全部成功
        /// </summary>
        public bool IsAllSuccess => ErrorCount == 0;
    }
}
