using Microsoft.EntityFrameworkCore;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Services
{
    public interface IESP32HealthCheckService
    {
        Task<bool> CheckControllerHealthAsync(int controllerId);
        Task CheckAllControllersHealthAsync();
        Task<Dictionary<int, bool>> GetControllersHealthStatusAsync();
    }

    public class ESP32HealthCheckService : IESP32HealthCheckService
    {
        private readonly ApplicationDbContext _context;
        private readonly IESP32CommunicationService _communicationService;
        private readonly ILogger<ESP32HealthCheckService> _logger;

        public ESP32HealthCheckService(
            ApplicationDbContext context,
            IESP32CommunicationService communicationService,
            ILogger<ESP32HealthCheckService> logger
        )
        {
            _context = context;
            _communicationService = communicationService;
            _logger = logger;
        }

        public async Task<bool> CheckControllerHealthAsync(int controllerId)
        {
            try
            {
                var controller = await _context.ESP32Controllers.FindAsync(controllerId);
                if (controller == null)
                {
                    _logger.LogWarning(
                        "ESP32 controller with ID {ControllerId} not found",
                        controllerId
                    );
                    return false;
                }

                var isOnline = await _communicationService.TestConnectionAsync(controller);

                // 更新控制器状态
                controller.IsOnline = isOnline;
                controller.LastHeartbeat = DateTime.UtcNow;
                controller.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation(
                    "ESP32 controller {ControllerId} health check completed. Status: {Status}",
                    controllerId,
                    isOnline ? "Online" : "Offline"
                );

                return isOnline;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error checking health of ESP32 controller {ControllerId}",
                    controllerId
                );
                return false;
            }
        }

        public async Task CheckAllControllersHealthAsync()
        {
            try
            {
                var controllers = await _context.ESP32Controllers.ToListAsync();

                var healthCheckTasks = controllers.Select(async controller =>
                {
                    var isOnline = await _communicationService.TestConnectionAsync(controller);

                    controller.IsOnline = isOnline;
                    controller.LastHeartbeat = DateTime.UtcNow;
                    controller.UpdatedAt = DateTime.UtcNow;

                    return new { Controller = controller, IsOnline = isOnline };
                });

                var results = await Task.WhenAll(healthCheckTasks);

                await _context.SaveChangesAsync();

                var onlineCount = results.Count(r => r.IsOnline);
                var totalCount = results.Length;

                _logger.LogInformation(
                    "Health check completed for all ESP32 controllers. Online: {OnlineCount}/{TotalCount}",
                    onlineCount,
                    totalCount
                );

                foreach (var result in results.Where(r => !r.IsOnline))
                {
                    _logger.LogWarning(
                        "ESP32 controller {ControllerId} ({Name}) is offline",
                        result.Controller.Id,
                        result.Controller.Name
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during health check of all ESP32 controllers");
            }
        }

        public async Task<Dictionary<int, bool>> GetControllersHealthStatusAsync()
        {
            try
            {
                var controllers = await _context
                    .ESP32Controllers.Select(c => new { c.Id, c.IsOnline })
                    .ToListAsync();

                return controllers.ToDictionary(c => c.Id, c => c.IsOnline);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting controllers health status");
                return new Dictionary<int, bool>();
            }
        }
    }
}
