using Microsoft.AspNetCore.Identity;
using WMS.API.Constants;
using WMS.API.Data;
using WMS.API.Models;

namespace WMS.API.Services
{
    /// <summary>
    /// 数据种子服务实现
    /// 负责初始化系统默认数据
    /// </summary>
    public class DataSeedService : IDataSeedService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DataSeedService> _logger;
        private readonly ApplicationDbContext _context;

        public DataSeedService(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            IConfiguration configuration,
            ILogger<DataSeedService> logger,
            ApplicationDbContext context
        )
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _configuration = configuration;
            _logger = logger;
            _context = context;
        }

        public async Task SeedAsync()
        {
            try
            {
                _logger.LogInformation("开始初始化系统种子数据...");

                await SeedRolesAsync();
                await SeedSuperAdminAsync();
                
                // 初始化条码解析相关数据
                var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
                var barcodeLogger = loggerFactory.CreateLogger<BarcodeSeedService>();
                var barcodeSeedService = new BarcodeSeedService(_context, barcodeLogger);
                await barcodeSeedService.SeedBarcodeDataAsync();

                _logger.LogInformation("系统种子数据初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统种子数据初始化失败");
                throw;
            }
        }

        public async Task SeedRolesAsync()
        {
            _logger.LogInformation("开始初始化系统角色...");

            foreach (var roleName in RoleConstants.AllRoles)
            {
                var roleExists = await _roleManager.RoleExistsAsync(roleName);
                if (!roleExists)
                {
                    var role = new IdentityRole(roleName);
                    var result = await _roleManager.CreateAsync(role);

                    if (result.Succeeded)
                    {
                        _logger.LogInformation("角色 {RoleName} 创建成功", roleName);
                    }
                    else
                    {
                        _logger.LogError(
                            "角色 {RoleName} 创建失败: {Errors}",
                            roleName,
                            string.Join(", ", result.Errors.Select(e => e.Description))
                        );
                    }
                }
                else
                {
                    _logger.LogInformation("角色 {RoleName} 已存在", roleName);
                }
            }

            _logger.LogInformation("系统角色初始化完成");
        }

        public async Task SeedSuperAdminAsync()
        {
            _logger.LogInformation("开始初始化超级管理员账户...");

            // 从配置文件获取超级管理员信息
            var superAdminEmail = _configuration["DefaultSuperAdmin:Email"] ?? "<EMAIL>";
            var superAdminPassword = _configuration["DefaultSuperAdmin:Password"] ?? "Admin@123456";
            var superAdminFullName = _configuration["DefaultSuperAdmin:FullName"] ?? "系统管理员";

            // 检查超级管理员是否已存在
            var existingAdmin = await _userManager.FindByEmailAsync(superAdminEmail);
            if (existingAdmin != null)
            {
                _logger.LogInformation("超级管理员账户已存在: {Email}", superAdminEmail);

                // 确保超级管理员拥有正确的角色
                var isInRole = await _userManager.IsInRoleAsync(
                    existingAdmin,
                    RoleConstants.SuperAdmin
                );
                if (!isInRole)
                {
                    await _userManager.AddToRoleAsync(existingAdmin, RoleConstants.SuperAdmin);
                    _logger.LogInformation(
                        "为现有超级管理员添加角色: {Role}",
                        RoleConstants.SuperAdmin
                    );
                }

                return;
            }

            // 创建超级管理员账户
            var superAdmin = new ApplicationUser
            {
                UserName = superAdminEmail,
                Email = superAdminEmail,
                FullName = superAdminFullName,
                EmployeeId = "ADMIN001",
                Department = "IT部门",
                Position = "系统管理员",
                EmailConfirmed = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
            };

            var createResult = await _userManager.CreateAsync(superAdmin, superAdminPassword);

            if (createResult.Succeeded)
            {
                // 添加超级管理员角色
                var addRoleResult = await _userManager.AddToRoleAsync(
                    superAdmin,
                    RoleConstants.SuperAdmin
                );

                if (addRoleResult.Succeeded)
                {
                    _logger.LogInformation("超级管理员账户创建成功: {Email}", superAdminEmail);
                    _logger.LogWarning(
                        "默认超级管理员密码: {Password} - 请及时修改!",
                        superAdminPassword
                    );
                }
                else
                {
                    _logger.LogError(
                        "超级管理员角色分配失败: {Errors}",
                        string.Join(", ", addRoleResult.Errors.Select(e => e.Description))
                    );
                }
            }
            else
            {
                _logger.LogError(
                    "超级管理员账户创建失败: {Errors}",
                    string.Join(", ", createResult.Errors.Select(e => e.Description))
                );
            }
        }
    }
}
