{"name": "wms-vgl-frontend", "version": "1.0.0", "description": "WMS-VGL Frontend - Visual Guided Logistics Warehouse Management System", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.5.0", "vue-router": "^4.4.0", "pinia": "^2.2.0", "axios": "^1.7.0", "ant-design-vue": "^4.2.6", "@ant-design/icons-vue": "^7.0.0", "dayjs": "^1.11.0", "lodash-es": "^4.17.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.0", "@tsconfig/node20": "^20.1.0", "@types/lodash-es": "^4.17.0", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "less": "^4.2.0", "npm-run-all2": "^6.2.0", "prettier": "^3.0.0", "typescript": "~5.4.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.3.0", "vue-tsc": "^2.0.0"}}